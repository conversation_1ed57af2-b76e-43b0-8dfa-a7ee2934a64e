import json
import urllib
from typing import Dict, Any, Optional, List, Union

import requests
import base64

from .base_api import BaseAPI
from datetime import datetime
from time import mktime

from ..models.store_data import get_session_context, Discount, ProductSku, Category


class MeituanAPI(BaseAPI):
    """美团外卖API客户端"""
    
    def __init__(self, cookies: Dict[str, str], sign_generator: callable, store_name: str = None):
        """
        初始化美团API客户端

        Args:
            cookies: 包含认证信息的cookie字典
            sign_generator: 签名生成函数
            store_name: 门店名称，用于错误提示
        """

        super().__init__(cookies, sign_generator)
        self.base_url = "https://shangoue.meituan.com"
        self.store_name = store_name or cookies.get('wmPoiId', '未知门店')
        self.params = {
            "yodaReady": "h5",
            "csecplatform": "4",
            "csecversion": "3.0.0"
        }
    
    # 商品分类管理API
    async def get_category_list(self) -> Dict[str, Any]:
        """获取商品分类列表"""
        url = f"{self.base_url}/reuse/sc/product/retail/r/tagList"
        data = {
            'wmPoiId': self.cookies.get('wmPoiId'),
            'needSmartSort': 'true'
        }
        return await self.post(url, data)
    
    async def save_category(self, tag_id: Optional[int] = None, tag_name: str = None) -> Dict[str, Any]:
        """
        新增或修改商品分类

        Args:
            category_info: 分类信息字典，包含以下字段:
                - id: str, 分类ID，新增时为空字符串，修改时为具体ID
                - name: str, 分类名称
                - description: str, 分类描述(可选)
                - level: int, 分类层级，1表示一级分类
                - parentId: int, 父分类ID，0表示顶级分类
                - top_flag: int, 置顶标记，0表示不置顶
                - topFlag: int, 置顶标记，0表示不置顶
                - isLeaf: int, 是否叶子节点，1表示是
                - time_zone: dict, 时间区间设置(可选)
                - topTimeZone: dict, 置顶时间区间设置(可选)

        Returns:
            Dict[str, Any]: API响应数据

        Raises:
            Exception: 当API请求失败时抛出异常
        """

        url = "https://shangoue.meituan.com/reuse/sc/product/food/w/saveWmProductTag"
        if tag_id is None:
            tag_id = ''
        # 构建请求数据
        data = {
            'wmPoiId': self.cookies.get('wmPoiId'),
            'tagInfo': json.dumps([{
                'id': tag_id,  # 新增时为空字符串
                'name': tag_name,
                'description': '',
                'level': 1,
                'parentId': 0,
                'top_flag': 0,
                'topFlag': 0,
                'isLeaf': 1,
                'time_zone':  {},
                'topTimeZone':  {}
            }])
        }

        self.logger.info(f"保存分类信息: {data.get('tagInfo')}")
        result = await self.post(url, data=data)
        if result and result.get('msg', '') == '成功':
            # 数据库新增该分类
            with get_session_context() as session:
                category = Category(
                    store_id=self.cookies.get('wmPoiId'),
                    category_id=result.get('data', {}),
                    name=tag_name,
                    parent_id=0,
                    productCount=0,
                    sequence=0
                )
                session.add(category)
                session.commit()
            return True
        return False
    
    async def update_category_sequence(self, tag_list: List[str]) -> Dict[str, Any]:
        """
        修改分类排序
        
        Args:
            tag_list: 分类ID列表，按照期望的顺序排列
        """
        url = f"{self.base_url}/reuse/sc/product/food/w/batchUpdateTagSequence"
        data = {
            'wmPoiId': self.cookies.get('wmPoiId'),
            'tagIds': ','.join(map(str, tag_list))  # 使用tagIds作为参数名，并用逗号连接ID列表
        }
        return await self.post(url, data, params=self.params)
    
    
    async def delete_category(self, tag_id: int) -> Dict[str, Any]:
        """
        删除商品分类
        
        Args:
            tag_id: 分类ID
        """
        data = {
            'wmPoiId': self.cookies.get('wmPoiId'), 
            'type': 2,
            'tagId': tag_id
        }
        url = f"{self.base_url}/reuse/sc/product/retail/w/tag/delete"
        result = await self.post(url, data) 
        if result and result.get('msg', '') == None:
            # 数据库删除该分类
            with get_session_context() as session:
                session.query(Category).filter_by(
                    store_id=self.cookies.get('wmPoiId'),
                    category_id=tag_id
                ).delete()
                session.commit()
            return True
        return False
    
    # 商品管理API
    async def get_product_list(self, page_num: int = 1, page_size: int = 20,
                        search_word: Optional[str] = None, tag_id: Optional[int] = None,
                        state: Optional[int] = 0) -> Dict[str, Any]:
        """
        获取商品列表
        
        Args:
            page_num: 页码，从1开始
            page_size: 每页数量
            search_word: 搜索关键词
            tag_id: 分类ID
            state: 0-全部 1-在售 2-已下架 3-售罄
        """
        url = f"{self.base_url}/reuse/sc/product/retail/r/searchListPage"
        data = {
            'wmPoiId': self.cookies.get('wmPoiId'),
            'pageNum': page_num,
            'pageSize': page_size,
            'needTag': 1,
            'needCombinationSpu': 2,
            'state': state,  # 全部商品
            'appType': 3,
            'saleStatus': 0
        }
        if search_word:
            data['searchWord'] = search_word
        if tag_id is not None:
            data['tagId'] = tag_id

        return await self.post(url, data)
    
    async def update_stock(self, sku_ids: Union[str, List[str]], stock: int) -> Dict[str, Any]:
        """
        修改商品库存
        
        Args:
            sku_ids: SKU ID或SKU ID列表
            stock: 库存数量
        """
        url = f"{self.base_url}/reuse/sc/product/retail/w/batchUpdateSkuStock"
        if isinstance(sku_ids, list):
            sku_ids = ','.join(map(str, sku_ids))

        wmPoiId = self.cookies.get('wmPoiId')
        return await self.post(url, {'skuIds': sku_ids, 'stock': stock,'editSource': 1,'packageConfirmFlag': 'false','wmPoiId':wmPoiId},params=self.params)
    
    async def update_price(self, sku_id: str, price: float) -> Dict[str, Any]:
        """
        修改商品原价
        
        Args:
            sku_id: SKU ID
            price: 商品原价
        """
        url = f"{self.base_url}/reuse/sc/product/retail/w/updatePrice"
        wmPoiId = self.cookies.get('wmPoiId')
        return await self.post(url, {'skuId': sku_id, 'price': price,'wmPoiId':wmPoiId,'editSource': 2})
    
    async def update_name(self, spu_id: str, spu_name: str) -> Dict[str, Any]:
        """
        修改商品名称
        
        Args:
            spu_id: 商品ID
            spu_name: 新的商品名称
            
        Returns:
            Dict[str, Any]: API响应数据
            
        Raises:
            Exception: 当API请求失败时抛出异常
        """
        url = f"{self.base_url}/reuse/sc/product/retail/w/updateSpuName"
        
        data = {
            'editSource': 1,
            'wmPoiId': self.cookies.get('wmPoiId'),
            'spuId': spu_id,
            'spuName': spu_name,
            'checkActivitySkuModify': True
        }
        
        self.logger.info(f"修改商品名称: spuId={spu_id}, spuName={spu_name}")
        self.logger.debug(f"请求数据: {data}")
        
        return await self.post(url, data=data, params=self.params)
    
    async def update_sell_status(self, spu_ids: Union[str, List[str]], 
                           sku_ids: Union[str, List[str]], sell_status: int) -> Dict[str, Any]:
        """
        修改商品上下架状态
        
        Args:
            spu_ids: SPU ID或SPU ID列表
            sku_ids: SKU ID或SKU ID列表
            sell_status: 销售状态，0-上架，1-下架
        """
        url = "https://shangoue.meituan.com/reuse/sc/product/retail/w/batchSetSellStatus"
        
        # 处理ID列表
        if isinstance(spu_ids, list):
            spu_ids = ','.join(map(str, spu_ids))
        if isinstance(sku_ids, list):
            sku_ids = ','.join(map(str, sku_ids))
        
        # 构建请求数据
        data = {
            'editSource': 1,
            'tagCat': 0,
            'spuIds': spu_ids,
            'skuIds': sku_ids,
            'opTab': 0,
            'wmPoiId': self.cookies.get('wmPoiId'),
            'sellstatus': sell_status,
            'packageConfirmFlag': 'false',
            'v2': 1,
            'viewStyle': 0
        }
        

        
        return await self.post(url, data=data, params=self.params)
    
    async def update_sequence(self, tag_id: int, spu_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        修改商品排序
        
        Args:
            tag_id: 分类ID
            spu_list: 商品列表，每个商品包含spuId和sequence字段
            
        Returns:
            Dict[str, Any]: API响应数据，格式如:
            {
                "code": 0,
                "msg": null
            }
            
        Raises:
            Exception: 当API请求失败时抛出异常
        """
        url = "https://shangoue.meituan.com/reuse/sc/product/food/w/batchUpdateSpuSequence"
        
        # 构建spuIdSeqMap参数
        spu_id_seq_map = {
            str(item['spuId']): item['sequence']
            for item in spu_list
        }
        
        # 构建请求数据
        data = {
            'wmPoiId': self.cookies.get('wmPoiId'),
            'tagId': tag_id,
            **{f'spuIdSeqMap[{k}]': v for k, v in spu_id_seq_map.items()}
        }
        
        self.logger.info(f"修改商品排序: tagId={tag_id}, spuList={spu_list}")
        self.logger.debug(f"请求数据: {data}")
        
        # 发送请求
        return await self.post(url, data=data, params=self.params)
    
    async def delete_product(self, sku_ids: Union[str, List[str]]) -> Dict[str, Any]:
        """
        删除商品
        
        Args:
            sku_ids: SKU ID或SKU ID列表
            
        Returns:
            Dict[str, Any]: API响应数据，格式如:
            {
                "msg": "删除成功",
                "code": 0,
                "data": null
            }
            
        Raises:
            Exception: 当API请求失败时抛出异常
        """
        url = "https://shangoue.meituan.com/reuse/sc/product/food/w/batchDelete"
        
        # 如果传入的是列表，将其转换为逗号分隔的字符串
        if isinstance(sku_ids, list):
            sku_ids = ','.join(map(str, sku_ids))
            
        # 构建请求数据
        data = {
            'skuIds': sku_ids,
            'opTab': 1,
            'tagCat': 0,
            'wmPoiId': self.cookies.get('wmPoiId'),
            'packageConfirmFlag': 'false',
            'v2': 1,
            'viewStyle': 0
        }
        
        self.logger.info(f"删除商品: skuIds={sku_ids}")
        self.logger.debug(f"请求数据: {data}")
        
        # 发送请求
        return await self.post(url, data=data, params=self.params)
    
    async def batch_update_product_category(self, sku_ids: List[str], tag_ids: List[int], op_type: int = 1) -> Dict[str, Any]:
        """
        批量更新商品分类
        
        Args:
            sku_ids: SKU ID列表
            tag_ids: 分类ID列表
            op_type: 操作类型，1-新增，2-替换，3-删除
            
        Returns:
            Dict[str, Any]: API响应数据，格式如:
            {
                "msg": null,
                "code": 0,
                "data": {
                    "msg": "本次共操作n商品，成功n，失败0",
                    "errorModels": []
                }
            }
            
        Raises:
            Exception: 当API请求失败时抛出异常
        """
        url = "https://shangoue.meituan.com/reuse/sc/product/retail/w/batchUpdateMultiTag"
        
        # 获取所有SKU对应的SPU ID
        spu_ids = []
        with get_session_context() as session:
            for sku_id in sku_ids:
                product_sku = session.query(ProductSku).filter_by(
                    store_id=self.cookies.get('wmPoiId'),
                    sku_id=sku_id
                ).first()
                
                if not product_sku:
                    raise Exception(f"未找到SKU ID {sku_id} 对应的商品信息")
                
                spu_ids.append(product_sku.product_id)
                # spu_ids去重
                spu_ids = list(set(spu_ids))
        # 构建请求数据
        data = {
            'tagCat': 0,
            'spuIds': ','.join(map(str, spu_ids)),
            'skuIds': ','.join(map(str, sku_ids)),
            'opTab': 0,
            'wmPoiId': self.cookies.get('wmPoiId'),
            'tagIds': ','.join(map(str, tag_ids)),
            'opType': op_type
        }
        
        self.logger.info(f"批量更新商品分类: skuIds={sku_ids}, spuIds={spu_ids}, tagIds={tag_ids}, opType={op_type}")
        self.logger.debug(f"请求数据: {data}")
        
        # 发送请求
        return await self.post(url, data=data, params=self.params)
    
    # 折扣管理API
    async def get_discount_list(self, page_no: int = 1, page_size: int = 10, search_params: Optional[Dict] = None) -> Dict[str, Any]:
       """
       获取折扣商品列表

       Args:
           page_no: int, 页码，从1开始
           page_size: int, 每页数量，默认10
           search_params: Optional[Dict], 搜索参数，可包含:
               - fuzzySkuName: str, 商品名称搜索关键词
               - skuIdList: List[str], SKU ID列表，用于精确搜索

       Returns:
           Dict[str, Any]: API响应数据，包含折扣商品列表
       """
       url = "https://waimaieapp.meituan.com/sg/business/item/activity/query/pageList"

       # 构建请求数据
       data = {
           "actType": 17,  # 固定值
           "page": {
               "pageNo": page_no,
               "pageSize": page_size
           },
           "queryReq": {
               "source": [-1],  # 全部来源
               "actStatus": 2  # 全部状态
           },
           "alonePoi": True,  # 固定值
           "loginPoiId": self.cookies.get('wmPoiId')
       }

       # 添加搜索参数
       if search_params:
           if "fuzzySkuName" in search_params:
               data["queryReq"]["fuzzySkuName"] = search_params["fuzzySkuName"]
           if "skuIdList" in search_params:
               data["queryReq"]["skuIdList"] = search_params["skuIdList"]

       self.logger.info(f"获取折扣商品列表: page={page_no}, size={page_size}")
       self.logger.debug(f"请求数据: {data}")

       # 发送POST请求，使用JSON格式
       return await self.post(url, data=data, contenttype='json')

    async def contentRiskCheck(self, content: str) -> Dict[str, Any]:
        """文本内容风险识别"""
        url = "https://waimaieapp.meituan.com/sg/business/activity/check/contentRiskCheck"
        data = [{
            "content": content,
            "fieldName": "actName"
        }]
        return await self.post(url, data=data, params=self.params,contenttype='json')

    def addproduct(self, skuName):
        try:
            url = "https://waimaieapp.meituan.com/reuse/activity/product/r/searchProductByCondition"
            skuName = skuName

            self.cookies.get('wmPoiId')

            wmPoiIds = self.cookies.get('wmPoiId')

            data = {
                'upcCodes': '',
                'sourceFoodCodes': '',
                'skuName': skuName,
                'wmPoiIds': wmPoiIds,
                'wmActType': 17,
                'withSpec': 'false',
            }
            response = requests.post(url, cookies=self.cookies, data=data).json()['data']
            return response
        except Exception as e:
            print(f"添加商品失败: {str(e)}")
            return None
    async def create_discount(self, discount_price,origin_price,itemName,spuId,wmSkuId):
        try:
            self.addproduct(itemName)
            discount_price = discount_price
            origin_price = origin_price
            itemName = itemName
            # 计算折扣
            discount = round(float(discount_price) / float(origin_price) * 10, 1)
            id = 0
            wmPoiId = self.cookies['wmPoiId']

            actInfo = {
                "discount": discount,
                "origin_price": origin_price,
                "act_price": discount_price
            }
            spuId = spuId
            wmSkuId = wmSkuId
            orderLimit = '-1'

            # 修改时间戳计算方式
            today = datetime.now().date()
            starttime = int(datetime.combine(today, datetime.min.time()).timestamp())
            endtime = starttime + 25920000  # 300天的秒数
            url = "https://waimaieapp.meituan.com/reuse/activity/setting/w/updateSpecialMealBase"
            headers = {
                "Content-Type": "application/x-www-form-urlencoded;charset=UTF-8"
            }
            payload = {
                "wmPoiId": wmPoiId,
                "poiPolicy": {
                    "online_pay": 0,
                    "foods": [
                        {
                            "foodKey": 1,
                            "id": id,
                            "wmPoiId": wmPoiId,
                            "actInfo": actInfo,
                            "period": "00:00-23:59",
                            "wmSkuId": wmSkuId,
                            "weeksTime": "1,2,3,4,5,6,7",
                            "startTime": starttime,
                            "endTime": endtime,
                            "orderPayType": 2,
                            "orderLimit": orderLimit,
                            "limitTimeSale": "999",
                            "itemName": itemName,
                            "todaySaleNum": 999,
                            "originId": 0,
                            "sortIndex": 0,
                            "settingType": "1",
                            "chargeType": "0",
                            "wmUserType": 0,
                            "poiUserType": "0",
                            "WmActPriceVo": {
                                "originPrice": origin_price,
                                "actPrice": str(discount_price),
                                "mtCharge": 0,
                                "agentCharge": 0,
                                "poiCharge": '{:.2f}'.format(float(origin_price) - float(discount_price)),
                                "chargeMethod": "0"
                            },
                            "autoDelayDays": 0,
                            "spuId": spuId,
                            "spec": '默认规格',
                            "priority": 0}
                    ]
                },
                "isAgree": 1,
                "wmActType": 17,

            }
            encoded_data = urllib.parse.urlencode(payload).encode('utf-8')
            response = requests.post(url, headers=headers, cookies = self.cookies,data=encoded_data).json()
            return response
        except Exception as e:
            return f'更改折扣失败,原因：{e}'
    
    async def update_discount(self, 
                            item_act_id: int,
                            act_price: float,
                            store_id: str,
                            order_limit_count: int = -1) -> Dict[str, Any]:
        """修改折扣商品信息"""
        url = "https://waimaieapp.meituan.com/sg/business/item/activity/batch/modify/saleFood"
        
        # 从cookies获取wmPoiId
        poi_id = int(self.cookies.get('wmPoiId'))
        # 在数据库中的discounts表获取actId
        # 修改数据库查询方式，确保在session作用域内完成所有操作
        actId = '0'
        try:
            with get_session_context() as session:
                discount = session.query(Discount).filter_by(
                    store_id=store_id,
                    itemact_id=item_act_id
                ).first()
                
                if discount:
                    actId = discount.actId
                    # 在session作用域内刷新对象
                    session.refresh(discount)
                
                # 确保在session作用域内提交
                session.commit()
        except Exception as e:
            self.logger.error(f"获取折扣信息失败: {str(e)}")
            # 如果查询失败，使用默认值继续
            actId = '0'
            
            
            
        
        if actId == '0':
            # 今天0点的10位时间戳
            startTime = int(mktime(datetime.now().replace(hour=0, minute=0, second=0, microsecond=0).timetuple()))
            #startTime+ 360天后的时间戳，结束时间必须是零点
            stopTime = startTime + 360 * 24 * 3600
            # 构建请求数据 - 修改数据类型和格式以匹配成功的curl请求
            data = {
                "actType": 17,
                "modifyItemList": [{
                    "itemActId": str(item_act_id),  # 转为字符串
                    "poiId": str(poi_id)  # 转为字符串
                }],
                "updateActInfo": False,  # 布尔值而不是字符串
                "actPrice": float(act_price),  # 确保是浮点数
                "orderLimit": order_limit_count != -1,  # 布尔值而不是字符串
                "orderLimitCount": int(order_limit_count) if order_limit_count != -1 else "-1",  # 字符串
                "dayInventory": False,  # 布尔值
                "autoDelayDays":30,  # 自动延期天数
                "dayInventory": False,  # 布尔值
                "dayInventoryCount": "-1",  # 字符串
                "discountType": 0,  # 折扣类型
                "startTime": startTime,  # 当前时间戳
                "stopTime": stopTime,  # 30天后的时间戳
                "time": "00:00-23:59",  # 时间区间
                "weeks": "1,2,3,4,5,6,7",  # 每天
                "updateActInfo": True,  # 更新活动信息
            }
        else:
            data = {
                "actType": 17,
                "modifyItemList": [{
                    "itemActId": str(item_act_id),  # 转为字符串
                    "poiId": str(poi_id)  # 转为字符串
                }],
                "updateActInfo": False,  # 布尔值而不是字符串
                "actPrice": float(act_price),  # 确保是浮点数
                "orderLimit": order_limit_count != -1,  # 布尔值而不是字符串
                "orderLimitCount": int(order_limit_count) if order_limit_count != -1 else "-1",  # 字符串
                "dayInventory": False,  # 布尔值
                "dayInventoryCount": "-1",  # 字符串
                "discountType": 0,  # 折扣类型
                "updateActInfo": False,  # 不更新活动信息
            }


        self.logger.info(f"修改折扣商品信息: itemActId={item_act_id}, actPrice={act_price}")
        self.logger.debug(f"请求数据: {data}")

        return await self.post(url, data=data, params=self.params,  contenttype='json')
    
    async def delete_discount(self, item_act_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        下线折扣商品
        
        Args:
            item_act_list: 折扣商品列表
        """
        url = f"{self.base_url}/sg/business/item/activity/batch/offline"
        return await self.post(url, {'actType': 17, 'itemActList': item_act_list})
    
    async def update_discount_sequence(self, item_act_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        修改折扣商品排序
        
        Args:
            item_act_list: 折扣商品列表，每个商品包含id和sequence字段
        """
        url = f"{self.base_url}/sg/business/item/activity/sequence/update"
        return await self.post(url, {'actType': 17, 'itemActList': item_act_list})
    
    async def change_product_status(self, product_id: str, new_status: int) -> Dict:
        """
        修改商品销售状态
        
        Args:
            product_id: 商品ID
            new_status: 新状态 0-下架 1-上架
        """
        url = f"{self.base_url}/reuse/sc/product/retail/w/updateSellStatus"
        data = {
            'wmPoiId': self.store_id,
            'spuId': product_id,
            'sellStatus': new_status
        }
        return await self._request('POST', url, data=data)
    
    async def cancel_discount(self, product_id: str) -> Dict:
        """
        取消商品折扣
        
        Args:
            product_id: 商品ID
        """
        url = f"{self.base_url}/reuse/sc/product/retail/w/cancelDiscount"
        data = {
            'wmPoiId': self.cookies.get('wmPoiId'),
            'spuId': product_id
        }
        return await self._request('POST', url, data=data)
    
    async def update_discount_sort(self, id: str, spu_id: str, wm_sku_id: str, sort_number: int) -> Dict:
        """
        修改商品折扣排序
        
        Args:
            id: 商品ID
            spu_id: 商品SPU ID
            wm_sku_id: 商品SKU ID
            sort_number: 新的排序号
            
        Returns:
            Dict: 包含响应数据的字典
            示例: {"code":0, "message":"成功", "data":{"code":0, "msg":null, "data":134102}}
        """
        url = "https://waimaieapp.meituan.com/sg/business/item/activity/updateSortIndex"
        
        params = {
            "yodaReady": "h5",
            "csecplatform": "4",
            "csecversion": "3.0.0"
        }
        
        # 构造form-urlencoded格式的数据
        sku_data = {
            "id": id,
            "spuId": spu_id,
            "wmSkuId": wm_sku_id,
            "wmPoiId": str(self.cookies.get('wmPoiId')),
            "sortNumber": sort_number
        }
        
        data = {
            "wmActType": "17",  # 转为字符串
            "skuSet": json.dumps([sku_data]),  # 将数组转为JSON字符串
            "opType": "7"  # 转为字符串
        }

        try:
            print("发送请求:")
            print(f"URL: {url}")
            print(f"参数: {params}")
            print(f"数据: {data}")
            
            response = await self.post(url, data=data, params=params)
            
            print(f"API响应: {response}")
            
            if response.get('code') != 0:
                error_msg = response.get('message', '未知错误')
                print(f"API错误: {error_msg}")
                raise Exception(f"API返回错误: {error_msg}")
            
            return response
            
        except Exception as e:
            print(f"请求异常: {str(e)}")
            raise Exception(f"修改失败: {str(e)}")

    async def update_product_images(self, spu_id: str, images: list):
        """更新商品图片
        Args:
            spu_id: 商品ID
            images: 图片URL列表
        """
        try:
            url = 'https://shangoue.meituan.com/reuse/sc/product/retail/w/picture'
            
            # 构建请求数据
            data = {
                'editSource': 1,
                'wmPoiId': self.cookies.get('wmPoiId'),
                'spuId': spu_id
            }
            
            # 添加图片数据
            for i, image_url in enumerate(images):
                if image_url:  # 只添加非空图片
                    data[f'pictures[{i}]'] = image_url

            # 发送请求
            response = await self._request(
                url,
                method='POST',
                data=data
            )

            return response

        except Exception as e:
            default_logger.error(f"更新美团商品图片失败: {e}")
            return None

    async def upload_image(self, image_data: bytes):
        """上传图片
        Args:
            image_data: 图片二进制数据
        """
        try:
            url = "https://shangoue.meituan.com/reuse/sc/product/uploadTool/w/uploadImg"
            
            # 将图片数据转换为base64
            encoded_string = base64.b64encode(image_data).decode('utf-8')
            encoded_string_data = f"data:image/jpeg;base64,{encoded_string}"
            
            data = {
                "multipart": encoded_string_data
            }

            # 发送请求
            response = await self._request(
                url,
                method='POST',
                data=data
            )

            if response.get('msg') == 'ok':
                return response.get('data', {}).get('url')
            
            return None

        except Exception as e:
            default_logger.error(f"上传美团图片失败: {e}")
            return None

    async def downloadPoiProductByExcel(self) -> Dict[str, Any]:
        """
        发起下载商品表格请求
        
        Returns:
            Dict[str, Any]: API响应数据
            
        Raises:
            Exception: 当API请求失败时抛出异常
        """
        url = "https://shangoue.meituan.com/reuse/sc/product/retail/r/downloadPoiProductByExcel"
        
        data = {
            'wmPoiId': self.cookies.get('wmPoiId')
        }
        
        self.logger.info(f"发起下载商品表格请求: wmPoiId={data['wmPoiId']}")
        
        return await self.post(url, data=data, params=self.params)
        
    async def task_r_list(self, page_num: int = 1, page_size: int = 10) -> List[Dict[str, Any]]:
        """
        获取商品表格请求下载链接列表
        
        Args:
            page_num: 页码，从1开始
            page_size: 每页数量，默认10
            
        Returns:
            List[Dict[str, Any]]: 下载任务列表，每个任务包含以下字段:
                - id: 任务ID
                - status: 任务状态 0-进行中 1-已完成
                - output: 下载链接
                - ctime: 创建时间
                - utime: 更新时间
                等
                
        Raises:
            Exception: 当API请求失败时抛出异常
        """
        url = "https://shangoue.meituan.com/reuse/sc/product/task/r/list"
        
        data = {
            'wmPoiId': self.cookies.get('wmPoiId'),
            'pageSize': page_size,
            'pageNum': page_num,
            'type': 6  # 固定值，表示下载店内商品
        }
        
        self.logger.info(f"获取商品表格下载任务列表: pageNum={page_num}, pageSize={page_size}")
        
        response = await self.post(url, data=data, params=self.params)
        return response.get('data', {}).get('data', [])