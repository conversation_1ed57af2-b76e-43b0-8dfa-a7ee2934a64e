@echo off
echo 正在复制CEF Python文件...

REM 创建目标目录
if not exist "dist\商店管理\_internal\cefpython3" mkdir "dist\商店管理\_internal\cefpython3"
if not exist "dist\商店管理\_internal\cefpython3\locales" mkdir "dist\商店管理\_internal\cefpython3\locales"
if not exist "dist\商店管理\_internal\cefpython3\swiftshader" mkdir "dist\商店管理\_internal\cefpython3\swiftshader"

REM 复制所有必要的文件
copy "venv\Lib\site-packages\cefpython3\*.dll" "dist\商店管理\_internal\cefpython3\" /Y
copy "venv\Lib\site-packages\cefpython3\*.pak" "dist\商店管理\_internal\cefpython3\" /Y
copy "venv\Lib\site-packages\cefpython3\*.dat" "dist\商店管理\_internal\cefpython3\" /Y
copy "venv\Lib\site-packages\cefpython3\*.bin" "dist\商店管理\_internal\cefpython3\" /Y
copy "venv\Lib\site-packages\cefpython3\*.exe" "dist\商店管理\_internal\cefpython3\" /Y
copy "venv\Lib\site-packages\cefpython3\locales\*" "dist\商店管理\_internal\cefpython3\locales\" /Y
copy "venv\Lib\site-packages\cefpython3\swiftshader\*" "dist\商店管理\_internal\cefpython3\swiftshader\" /Y

echo CEF Python文件复制完成！
