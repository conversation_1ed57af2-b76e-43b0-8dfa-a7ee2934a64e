#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from PyQt5.QtWidgets import QApplication
    from qfluentwidgets import Pivot
    
    # 创建应用程序实例（必需的）
    app = QApplication(sys.argv)
    
    print("✓ 成功导入 Pivot")
    
    # 创建一个实例来测试
    widget = Pivot()
    
    print("\n=== Pivot 的信号 ===")
    for attr in sorted(dir(widget)):
        if not attr.startswith('_'):
            try:
                attr_obj = getattr(widget, attr)
                if hasattr(attr_obj, 'connect'):
                    print(f"信号: {attr}")
            except Exception as e:
                print(f"错误获取 {attr}: {e}")
    
    # 检查是否有 currentItemChanged
    if hasattr(widget, 'currentItemChanged'):
        print(f"\n✓ Pivot 有 currentItemChanged 信号")
        try:
            def test_handler(*args):
                print(f"currentItemChanged 信号触发，参数: {args}")
            widget.currentItemChanged.connect(test_handler)
            print("✓ 成功连接 currentItemChanged 信号")
        except Exception as e:
            print(f"✗ 连接 currentItemChanged 信号失败: {e}")
    else:
        print(f"\n✗ Pivot 没有 currentItemChanged 信号")
    
    # 尝试添加项目并测试
    print(f"\n=== 测试添加项目到 Pivot ===")
    try:
        widget.addItem("test1", "测试1")
        widget.addItem("test2", "测试2")
        print("✓ 成功添加项目到 Pivot")
        
        # 再次检查信号
        if hasattr(widget, 'currentItemChanged'):
            print("✓ 添加项目后，Pivot 有 currentItemChanged 信号")
        else:
            print("✗ 添加项目后，Pivot 仍然没有 currentItemChanged 信号")
            
    except Exception as e:
        print(f"✗ 添加项目到 Pivot 失败: {e}")
    
except ImportError as e:
    print(f"✗ 导入失败: {e}")
except Exception as e:
    print(f"✗ 其他错误: {e}")

print("\n测试完成")
