#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最基本的CEF测试 - 确认CEF能否正常工作
"""

import sys
import os

# 添加Lib/site-packages到Python路径以支持CEF Python
lib_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'Lib', 'site-packages')
if lib_path not in sys.path:
    sys.path.insert(0, lib_path)

print("开始导入CEF...")

try:
    from cefpython3 import cefpython as cef
    print("CEF导入成功")
except Exception as e:
    print(f"CEF导入失败: {e}")
    sys.exit(1)

print("开始导入PyQt5...")

try:
    from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget
    from PyQt5.QtCore import QTimer
    print("PyQt5导入成功")
except Exception as e:
    print(f"PyQt5导入失败: {e}")
    sys.exit(1)

def main():
    print("开始初始化...")
    
    # 初始化CEF
    sys.excepthook = cef.ExceptHook
    
    settings = {
        "debug": True,
        "log_severity": cef.LOGSEVERITY_INFO,
        "log_file": "cef_debug.log",
        "multi_threaded_message_loop": False,
    }
    
    try:
        cef.Initialize(settings)
        print("CEF初始化成功")
    except Exception as e:
        print(f"CEF初始化失败: {e}")
        return
    
    # 创建Qt应用
    try:
        app = QApplication(sys.argv)
        print("Qt应用创建成功")
    except Exception as e:
        print(f"Qt应用创建失败: {e}")
        cef.Shutdown()
        return
    
    # 创建简单窗口
    try:
        window = QMainWindow()
        window.setWindowTitle("CEF基本测试")
        window.setGeometry(100, 100, 800, 600)
        window.show()
        print("窗口创建成功")
    except Exception as e:
        print(f"窗口创建失败: {e}")
        cef.Shutdown()
        return
    
    # CEF消息循环
    try:
        cef_timer = QTimer()
        cef_timer.timeout.connect(lambda: cef.MessageLoopWork())
        cef_timer.start(10)
        print("CEF消息循环启动成功")
    except Exception as e:
        print(f"CEF消息循环启动失败: {e}")
        cef.Shutdown()
        return
    
    print("开始运行应用...")
    
    # 运行应用
    try:
        app.exec_()
    except Exception as e:
        print(f"应用运行失败: {e}")
    finally:
        print("清理CEF...")
        cef.Shutdown()
        print("程序结束")

if __name__ == "__main__":
    main()
