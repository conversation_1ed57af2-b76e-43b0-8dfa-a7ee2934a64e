#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试认证错误处理
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from src.api.base_api import AuthenticationExpiredError
    print("✅ 成功导入AuthenticationExpiredError")
    
    # 测试异常创建
    error = AuthenticationExpiredError('美团', '星辰', '美团-星辰的cookies已失效')
    print(f"✅ 异常创建成功: {error.message}")
    print(f"   平台: {error.platform}")
    print(f"   门店: {error.store_name}")
    
except Exception as e:
    print(f"❌ 导入或测试失败: {e}")
    import traceback
    traceback.print_exc()

print("\n=== 修改总结 ===")
print("✅ 1. 添加了AuthenticationExpiredError自定义异常类")
print("✅ 2. 修改了美团API的_handle_response方法，对错误码1001进行专门处理")
print("✅ 3. 修改了饿了么客户端，移除自动登录弹窗，改为抛出认证过期异常")
print("✅ 4. 修改了数据同步服务，添加对认证过期异常的处理")
print("✅ 5. 修改了产品界面的错误处理，区分认证过期和其他错误")
print("✅ 6. 修改了批量操作的错误处理，支持跳过认证过期的门店")
print("✅ 7. 所有API初始化都传递了门店名称，确保错误信息准确")

print("\n=== 功能特点 ===")
print("🎯 1. 错误信息明确区分平台：'美团-[门店名]' vs '饿了么-[门店名]'")
print("🎯 2. 认证过期时使用InfoBar.warning而不是阻塞性对话框")
print("🎯 3. 自动跳过失效门店，继续处理其他有效门店")
print("🎯 4. 操作完成后统一显示跳过的门店列表")
print("🎯 5. 不再强制用户立即重新登录")
