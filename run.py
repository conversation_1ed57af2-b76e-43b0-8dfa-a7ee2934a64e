import asyncio
import os
import sys
from qasync import QEvent<PERSON>oop, asyncClose
from PyQt5.QtWidgets import QApplication

# 添加src目录到Python路径
src_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src')
sys.path.insert(0, src_path)

# 导入并运行主程序
from src.main import main

if __name__ == '__main__':
    app = QApplication(sys.argv)
    loop = QEventLoop(app)
    asyncio.set_event_loop(loop)

    window = main()
    window.show()

    # 检查是否有CEF可用
    CEF_AVAILABLE = False
    try:
        from cefpython3 import cefpython as cef
        CEF_AVAILABLE = True
    except ImportError:
        pass

    try:
        # 运行事件循环
        with loop:
            if CEF_AVAILABLE:
                # 创建CEF消息循环定时器
                from PyQt5.QtCore import QTimer
                cef_timer = QTimer()
                cef_timer.timeout.connect(lambda: cef.MessageLoopWork())
                cef_timer.start(10)  # 每10ms处理一次CEF消息

            loop.run_forever()
    finally:
        # 清理CEF资源
        if CEF_AVAILABLE:
            try:
                cef.Shutdown()
            except Exception as e:
                print(f"CEF清理失败: {e}")