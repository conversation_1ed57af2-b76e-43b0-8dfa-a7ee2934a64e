#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最终的CEF渲染测试脚本 - 使用优化的延迟创建方式
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtCore import QTimer, Qt

# 添加Lib/site-packages到Python路径以支持CEF Python
lib_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'Lib', 'site-packages')
if lib_path not in sys.path:
    sys.path.insert(0, lib_path)

# CEF Python imports
from cefpython3 import cefpython as cef

class ClientHandler:
    """CEF客户端处理器"""

    def __init__(self, parent_widget):
        self.parent_widget = parent_widget

    def OnLoadEnd(self, browser, **_):
        """页面加载完成时的回调"""
        try:
            print("页面加载完成")
            # 延迟触发重绘
            QTimer.singleShot(500, lambda: self._ensure_proper_rendering(browser))
            
        except Exception as e:
            print(f"页面加载完成回调失败: {e}")

    def _ensure_proper_rendering(self, browser):
        """确保浏览器正确渲染"""
        try:
            if browser and self.parent_widget:
                browser.WasResized()
                self.parent_widget.update()
                print("触发浏览器重绘")
        except Exception as e:
            print(f"触发浏览器重绘失败: {e}")


class CEFWidget(QWidget):
    """优化的CEF浏览器组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.browser = None
        self.resize_timer = None
        self.setFocusPolicy(Qt.StrongFocus)
        
        # 设置窗口属性 - 使用最基本的设置
        self.setAttribute(Qt.WA_NativeWindow, True)
        self.setAttribute(Qt.WA_DontCreateNativeAncestors, True)

    def embed_browser(self, url):
        """嵌入CEF浏览器 - 使用优化的延迟创建方式"""
        try:
            # 如果已经有浏览器实例，先关闭它
            if self.browser:
                self.browser.CloseBrowser(True)
                self.browser = None

            # 确保窗口已经显示并获得正确的尺寸
            self.show()
            self.repaint()
            QApplication.processEvents()

            # 等待窗口完全初始化
            QTimer.singleShot(500, lambda: self._create_browser_delayed(url))

        except Exception as e:
            print(f"创建CEF浏览器失败: {e}")

    def _create_browser_delayed(self, url):
        """延迟创建浏览器，确保窗口完全初始化"""
        try:
            # 设置CEF窗口信息 - 使用子窗口模式
            window_info = cef.WindowInfo()
            rect = [0, 0, self.width(), self.height()]
            window_info.SetAsChild(int(self.winId()), rect)

            # 浏览器设置 - 最小化设置避免冲突
            browser_settings = {
                "web_security_disabled": True,
                "plugins_disabled": True,
                "javascript_disabled": False,
                "background_color": 0xFFFFFFFF,
            }

            # 创建浏览器
            self.browser = cef.CreateBrowserSync(
                window_info=window_info,
                url=url,
                settings=browser_settings
            )

            # 设置客户端处理器
            client_handler = ClientHandler(self)
            self.browser.SetClientHandler(client_handler)

            print(f"CEF浏览器创建成功: {url}")

        except Exception as e:
            print(f"延迟创建CEF浏览器失败: {e}")

    def resizeEvent(self, event):
        """窗口大小改变时调整浏览器大小"""
        super().resizeEvent(event)
        if self.browser:
            # 使用定时器延迟调整大小
            if self.resize_timer:
                self.resize_timer.stop()
            
            self.resize_timer = QTimer()
            self.resize_timer.setSingleShot(True)
            self.resize_timer.timeout.connect(self._do_resize)
            self.resize_timer.start(200)  # 延迟200ms执行

    def _do_resize(self):
        """实际执行浏览器大小调整"""
        if self.browser:
            try:
                self.browser.WasResized()
                print(f"浏览器大小已调整: {self.width()}x{self.height()}")
            except Exception as e:
                print(f"调整浏览器大小失败: {e}")

    def closeEvent(self, event):
        """关闭时清理浏览器"""
        try:
            if self.resize_timer:
                self.resize_timer.stop()
                self.resize_timer = None
            
            if self.browser:
                self.browser.CloseBrowser(True)
                self.browser = None
                
            print("CEF浏览器已清理")
        except Exception as e:
            print(f"关闭浏览器失败: {e}")
        super().closeEvent(event)


class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("CEF最终测试")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 创建CEF widget
        self.cef_widget = CEFWidget()
        layout.addWidget(self.cef_widget)
        
        # 延迟加载页面，确保窗口完全初始化
        QTimer.singleShot(1000, self.load_page)

    def load_page(self):
        """延迟加载页面"""
        print("开始加载页面...")
        self.cef_widget.embed_browser("https://www.baidu.com")


def main():
    print("开始初始化CEF...")

    # 初始化CEF
    sys.excepthook = cef.ExceptHook

    # 最小化的CEF设置
    settings = {
        "debug": False,
        "log_severity": cef.LOGSEVERITY_INFO,  # 显示更多日志
        "log_file": "",
        "multi_threaded_message_loop": False,
    }

    cef.Initialize(settings)
    print("CEF初始化完成")

    # 创建Qt应用
    app = QApplication(sys.argv)
    print("Qt应用创建完成")

    # 创建测试窗口
    window = TestWindow()
    window.show()
    print("测试窗口显示完成")

    # CEF消息循环定时器
    cef_timer = QTimer()
    cef_timer.timeout.connect(lambda: cef.MessageLoopWork())
    cef_timer.start(10)
    print("CEF消息循环启动")

    # 运行应用
    print("开始运行应用...")
    app.exec_()

    # 清理CEF
    print("清理CEF...")
    cef.Shutdown()


if __name__ == "__main__":
    main()
