from datetime import datetime
import os
import sys
import json
import ctypes
from ctypes import wintypes
from PyQt5.QtCore import pyqtSignal, QTimer, QThread
from PyQt5.QtWidgets import QMainWindow, QVBoxLayout, QWidget, QPushButton, QProgressBar, QHBoxLayout, QApplication
from PyQt5.QtGui import QImage, QPainter, QIcon, QPixmap, QPaintEvent
from src.utils.logger import default_logger
from qfluentwidgets import PrimaryPushButton, StateToolTip
from PyQt5.QtCore import Qt
from src.services.image_processor import ImageProcessor
from src.ui.components.drop_zone import DropZone

# 添加Lib/site-packages到Python路径以支持CEF Python
lib_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'Lib', 'site-packages')
if os.path.exists(lib_path):
    sys.path.insert(0, lib_path)

# CEF Python imports
from cefpython3 import cefpython as cef

# Windows API 常量和函数
user32 = ctypes.windll.user32
SWP_NOZORDER = 0x0004
SWP_NOACTIVATE = 0x0010


class ClientHandler:
    """CEF客户端处理器，用于处理页面加载事件"""

    def __init__(self, parent_widget):
        self.parent_widget = parent_widget

    def OnLoadEnd(self, browser, **_):
        """页面加载完成时的回调"""
        try:
            # 页面加载完成后设置cookies
            if hasattr(self.parent_widget, 'pending_cookies') and self.parent_widget.pending_cookies:
                self.parent_widget.set_cookies_after_load()

            # 确保浏览器正确渲染和调整大小
            QTimer.singleShot(100, lambda: self._ensure_proper_sizing(browser))

        except Exception as e:
            default_logger.error(f"页面加载完成回调失败: {e}")

    def _ensure_proper_sizing(self, browser):
        """确保浏览器正确调整大小"""
        try:
            if browser and self.parent_widget:
                # 立即强制调整浏览器大小
                self.parent_widget._force_browser_resize()
                default_logger.debug("页面加载后强制调整浏览器大小")

                # 多次延迟调整，确保完全填充
                QTimer.singleShot(200, lambda: self.parent_widget._force_browser_resize())
                QTimer.singleShot(500, lambda: self.parent_widget._force_browser_resize())
                QTimer.singleShot(800, lambda: self.parent_widget._force_browser_resize())
                QTimer.singleShot(1000, lambda: self.parent_widget._force_browser_resize())
                QTimer.singleShot(1500, lambda: self.parent_widget._force_browser_resize())
        except Exception as e:
            default_logger.error(f"触发浏览器重绘失败: {e}")


class WatermarkRemovalThread(QThread):
    finished = pyqtSignal(bytes)
    progress = pyqtSignal(int)
    error = pyqtSignal(str)
    
    def __init__(self, image_processor, image_path, selected_areas):
        super().__init__()
        self.image_processor = image_processor
        self.image_path = image_path
        self.selected_areas = selected_areas
        
    def run(self):
        try:
            with open(self.image_path, 'rb') as f:
                image_bytes = f.read()
            # 模拟进度
            self.progress.emit(30)
            result = self.image_processor.remove_watermark(image_bytes, self.selected_areas)
            self.progress.emit(100)
            if result:
                self.finished.emit(result)
            else:
                self.error.emit("处理失败")
        except Exception as e:
            self.error.emit(str(e))

class CEFWidget(QWidget):
    """CEF浏览器组件 - 使用离屏渲染模式"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.browser = None
        self.pending_cookies = None
        self.target_url = None
        self.resize_timer = None
        self.browser_hwnd = None  # 浏览器窗口句柄
        self.setFocusPolicy(Qt.StrongFocus)
        # 设置窗口属性 - 使用最基本的设置
        self.setAttribute(Qt.WA_NativeWindow, True)
        self.setAttribute(Qt.WA_DontCreateNativeAncestors, True)

    def embed_browser(self, url, cookies=None):
        """嵌入CEF浏览器 - 使用优化的窗口嵌入模式"""
        try:
            # 如果已经有浏览器实例，先关闭它
            if self.browser:
                self.browser.CloseBrowser(True)
                self.browser = None

            # 保存cookies以便后续设置
            self.pending_cookies = cookies
            self.target_url = url

            # 确保窗口已经显示并获得正确的尺寸
            self.show()
            self.repaint()
            QApplication.processEvents()

            # 等待窗口完全初始化
            QTimer.singleShot(500, lambda: self._create_browser_delayed(url))

        except Exception as e:
            default_logger.error(f"创建CEF浏览器失败: {e}")

    def _create_browser_delayed(self, url):
        """延迟创建浏览器，确保窗口完全初始化"""
        try:
            # 获取当前widget的精确尺寸
            widget_rect = self.rect()
            default_logger.debug(f"Widget尺寸: {widget_rect.width()}x{widget_rect.height()}")

            # 获取Windows客户区尺寸（考虑DPI缩放）
            hwnd = int(self.winId())
            client_rect = ctypes.wintypes.RECT()
            user32.GetClientRect(hwnd, ctypes.byref(client_rect))

            cef_width = client_rect.right - client_rect.left
            cef_height = client_rect.bottom - client_rect.top

            default_logger.debug(f"Windows客户区尺寸: {cef_width}x{cef_height}")

            # 设置CEF窗口信息 - 使用Windows API获取的精确坐标
            window_info = cef.WindowInfo()
            rect = [0, 0, cef_width, cef_height]
            window_info.SetAsChild(hwnd, rect)
            default_logger.debug(f"CEF窗口设置: {rect}")

            # 浏览器设置 - 使用CEF Python支持的设置
            browser_settings = {
                "web_security_disabled": True,
                "plugins_disabled": True,
                "javascript_disabled": False,
                "background_color": 0xFFFFFFFF,
            }

            # 创建浏览器
            self.browser = cef.CreateBrowserSync(
                window_info=window_info,
                url=url,
                settings=browser_settings
            )

            # 设置客户端处理器
            client_handler = ClientHandler(self)
            self.browser.SetClientHandler(client_handler)

            # 获取浏览器窗口句柄
            self._get_browser_hwnd()

            # 立即同步调用一次resize
            try:
                self.browser.WasResized()
                default_logger.debug("浏览器创建后立即同步resize")
            except:
                pass

            # 等待浏览器完全初始化后再进行激进resize操作
            QTimer.singleShot(1000, lambda: self._post_creation_aggressive_resize())

            # 备用的多次resize确保渲染正确
            QTimer.singleShot(1200, lambda: self._force_browser_resize())
            QTimer.singleShot(1500, lambda: self._force_browser_resize())

            default_logger.info(f"CEF浏览器创建成功: {url}")

        except Exception as e:
            default_logger.error(f"延迟创建CEF浏览器失败: {e}")

    def set_cookies_after_load(self):
        """页面加载完成后设置cookies"""
        try:
            if self.browser and self.pending_cookies:
                for name, value in self.pending_cookies.items():
                    # 构建cookie字符串
                    domain = ".meituan.com" if "meituan" in self.target_url else ".ele.me"
                    cookie_str = f"{name}={value}; domain={domain}; path=/;"

                    # 通过JavaScript设置cookie
                    js_code = f"document.cookie = '{cookie_str}'"
                    self.browser.GetMainFrame().ExecuteFunction("eval", js_code)

                default_logger.info(f"页面加载完成后设置了 {len(self.pending_cookies)} 个cookies")

                # 清空待设置的cookies
                self.pending_cookies = None

                # 刷新页面以应用cookies
                QTimer.singleShot(1000, lambda: self.browser.Reload() if self.browser else None)

        except Exception as e:
            default_logger.error(f"页面加载完成后设置cookies失败: {e}")

    def _get_browser_hwnd(self):
        """获取CEF浏览器的窗口句柄"""
        try:
            if self.browser:
                # 尝试获取浏览器窗口句柄
                browser_hwnd = self.browser.GetWindowHandle()
                if browser_hwnd:
                    self.browser_hwnd = browser_hwnd
                    default_logger.debug(f"获取到浏览器窗口句柄: {browser_hwnd}")
                else:
                    # 如果直接获取失败，尝试查找子窗口
                    QTimer.singleShot(200, self._find_browser_hwnd)
        except Exception as e:
            default_logger.error(f"获取浏览器窗口句柄失败: {e}")

    def _find_browser_hwnd(self):
        """查找CEF浏览器的子窗口句柄"""
        try:
            parent_hwnd = int(self.winId())

            def enum_child_proc(hwnd, lparam):
                # 检查是否是Chrome子窗口
                class_name = ctypes.create_unicode_buffer(256)
                user32.GetClassNameW(hwnd, class_name, 256)
                if "Chrome" in class_name.value:
                    self.browser_hwnd = hwnd
                    default_logger.debug(f"找到Chrome子窗口: {hwnd}, 类名: {class_name.value}")
                    return False  # 停止枚举
                return True  # 继续枚举

            # 枚举子窗口
            enum_child_proc_type = ctypes.WINFUNCTYPE(ctypes.c_bool, wintypes.HWND, wintypes.LPARAM)
            user32.EnumChildWindows(parent_hwnd, enum_child_proc_type(enum_child_proc), 0)

        except Exception as e:
            default_logger.error(f"查找浏览器子窗口失败: {e}")

    def _trigger_size_oscillation(self):
        """触发尺寸震荡以强制CEF重新渲染"""
        try:
            if self.browser_hwnd:
                # 获取当前正确尺寸
                hwnd = int(self.winId())
                client_rect = ctypes.wintypes.RECT()
                user32.GetClientRect(hwnd, ctypes.byref(client_rect))

                correct_width = client_rect.right - client_rect.left
                correct_height = client_rect.bottom - client_rect.top

                # 先设置一个稍微小一点的尺寸
                user32.SetWindowPos(
                    self.browser_hwnd,
                    0, 0, 0,
                    correct_width - 1,
                    correct_height - 1,
                    0x0004 | 0x0010  # SWP_NOZORDER | SWP_NOACTIVATE
                )

                # 立即设置回正确尺寸
                QTimer.singleShot(10, lambda: self._restore_correct_size(correct_width, correct_height))

                default_logger.debug(f"触发尺寸震荡: {correct_width}x{correct_height}")
        except Exception as e:
            default_logger.debug(f"尺寸震荡失败: {e}")

    def _restore_correct_size(self, width, height):
        """恢复正确尺寸"""
        try:
            if self.browser_hwnd:
                user32.SetWindowPos(
                    self.browser_hwnd,
                    0, 0, 0,
                    width, height,
                    0x0004 | 0x0010  # SWP_NOZORDER | SWP_NOACTIVATE
                )

                # 强制刷新
                user32.InvalidateRect(self.browser_hwnd, None, True)
                user32.UpdateWindow(self.browser_hwnd)

                if self.browser:
                    self.browser.WasResized()

                default_logger.debug(f"恢复正确尺寸: {width}x{height}")
        except Exception as e:
            default_logger.debug(f"恢复正确尺寸失败: {e}")

    def _post_creation_aggressive_resize(self):
        """浏览器创建后的激进resize操作"""
        try:
            default_logger.debug("开始浏览器创建后激进resize...")

            # 获取正确尺寸
            hwnd = int(self.winId())
            client_rect = ctypes.wintypes.RECT()
            user32.GetClientRect(hwnd, ctypes.byref(client_rect))

            correct_width = client_rect.right - client_rect.left
            correct_height = client_rect.bottom - client_rect.top

            default_logger.debug(f"目标尺寸: {correct_width}x{correct_height}")

            if self.browser_hwnd:
                # 步骤1: 先设置一个明显不同的尺寸
                default_logger.debug("步骤1: 设置临时尺寸")
                user32.SetWindowPos(
                    self.browser_hwnd, 0, 0, 0,
                    correct_width // 2, correct_height // 2,
                    SWP_NOZORDER | SWP_NOACTIVATE
                )

                # 步骤2: 等待一下再设置正确尺寸
                QTimer.singleShot(100, lambda: self._set_aggressive_final_size(correct_width, correct_height))

            # 同时调用CEF方法
            if self.browser:
                self.browser.WasResized()
                try:
                    self.browser.NotifyMoveOrResizeStarted()
                except:
                    pass

        except Exception as e:
            default_logger.debug(f"激进resize失败: {e}")

    def _set_aggressive_final_size(self, width, height):
        """设置激进resize的最终正确尺寸"""
        try:
            default_logger.debug(f"步骤2: 设置最终尺寸 {width}x{height}")

            if self.browser_hwnd:
                # 设置正确尺寸
                result = user32.SetWindowPos(
                    self.browser_hwnd, 0, 0, 0,
                    width, height,
                    SWP_NOZORDER | SWP_NOACTIVATE
                )
                default_logger.debug(f"SetWindowPos结果: {result}")

                # 强制刷新
                user32.InvalidateRect(self.browser_hwnd, None, True)
                user32.UpdateWindow(self.browser_hwnd)
                default_logger.debug("强制刷新完成")

            # 再次调用CEF方法
            if self.browser:
                self.browser.WasResized()
                default_logger.debug("调用WasResized完成")

            # 强制重绘Qt widget
            self.update()
            self.repaint()
            QApplication.processEvents()
            default_logger.debug("Qt重绘完成")

            # 最后再确认一次
            QTimer.singleShot(500, self._aggressive_final_confirmation)

        except Exception as e:
            default_logger.debug(f"设置最终尺寸失败: {e}")

    def _aggressive_final_confirmation(self):
        """激进resize的最终确认"""
        try:
            default_logger.debug("激进resize最终确认...")
            if self.browser:
                self.browser.WasResized()
            default_logger.debug("激进resize最终确认完成")
        except Exception as e:
            default_logger.debug(f"激进resize最终确认失败: {e}")

    def set_cookies(self, cookies, url):
        """设置cookies（兼容性方法）"""
        try:
            # CEF Python 66.1版本使用不同的cookie设置方法
            # 通过JavaScript设置cookies
            if self.browser:
                for name, value in cookies.items():
                    # 构建cookie字符串
                    domain = ".meituan.com" if "meituan" in url else ".ele.me"
                    cookie_str = f"{name}={value}; domain={domain}; path=/;"

                    # 通过JavaScript设置cookie
                    js_code = f"document.cookie = '{cookie_str}'"
                    self.browser.GetMainFrame().ExecuteFunction("eval", js_code)

                default_logger.info(f"通过JavaScript设置了 {len(cookies)} 个cookies")
        except Exception as e:
            default_logger.error(f"设置cookies失败: {e}")

    def resizeEvent(self, event):
        """窗口大小改变时调整浏览器大小"""
        super().resizeEvent(event)

        # 使用定时器延迟调整大小，避免频繁调用
        if self.resize_timer:
            self.resize_timer.stop()

        self.resize_timer = QTimer()
        self.resize_timer.setSingleShot(True)
        self.resize_timer.timeout.connect(self._do_resize)
        self.resize_timer.start(50)  # 减少延迟到50ms

    def _do_resize(self):
        """实际执行浏览器大小调整"""
        if self.browser:
            try:
                self._force_browser_resize()
            except Exception as e:
                default_logger.error(f"调整浏览器大小失败: {e}")

    def _force_browser_resize(self):
        """强制调整浏览器大小 - 多种方法组合"""
        if not self.browser:
            return

        try:
            # 获取Windows客户区的精确尺寸（考虑DPI缩放）
            hwnd = int(self.winId())
            client_rect = ctypes.wintypes.RECT()
            user32.GetClientRect(hwnd, ctypes.byref(client_rect))

            new_width = client_rect.right - client_rect.left
            new_height = client_rect.bottom - client_rect.top

            default_logger.debug(f"强制调整浏览器大小到: {new_width}x{new_height} (Windows API)")

            # 方法1: 调用CEF的多种resize方法
            self.browser.WasResized()

            # 尝试CEF的其他resize方法
            try:
                self.browser.NotifyMoveOrResizeStarted()
            except:
                pass

            # 方法2: 如果有浏览器窗口句柄，直接调整Windows窗口大小
            if self.browser_hwnd:
                try:
                    # 使用SetWindowPos调整子窗口大小
                    result = user32.SetWindowPos(
                        self.browser_hwnd,  # 窗口句柄
                        0,                  # 插入位置
                        0,                  # X坐标
                        0,                  # Y坐标
                        new_width,          # 宽度
                        new_height,         # 高度
                        SWP_NOZORDER | SWP_NOACTIVATE  # 标志
                    )
                    if result:
                        default_logger.debug(f"Windows API调整窗口大小成功: {new_width}x{new_height}")
                    else:
                        default_logger.debug("Windows API调整窗口大小失败")

                    # 额外强制刷新窗口
                    user32.InvalidateRect(self.browser_hwnd, None, True)
                    user32.UpdateWindow(self.browser_hwnd)

                except Exception as e:
                    default_logger.debug(f"Windows API调整窗口大小异常: {e}")
            else:
                # 如果还没有窗口句柄，尝试重新获取
                self._get_browser_hwnd()

            # 方法3: 强制重绘
            self.update()
            self.repaint()
            QApplication.processEvents()

            # 方法4: 延迟再次调用WasResized
            QTimer.singleShot(100, lambda: self._delayed_resize())

        except Exception as e:
            default_logger.error(f"强制调整浏览器大小异常: {e}")

    def _delayed_resize(self):
        """延迟调用WasResized"""
        try:
            if self.browser:
                self.browser.WasResized()
                default_logger.debug("延迟调用WasResized完成")
        except Exception as e:
            default_logger.debug(f"延迟调用WasResized失败: {e}")

    def closeEvent(self, event):
        """关闭时清理浏览器"""
        try:
            # 停止定时器
            if self.resize_timer:
                self.resize_timer.stop()
                self.resize_timer = None

            # 关闭浏览器
            if self.browser:
                self.browser.CloseBrowser(True)
                self.browser = None

            # 清理窗口句柄
            self.browser_hwnd = None

            default_logger.info("CEF浏览器已清理")
        except Exception as e:
            default_logger.error(f"关闭浏览器失败: {e}")
        super().closeEvent(event)


class MeituanEditWindow(QMainWindow):
    # 添加信号
    cookies_ready = pyqtSignal(dict)

    def __init__(self, url: str, cookies: dict, store_name: str, platform:str, islogin = False, action = None, parent=None, on_page_loaded=None):
        super().__init__(parent)

        # 设置窗口图标
        icon_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
            'assets',
            'images',
            'meituan.png' if platform == '美团' else 'eleme.png'
        )

        # 设置窗口图标
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))
            default_logger.info(f"设置{platform}窗口图标成功")
        else:
            default_logger.warning(f"未找到{platform}图标文件: {icon_path}")

        if action == 'discount':
            self.setWindowTitle(f'折扣编辑 - {store_name}')
        else:
            self.setWindowTitle(f'商品编辑 - {store_name}')
        self.resize(1200, 800)
        self.stored_cookies = cookies.copy()
        self.islogin = islogin
        self.on_page_loaded = on_page_loaded
        self.action = action  # 保存action参数
        self.waiting_for_screenshot = False
        self.screenshot_timeout_timer = None

        # 创建主窗口部件和布局
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        main_layout = QHBoxLayout(main_widget)  # 改为水平布局
        main_layout.setContentsMargins(0, 0, 0, 0)  # 移除边距
        main_layout.setSpacing(0)  # 移除间距

        # 创建左侧网页区域
        web_container = QWidget()
        web_layout = QVBoxLayout(web_container)
        web_layout.setContentsMargins(0, 0, 0, 0)

        default_logger.info(f"创建{platform}编辑窗口: {store_name}")

        # 创建CEF浏览器组件
        self.web_view = CEFWidget()
        web_layout.addWidget(self.web_view)

        default_logger.info("CEF浏览器组件创建成功")

        if self.islogin:
            # 添加完成登录按钮
            self.finish_button = PrimaryPushButton('完成登录')
            self.finish_button.clicked.connect(self._on_finish_clicked)
            web_layout.addWidget(self.finish_button)

        # 添加网页区域到主布局
        main_layout.addWidget(web_container)

        # 创建右侧操作区域
        self.right_panel = QWidget()
        self.right_panel.setFixedWidth(320)  # 设置固定宽度
        self.right_panel.hide()  # 默认隐藏
        right_layout = QVBoxLayout(self.right_panel)
        right_layout.setContentsMargins(0, 0, 0, 0)

        # 创建水印处理区域
        self.setup_watermark_removal_widget()
        right_layout.addWidget(self.watermark_widget)
        right_layout.addStretch()  # 添加弹性空间

        # 添加右侧面板到主布局
        main_layout.addWidget(self.right_panel)

        # 创建显示/隐藏按钮
        self.toggle_button = QPushButton('', self)
        self.toggle_button.setFixedSize(24, 60)
        self.toggle_button.clicked.connect(self.toggle_right_panel)
        self.toggle_button.setStyleSheet("""
            QPushButton {
                background-color: #f0f0f0;
                border: 1px solid #ccc;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
            }
        """)
        self.update_toggle_button()

        # 添加截图相关的属性
        self.screenshot_taken = False
        self.screenshot_retry_count = 0  # 添加重试计数
        self.max_screenshot_retries = 3  # 最大重试次数
        self.url = url
        self.store_name = store_name
        self.platform = platform

        # 创建图片处理器实例
        self.image_processor = ImageProcessor(
            api_key='oHrCgKkb6rXuuZs6H5hbRCL8',
            secret_key='RawUU1XjSNPgk158OtHbnRstTENNoltc'
        )

        # 延迟加载页面，确保窗口完全显示后再加载
        QTimer.singleShot(100, lambda: self.load_page(url, cookies))

    def load_page(self, url, cookies):
        """加载页面"""
        try:
            self.web_view.embed_browser(url, cookies)
            default_logger.info(f"开始加载页面: {url}")

            # 延迟截图
            QTimer.singleShot(5000, self.take_screenshot)
        except Exception as e:
            default_logger.error(f"加载页面失败: {e}")

    def _on_finish_clicked(self):
        """完成登录按钮点击处理"""
        try:
            # 获取当前cookies
            if self.web_view.browser:
                # CEF的cookie获取是异步的，这里暂时使用初始cookies
                # 在实际应用中可以通过JavaScript获取cookies
                pass

            # 发送cookies就绪信号
            self.cookies_ready.emit(self.stored_cookies.copy())
            default_logger.info(f"已获取 {len(self.stored_cookies)} 个cookies")
            # 关闭窗口
            self.close()
        except Exception as e:
            default_logger.error(f"处理完成登录失败: {str(e)}")

    def get_cookies(self) -> dict:
        """获取当前保存的所有cookies"""
        return self.stored_cookies.copy()

    def take_screenshot(self):
        """捕获网页截图"""
        if self.screenshot_taken:
            return

        try:
            # 确保窗口不是最小化状态，并且在前台
            self.setWindowState(self.windowState() & ~Qt.WindowMinimized | Qt.WindowActive)
            self.activateWindow()
            self.raise_()
            self.show()

            # 检查CEF浏览器是否可见
            if not self.web_view.isVisible():
                default_logger.warning("CEF浏览器 不可见，等待显示")
                return

            def do_screenshot():
                """执行实际的截图操作"""
                try:
                    # 直接获取CEF组件的截图
                    pixmap = self.web_view.grab()

                    # 检查截图是否有效
                    if pixmap.isNull() or pixmap.width() == 0 or pixmap.height() == 0:
                        handle_screenshot_failure("截图无效")
                        return

                    # 创建一个正方形的图像
                    size = min(pixmap.width(), pixmap.height())
                    square_image = QImage(size, size, QImage.Format_ARGB32)

                    # 使用QPainter绘制正方形截图
                    painter = QPainter(square_image)
                    painter.drawImage(0, 0, pixmap.toImage().copy(0, 0, size, size))
                    painter.end()

                    # 保存截图
                    cache_dir = os.path.join(
                        os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
                        'cache',
                        'snapshots'
                    )
                    os.makedirs(cache_dir, exist_ok=True)

                    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
                    filename = f"{self.platform}_{self.store_name}_{timestamp}.png"
                    filepath = os.path.join(cache_dir, filename)

                    square_image.save(filepath)
                    default_logger.info(f"截图保存成功: {filepath}")

                    # 调用回调函数
                    if self.on_page_loaded:
                        self.on_page_loaded()

                    self.screenshot_taken = True

                except Exception as e:
                    default_logger.error(f"创建截图失败: {e}")
                    handle_screenshot_failure(str(e))

            def handle_screenshot_failure(reason):
                """处理截图失败的情况"""
                self.screenshot_retry_count += 1
                if self.screenshot_retry_count < self.max_screenshot_retries:
                    default_logger.warning(f"截图失败({reason})，第{self.screenshot_retry_count}次重试")
                    QTimer.singleShot(1000, check_content)
                else:
                    default_logger.error(f"截图失败({reason})，已达到最大重试次数")
                    self.screenshot_taken = True  # 防止继续重试
                    if self.on_page_loaded:
                        self.on_page_loaded()

            def check_content():
                """检查页面内容是否已加载"""
                if self.screenshot_taken:
                    return

                # CEF浏览器不需要检查页面状态，直接截图
                do_screenshot()

            # 开始检查和截图流程
            check_content()

        except Exception as e:
            default_logger.error(f"创建截图失败: {e}")
            self.screenshot_taken = True  # 防止继续重试
            if self.on_page_loaded:
                self.on_page_loaded()

    def closeEvent(self, event):
        """窗口关闭时的处理"""
        try:
            # 清理CEF浏览器资源
            if hasattr(self, 'web_view') and self.web_view:
                self.web_view.closeEvent(event)

            default_logger.info("清理CEF浏览器资源成功")
        except Exception as e:
            default_logger.error(f"清理CEF浏览器资源失败: {e}")
        super().closeEvent(event)

    def toggle_right_panel(self):
        """切换右侧面板的显示/隐藏状态"""
        if self.right_panel.isVisible():
            self.right_panel.hide()
        else:
            self.right_panel.show()
        self.update_toggle_button()

    def update_toggle_button(self):
        """更新切换按钮的文本和位置"""
        if self.right_panel.isVisible():
            self.toggle_button.setText('>')
            self.toggle_button.setToolTip('隐藏图片处理区域')
        else:
            self.toggle_button.setText('<')
            self.toggle_button.setToolTip('显示图片处理区域')
        
        # 更新按钮位置
        self.update_toggle_button_position()

    def update_toggle_button_position(self):
        """更新切换按钮的位置"""
        if self.right_panel.isVisible():
            x = self.width() - self.right_panel.width() - self.toggle_button.width()
        else:
            x = self.width() - self.toggle_button.width()
        y = (self.height() - self.toggle_button.height()) // 2
        self.toggle_button.move(x, y)

    def resizeEvent(self, event):
        """窗口大小改变时的处理"""
        super().resizeEvent(event)
        self.update_toggle_button_position()

    def setup_watermark_removal_widget(self):
        """设置水印处理区域"""
        self.watermark_widget = QWidget()
        watermark_layout = QVBoxLayout(self.watermark_widget)
        watermark_layout.setContentsMargins(10, 10, 10, 10)
        watermark_layout.setSpacing(10)
        
        # 创建拖放区域
        self.drop_zone = DropZone()
        self.drop_zone.setFixedSize(300, 300)
        self.drop_zone.image_dropped.connect(self.handle_image_drop)
        
        # 创建按钮容器
        button_container = QWidget()
        button_layout = QVBoxLayout(button_container)
        button_layout.setContentsMargins(0, 0, 0, 0)
        button_layout.setSpacing(5)
        
        # 创建进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setFixedWidth(280)  # 调整进度条宽度
        self.progress_bar.hide()
        
        # 创建处理按钮
        self.process_button = PrimaryPushButton('AI修复')
        self.process_button.setFixedWidth(280)  # 调整按钮宽度
        self.process_button.setToolTip('使用百度AI对选中区域进行智能修复')
        self.process_button.clicked.connect(self.process_image)
        self.process_button.setEnabled(False)
        
        # 创建模式切换按钮
        self.mode_button = PrimaryPushButton('切换到拖拽模式')
        self.mode_button.setFixedWidth(280)  # 调整按钮宽度
        self.mode_button.clicked.connect(self.toggle_mode)
        self.mode_button.setEnabled(False)
        
        # 添加清除选择按钮
        self.clear_button = PrimaryPushButton('清除选择')
        self.clear_button.setFixedWidth(280)  # 调整按钮宽度
        self.clear_button.clicked.connect(self.clear_selection)
        self.clear_button.setEnabled(False)
        
        # 将按钮添加到按钮容器
        button_layout.addWidget(self.progress_bar)
        button_layout.addWidget(self.process_button)
        button_layout.addWidget(self.mode_button)
        button_layout.addWidget(self.clear_button)
        button_layout.addStretch()  # 添加弹性空间
        
        # 添加到布局
        watermark_layout.addWidget(self.drop_zone)
        watermark_layout.addWidget(button_container)
        watermark_layout.setAlignment(Qt.AlignTop)
        
        # 存储选择的区域
        self.selected_areas = []
        
        # 连接区域选择信号
        self.drop_zone.area_selected.connect(self.on_area_selected)
        
        # 设置位置和大小
        self.watermark_widget.setGeometry(
            0,  # 调整左边距离
            0,  # 顶部距离
            300,                # 调整宽度
            450                 # 调整高度
        )
        
        # 状态提示
        self.state_tooltip = None

    def toggle_mode(self):
        """切换拖拽/选择模式"""
        if self.drop_zone.is_drag_mode:
            # 切换到选择模式
            self.drop_zone.is_drag_mode = False
            self.mode_button.setText('切换到拖拽模式')
            self.clear_button.setEnabled(True)
        else:
            # 切换到拖拽模式
            self.drop_zone.is_drag_mode = True
            self.mode_button.setText('切换到选择模式')
            self.clear_button.setEnabled(False)

    def handle_image_drop(self, image_path):
        """处理拖入的图片"""
        self.current_image_path = image_path
        # 显示预览
        pixmap = QPixmap(image_path)
        scaled_pixmap = pixmap.scaled(
            self.drop_zone.size(), 
            Qt.KeepAspectRatio, 
            Qt.SmoothTransformation
        )
        self.drop_zone.setPixmap(scaled_pixmap)
        # 重置按钮状态
        self.process_button.setEnabled(False)  # 默认禁用，等待用户选择区域
        self.clear_button.setEnabled(False)
        # 清除之前的选择
        self.selected_areas.clear()
        self.drop_zone.clear_selection()
        
    def process_image(self):
        """处理图片"""
        if not hasattr(self, 'current_image_path') or not self.selected_areas:
            return
            
        # 显示进度条
        self.progress_bar.show()
        self.progress_bar.setValue(0)
        self.process_button.setEnabled(False)
        
        # 创建处理线程
        self.worker_thread = WatermarkRemovalThread(
            self.image_processor, 
            self.current_image_path,
            self.selected_areas  # 传入选择的区域
        )
        
        # 连接信号
        self.worker_thread.progress.connect(self.update_progress)
        self.worker_thread.finished.connect(self.handle_processed_image)
        self.worker_thread.error.connect(self.handle_error)
        
        # 显示状态提示
        self.state_tooltip = StateToolTip('处理中', '正在去除水印...', self)
        self.state_tooltip.move(
            self.width() - 220,
            320
        )
        self.state_tooltip.show()
        
        # 启动线程
        self.worker_thread.start()
        
    def update_progress(self, value):
        """更新进度条"""
        self.progress_bar.setValue(value)
        
    def handle_processed_image(self, image_data):
        """处理完成的图片"""
        # 保存处理后的图片
        processed_path = self.current_image_path.replace(
            '.',
            '_processed.'
        )
        with open(processed_path, 'wb') as f:
            f.write(image_data)
            
        # 更新预览并设置为可拖拽
        self.drop_zone.set_processed_image(processed_path)
        
        # 更新状态
        self.progress_bar.hide()
        self.process_button.setEnabled(True)
        self.mode_button.setEnabled(True)  # 启用模式切换按钮
        if self.state_tooltip:
            self.state_tooltip.setContent('处理完成，点击"切换到拖拽模式"后可拖拽图片到网页中')
            self.state_tooltip.setState(True)
            
    def handle_error(self, error_msg):
        """处理错误"""
        self.progress_bar.hide()
        self.process_button.setEnabled(True)
        self.mode_button.setEnabled(True)  # 启用模式切换按钮
        if self.state_tooltip:
            self.state_tooltip.setContent(f'处理失败: {error_msg}')
            self.state_tooltip.setState(False)
            
    def on_area_selected(self, area):
        """处理选择的区域"""
        self.selected_areas.append(area)
        self.process_button.setEnabled(True)
        self.clear_button.setEnabled(True)

    def clear_selection(self):
        """清除选择的区域"""
        self.selected_areas.clear()
        self.drop_zone.clear_selection()
        self.process_button.setEnabled(False)
        self.clear_button.setEnabled(False)