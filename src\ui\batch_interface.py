from datetime import datetime
import os
import json
import asyncio
from typing import List, Dict

import aiohttp
from PyQt5.QtCore import Qt, QSize, pyqtSignal, QUrl
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidgetItem,
                             QLabel, QHeaderView, QCheckBox, QFrame, QMainWindow,
                             QMessageBox)
from PyQt5.QtGui import QPixmap, QColor
from PyQt5.QtWebEngineCore import QWebEngineCookieStore
from PyQt5.QtWebEngineWidgets import QWebEngineProfile, QWebEnginePage, QWebEngineView
from PyQt5.QtNetwork import QNetworkCookie
from qfluentwidgets import (CardWidget, ComboBox, PushButton, TextEdit,
                          TableWidget, CheckBox, LineEdit, SubtitleLabel,
                          FluentIcon, InfoBar, InfoBarPosition, SearchLineEdit,
                          TransparentToolButton,PrimaryPushButton)
from qasync import asyncSlot

from src.services.product_sync import ProductSyncService
from src.utils.logger import default_logger
from src.ui.dialogs.stock_sync_dialog import StockSyncDialog
from .components.image_label import ImageLabel
from src.utils.mtgsig import MtgsigGenerator
from .dialogs.price_dialog import PriceDialog
from src.api.meituan_api import MeituanAPI
from src.api.eleme_api import Eleme
from src.models.store_data import get_session_context, Discount
from src.ui.meituaneditwindow import MeituanEditWindow
from .dialogs.image_manager_dialog import ImageManagerDialog
from .图片上传模块 import changePicture

class BatchInterface(QWidget):
    def __init__(self, loop=None, parent=None):
        super().__init__(parent)
        
        # 保存事件循环
        self.loop = loop or asyncio.get_event_loop()
        
        # 初始化服务
        self.root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        self.cache_file = os.path.join(self.root_dir, 'cache', 'store_cache.json')

        self.product_sync = ProductSyncService(os.path.join(self.root_dir, 'cache'))
        
        # 保存编辑窗口的引用
        self.edit_windows = []

        # 初始化UI
        self.setup_ui()
        self.setup_master_store_table()
        
        # 加载门店数据
        self.load_stores()

        # 连接信号
        self.connect_signals()

    def load_stores(self):
        """从缓存文件加载门店列表"""
        try:
            # 检查文件是否存在
            if not os.path.exists(self.cache_file):
                default_logger.error(f"门店缓存文件不存在，路径: {self.cache_file}")
                InfoBar.error(
                    title='错误',
                    content=f'门店缓存文件不存在\n路径: {self.cache_file}',
                    parent=self
                )
                return

            # 读取缓存文件
            with open(self.cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)

            default_logger.info(f"成功读取缓存文件，包含 {len(cache_data.get('stores', {}))} 个门店")

            # 清空并添加门店
            self.store_combo.clear()
            self.store_combo.addItem('全部门店', userData={'id': 'all', 'platform': '全部平台'})

            # 获取当前选择的平台
            current_platform = self.platform_combo.currentText()

            # 遍历门店数据
            for object_id, store_data in cache_data['stores'].items():
                # 如果选择了特定平台，则只显示该平台的门店
                if current_platform != '全部平台' and store_data['platform'] != current_platform:
                    continue

                # 获取cookies
                cookies = cache_data.get('cookies', {}).get(object_id)

                # 获取店铺ID (从cookies中获取)
                store_id = None
                if cookies:
                    if store_data['platform'] == '美团':
                        store_id = cookies.get('wmPoiId')
                    elif store_data['platform'] == '饿了么':
                        if store_data['storeName'] == '星辰':
                            store_id = '1078371359'
                        elif store_data['storeName'] == '繁花觅':
                            store_id = '872232395'
                        elif store_data['storeName'] == '繁花里':
                            store_id = '20010011159'

                # 显示格式：店铺名称 (平台)
                display_name = f"{store_data['storeName']} ({store_data['platform']})"

                # 使用完整数据作为userData，确保包含所有必要的字段
                store_info = {
                    'id': store_id,  # 使用wmPoiId作为店铺ID
                    'object_id': object_id,
                    'cookies': cookies,
                    'platform': store_data['platform'],
                    'name': store_data['storeName']
                }

                if store_info['id']:  # 只添加有效的店铺ID
                    self.store_combo.addItem(display_name, userData=store_info)
                else:
                    default_logger.warning(f"店铺 {display_name} 没有有效的店铺ID，已跳过")

            default_logger.info(f"成功加载 {self.store_combo.count() - 1} 个门店到下拉框")

        except Exception as e:
            default_logger.error(f"加载门店数据失败: {e}")
            self.store_combo.clear()
            self.store_combo.addItem('加载失败', None)
            InfoBar.error(
                title='错误',
                content=f'加载门店数据失败: {str(e)}',
                parent=self
            )

    def setup_ui(self):
        """初始化界面"""
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(16, 16, 16, 16)
        self.layout.setSpacing(12)
        self.layout.setAlignment(Qt.AlignTop)

        # 顶部搜索区域
        self.top_layout = QHBoxLayout()
        self.top_layout.setSpacing(8)

        # 门店选择区域
        self.store_layout = QHBoxLayout()

        # 平台筛选
        self.platform_label = QLabel('平台:', self)
        self.platform_combo = ComboBox(self)
        self.platform_combo.setFixedWidth(100)
        self.platform_combo.addItems(['全部平台', '美团', '饿了么'])
        self.platform_combo.currentIndexChanged.connect(self.on_platform_changed)

        # 门店选择
        self.store_label = QLabel('门店:', self)
        self.store_combo = ComboBox(self)
        self.store_combo.setFixedWidth(200)

        self.store_layout.addWidget(self.platform_label)
        self.store_layout.addWidget(self.platform_combo)
        self.store_layout.addSpacing(16)
        self.store_layout.addWidget(self.store_label)
        self.store_layout.addWidget(self.store_combo)
        self.top_layout.addLayout(self.store_layout)

        # 搜索区域
        self.search_layout = QHBoxLayout()
        self.search_layout.setSpacing(8)

        # 关键字搜索
        self.keyword_input = SearchLineEdit(self)
        self.keyword_input.setPlaceholderText('请输入商品名称')
        self.keyword_input.setFixedWidth(200)

        # 清空和搜索按钮
        self.clear_btn = TransparentToolButton(FluentIcon.DELETE, self)
        self.clear_btn.setFixedSize(32, 32)
        self.clear_btn.setIconSize(QSize(16, 16))

        self.search_btn = TransparentToolButton(FluentIcon.SEARCH, self)
        self.search_btn.setFixedSize(32, 32)
        self.search_btn.setIconSize(QSize(16, 16))

        self.search_layout.addWidget(self.keyword_input)
        self.search_layout.addWidget(self.search_btn)
        self.search_layout.addWidget(self.clear_btn)

        self.stock_sync_btn = PrimaryPushButton('库存同步', self)
        self.stock_sync_btn.setFixedSize(100, 32)
        self.stock_sync_btn.clicked.connect(self.show_stock_sync_dialog)

        self.top_layout.addLayout(self.search_layout)
        self.top_layout.addStretch(1)
        self.top_layout.addWidget(self.stock_sync_btn)
        self.layout.addLayout(self.top_layout)

        # 添加总店数据表格
        self.master_store_table = TableWidget(self)
        self.master_store_table.setColumnCount(13)  # 增加删除按钮列
        self.master_store_table.setHorizontalHeaderLabels([
            '选择', '图片', '商品名称', '商品ID', '所属门店', '原价', '折扣价', '库存',
            '限购数量', '折扣排序', '起购数量', '销售状态', '删除商品'
        ])
        
        # 设置表格列宽
        self.master_store_table.setColumnWidth(0, 40)   # 选择列
        self.master_store_table.setColumnWidth(1, 100)   # 图片列
        self.master_store_table.setColumnWidth(12, 100)  # 删除按钮列
        # # 其他列自动调整
        # for i in range(self.master_store_table.columnCount()):
        #     if i != 1:  # 除了图片列，其他列自动调整
        #         self.master_store_table.horizontalHeader().setSectionResizeMode(i, QHeaderView.ResizeToContents)
        
        # 只显示一行
        self.master_store_table.setRowCount(1)
        self.master_store_table.setRowHeight(0, 70)  # 设置行高
        # 隐藏选中、商品id列、所属门店
        self.master_store_table.setColumnHidden(3, True)
        self.master_store_table.setColumnHidden(4, True)
        # 把原价、折扣价、库存、限购数量、起购数量、折扣排序、宽度设置为100
        self.master_store_table.setColumnWidth(5, 80)
        self.master_store_table.setColumnWidth(6, 80)
        self.master_store_table.setColumnWidth(7, 80)
        self.master_store_table.setColumnWidth(8, 80)
        self.master_store_table.setColumnWidth(9, 80)
        self.master_store_table.setColumnWidth(11, 250)

        # 设置表格高度100
        self.master_store_table.setFixedHeight(100)
        
        # 添加选择框
        checkbox = QCheckBox(self)
        self.master_store_table.setCellWidget(0, 0, checkbox)

        # 添加默认数据
        for col in range(1, 12):
            if col == 1:  # 图片列
                image_label = ImageLabel(self, event_loop=self.loop)
                # 断开默认的预览连接
                image_label.image_clicked.disconnect(image_label.show_preview)
                # 连接到图片管理对话框
                image_label.image_clicked.connect(
                    lambda: asyncio.create_task(self.show_master_image_manager())
                )
                self.master_store_table.setCellWidget(0, col, image_label)
            elif col == 11:  # 状态列
                status_widget = QWidget()
                status_layout = QHBoxLayout(status_widget)
                status_layout.setContentsMargins(0, 0, 0, 0)
                status_layout.setAlignment(Qt.AlignCenter)
                status_layout.setSpacing(4)
                
                online_btn = PushButton('上架', self)
                offline_btn = PushButton('下架', self)
                
                status_layout.addWidget(online_btn)
                status_layout.addWidget(offline_btn)
                self.master_store_table.setCellWidget(0, col, status_widget)
            elif col == 2:
                # 放入一个TextEdit
                text_edit = TextEdit(self)
                text_edit.setFixedHeight(60)  # 增加高度以容纳多行
                text_edit.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)  # 添加垂直滚动条
                text_edit.setLineWrapMode(TextEdit.WidgetWidth)  # 启用自动换行
                # 监控回车键按下
                text_edit.keyPressEvent = lambda event: self.handle_text_edit_keypress(event, text_edit)
                # 连接文本变化信号
                text_edit.textChanged.connect(lambda: self.update_product_name_header(text_edit.toPlainText()))
                self.master_store_table.setCellWidget(0, col, text_edit)
            else:
                item = QTableWidgetItem('-')
                item.setTextAlignment(Qt.AlignCenter)
                self.master_store_table.setItem(0, col, item)

        # 添加总店数据区域
        # 设置表格标题
        master_store_label = SubtitleLabel('总店数据（修改此处数据将同步修改下方所有门店）', self)
        self.layout.addWidget(master_store_label)
        self.layout.addWidget(self.master_store_table)
        
        # 添加分隔线
        separator = QFrame(self)
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        self.layout.addWidget(separator)

        # 商品表格
        self.products_table = TableWidget(self)
        self.products_table.setColumnCount(16)  # 增加删除按钮列
        self.products_table.setSortingEnabled(True)  # 启用排序
        self.products_table.setHorizontalHeaderLabels([
            '选择', '图片', '商品名称', '规格', '商品ID', '所属门店', '原价', '折扣价', '库存',
            '限购数量', '折扣排序', '起购数量', '状态', '商品信息', '折扣信息', '删除商品'
        ])

        # 设置表格列宽
        self.products_table.setColumnWidth(0, 40)   # 选择列
        self.products_table.setColumnWidth(1, 70)   # 图片列
        self.products_table.setColumnWidth(15, 100)  # 删除按钮列
        # 隐藏第1、5列
        self.products_table.setColumnHidden(4, True)

        # 其他列自动调整
        for i in range(self.products_table.columnCount()):
            if i not in [1, 15]:  # 除了图片列和删除按钮列，其他列自动调整
                self.products_table.horizontalHeader().setSectionResizeMode(i, QHeaderView.ResizeToContents)

        self.products_table.setAlternatingRowColors(True)
        self.products_table.verticalHeader().hide()
        self.layout.addWidget(self.products_table)

        # 设置单元格可编辑
        self.products_table.setEditTriggers(TableWidget.DoubleClicked | TableWidget.EditKeyPressed)
        
        # 创建一个非异步的中间处理函数
        def on_products_cell_changed(row, column):
            asyncio.create_task(self.on_products_cell_changed(row, column))
        
        # 连接单元格编辑完成信号到中间处理函数
        self.products_table.cellChanged.connect(on_products_cell_changed)
        
        # 存储编辑前的值，用于比较变化
        self.products_previous_value = None
        self.products_table.cellDoubleClicked.connect(self.store_products_previous_value)

        # 所有UI组件创建完成后，连接信号
        self.connect_signals()

    def show_stock_sync_dialog(self):
        """显示库存同步对话框"""
        # 获取当前搜索框中的关键字
        keyword = self.keyword_input.text().strip()
        # 创建对话框并传递关键字参数
        self.stock_sync_dialog = StockSyncDialog(self, initial_keyword=keyword)
        self.stock_sync_dialog.show()

    def handle_text_edit_keypress(self, event, text_edit):
        """处理TextEdit的回车键按下事件"""
        if event.key() == Qt.Key_Return and not event.modifiers() & Qt.ShiftModifier:
            # 只在按下回车且没有按住Shift键时触发更新
            # 如果字数大于45就提示
            if len(text_edit.toPlainText()) > 45:
                InfoBar.warning(
                    title='提示',
                    content='商品名称字数不能超过45个字',
                    parent=self
                )
                return
            asyncio.create_task(self.update_all_stores_name(text_edit.toPlainText()))
            # 阻止回车键的默认行为
            event.accept()
        else:
            # 对于其他按键，保持默认行为
            super(TextEdit, text_edit).keyPressEvent(event)

    def on_master_store_item_changed(self, item):
        """处理总店表格单元格内容改变事件"""
        self.update_product_name_header(item.toPlainText())

    def store_products_previous_value(self, row, column):
        """存储编辑前的单元格值"""
        item = self.products_table.item(row, column)
        self.products_previous_value = item.text() if item else None

    async def on_products_cell_changed(self, row, column):
        """处理商品明细表格单元格编辑完成事件"""
        try:
            if self.products_previous_value is None:
                return

            new_value = self.products_table.item(row, column).text()
            if new_value == self.products_previous_value:
                return

            # 获取商品信息
            product_name = self.products_table.item(row, 2).text()
            product_id = self.products_table.item(row, 4).text()
            store_info = self.products_table.item(row, 5).text()  # 格式: "门店名称 (平台)"
            
            # 解析门店信息
            store_name = store_info.split(' (')[0]
            platform = store_info.split('(')[1].rstrip(')')

            # 获取当前商品数据
            current_product = None
            for product in self.all_products:
                if str(product['id']) == product_id and product['store']['name'] == store_name:
                    current_product = product
                    break

            if not current_product:
                raise Exception("未找到对应的商品数据")

            store = {
                'id': current_product['store']['id'],
                'name': store_name,
                'platform': platform,
                'cookies': None
            }
            api = await self.product_sync.get_api_instance(store)

            # 根据列类型处理不同的数据更新
            if column == 2:  # 商品名称列
                if platform == '美团':
                    # 调用美团API修改商品名称
                    if not api:
                        raise Exception(f"未找到门店 {store_name} 的API实例")

                    result = await api.update_name(product_id, new_value)
                    if not result or result.get('code') != 0:
                        error_msg = result.get('msg', '未知错误') if result else '接口调用失败'
                        raise Exception(f"修改美团商品名称失败: {error_msg}")
                
                elif platform == '饿了么':
                    # 调用饿了么API修改商品名称
                    if not api:
                        raise Exception(f"未找到门店 {store_name} 的API实例")

                    updates = {
                        "title": new_value
                    }
                    result = await api.api.product.update_product(product_id, updates)
                    # 饿了么API返回布尔值，True表示成功
                    if not result:
                        error_msg = '修改失败'
                        raise Exception(f"修改饿了么商品名称失败: {error_msg}")
                InfoBar.success(
                    title='成功',
                    content=f'修改商品 {product_name} 的名称为 {new_value}',
                    parent=self
                )
                
            elif column == 6:  # 原价列
                try:
                    old_value = self.products_previous_value.replace('¥', '')
                    new_value = new_value.replace('¥', '')
                    await self.update_all_stores_price(float(new_value), products=[current_product],row=row)
                except Exception as e:
                    raise
                
            elif column == 7:  # 折扣价列
                try:
                    old_value = self.products_previous_value.replace('¥', '')
                    new_value = new_value.replace('¥', '')
                    spec_value = self.products_table.item(row, 3).text()
                    await self.update_all_stores_discount_price(float(new_value), products=[current_product], spec_value=spec_value,row=row)
                except Exception as e:
                    raise
                
            elif column == 8:  # 库存列
                try:
                    old_value = self.products_previous_value
                    await self.update_all_stores_stock(int(new_value), products=[current_product],row=row)
                except Exception as e:
                    raise

            elif column == 9:  # 限购数量列
                try:
                    old_value = self.products_previous_value
                    await self.update_all_stores_order_limit(int(new_value), products=[current_product],row=row)
                except Exception as e:
                    raise
                
            elif column == 10:  # 折扣排序列
                try:
                    old_value = self.products_previous_value
                    await self.update_all_stores_sort_index(int(new_value), products=[current_product],row=row)
                except Exception as e:
                    raise
                
            elif column == 11:  # 起购数量列
                try:
                    old_value = self.products_previous_value
                    await self.update_all_stores_min_order_count(int(new_value), products=[current_product],row=row)
                except Exception as e:
                    raise

        except ValueError as e:
            error_msg = f'输入的值格式不正确: {str(e)}'
            InfoBar.error(
                title='错误',
                content=error_msg,
                parent=self
            )
            # 恢复原值
            item = self.products_table.item(row, column)
            item.setText(self.products_previous_value)
        except Exception as e:
            error_msg = str(e)
            InfoBar.error(
                title='错误',
                content=error_msg,
                parent=self
            )
            # 恢复原值
            item = self.products_table.item(row, column)
            item.setText(self.products_previous_value)
        finally:
            self.products_previous_value = None

    def connect_signals(self):
        """连接所有信号"""
        self.store_combo.currentIndexChanged.connect(self.on_store_changed)
        self.clear_btn.clicked.connect(self.on_clear_clicked)
        # 移除重复的搜索触发
        if not hasattr(self, '_search_connected'):
            self.search_btn.clicked.connect(self.on_search_clicked_wrapper)
            self.keyword_input.returnPressed.connect(self.on_search_clicked_wrapper)
            self._search_connected = True

    async def on_status_clicked(self, row: int, current_status: str):
        """状态按钮点击处理"""
        try:
            # 获取商品信息
            product_name = self.products_table.item(row, 2).text()
            product_id = self.products_table.item(row, 4).text()
            store_info = self.products_table.item(row, 5).text()  # 格式: "门店名称 (平台)"
            spec_value = self.products_table.item(row, 3).text()  # 获取规格值
            
            # 解析门店信息
            store_name = store_info.split(' (')[0]
            platform = store_info.split('(')[1].rstrip(')')
            
            # 获取当前商品数据
            current_product = None
            for product in self.all_products:
                if str(product['id']) == product_id and product['store']['name'] == store_name:
                    current_product = product
                    break

            if not current_product:
                raise Exception("未找到对应的商品数据")

            # 获取SKU信息
            sku = self.get_product_sku(current_product, spec_value)
            if not sku:
                raise Exception("商品没有SKU信息")
            if platform == '美团':
                sku_id = str(sku['id'])
            else:  # 饿了么
                sku_id = str(sku.get('itemSkuId', sku.get('id')))

            # 构建store对象以获取API实例
            store = {
                'id': current_product['store']['id'],
                'name': store_name,
                'platform': platform,
                'cookies': None
            }

            # 获取API实例
            api = await self.product_sync.get_api_instance(store)
            if not api:
                raise Exception(f"未找到门店API实例: {store_name}")

            print('current_status',current_status)
            # 根据平台调用不同的API
            if platform == '美团':
                # 美团: 0-上架，1-下架
                new_status = 0 if current_status == '上架' else 1
                result = await api.update_sell_status(
                    spu_ids=product_id,
                    sku_ids=sku_id,
                    sell_status=new_status
                )
                success = result.get('msg', '') == 'success'
            else:  # 饿了么
                # 饿了么: 0-上架，-2-下架
                new_status = 0 if current_status == '上架' else -2
                result = await api.api.product.batch_update_products(
                    product_ids=[product_id],
                    status=new_status
                )
                success = result.get(product_id, False)

            if success:
                # 更新按钮状态
                status_widget = self.products_table.cellWidget(row, 11)
                if status_widget:
                    status_btn = status_widget.layout().itemAt(0).widget()
                    new_text = '下架' if current_status == '上架' else '上架'
                    status_btn.setText(new_text)
                    
                    # 更新按钮样式
                    if current_status == '上架':  # 切换到在线状态 - 绿色
                        status_btn.setStyleSheet("""
                            QPushButton {
                                color: white;
                                background-color: #52c41a;
                                border: none;
                                border-radius: 4px;
                                padding: 4px 12px;
                                min-width: 60px;
                            }
                            QPushButton:hover {
                                background-color: #73d13d;
                            }
                            QPushButton:pressed {
                                background-color: #389e0d;
                            }
                        """)
                    else:  # 切换到下架状态 - 红色
                        status_btn.setStyleSheet("""
                            QPushButton {
                                color: white;
                                background-color: #ff4d4f;
                                border: none;
                                border-radius: 4px;
                                padding: 4px 12px;
                                min-width: 60px;
                            }
                            QPushButton:hover {
                                background-color: #ff7875;
                            }
                            QPushButton:pressed {
                                background-color: #d9363e;
                            }
                        """)
                    
                    # 更新商品数据中的状态
                    if platform == '美团':
                        new_sell_status = 0 if current_status == '上架' else 1
                    else:  # 饿了么
                        new_sell_status = 0 if current_status == '上架' else -2
                    
                    # 更新商品数据
                    sku['sellStatus'] = new_sell_status

                # 显示成功消息
                InfoBar.success(
                    title='成功',
                    content=f'商品已{"上架" if current_status == "上架" else "下架"}',
                    parent=self
                )

            else:
                raise Exception("API调用失败")

        except Exception as e:
            default_logger.error(f"修改商品状态失败: {e}")
            InfoBar.error(
                title='错误',
                content=f'修改商品状态失败: {str(e)}',
                parent=self
            )

    async def on_edit_clicked(self, row: int):
        """编辑按钮点击处理"""
        try:
            # 获取商品信息
            product_name = self.products_table.item(row, 2).text()
            product_id = self.products_table.item(row, 4).text()
            store_info = self.products_table.item(row, 5).text()  # 格式: "门店名称 (平台)"
            
            # 解析门店信息
            print('store_info',store_info)
            store_name = store_info.split(' (')[0]
            platform = store_info.split('(')[1].rstrip(')')
            
            if platform == '美团':
                # 获取门店cookies
                cookies = self.get_store_cookies(store_name,platform)
                if not cookies:
                    raise Exception(f"未找到门店 {store_name} 的cookies")
                
                # 获取wmPoiId
                wm_poi_id = cookies.get('wmPoiId')
                if not wm_poi_id:
                    raise Exception(f"未找到门店 {store_name} 的wmPoiId")
                
                # 打开美团编辑页面
                await self.open_meituan_edit_page(product_id, wm_poi_id, cookies, store_name)
            else:  # 饿了么平台
                # 获取门店cookies
                cookies = self.get_store_cookies(store_name,platform)
                if not cookies:
                    raise Exception(f"未找到门店 {store_name} 的cookies")
                
                # 打开饿了么编辑页面
                await self.open_eleme_edit_page(product_id, cookies, store_name)

        except Exception as e:
            default_logger.error(f"编辑商品失败: {e}")
            InfoBar.error(
                title='错误',
                content=f'编辑商品失败: {str(e)}',
                parent=self
            )

    async def on_discount_clicked(self, row: int):
        """折扣按钮点击处理"""
        try:
            # 获取商品信息
            product_name = self.products_table.item(row, 2).text()
            product_id = self.products_table.item(row, 4).text()
            store_info = self.products_table.item(row, 5).text()  # 格式: "门店名称 (平台)"
            
            # 解析门店信息
            store_name = store_info.split(' (')[0]
            platform = store_info.split('(')[1].rstrip(')')
            
            # 获取门店cookies
            cookies = self.get_store_cookies(store_name, platform)
            if not cookies:
                raise Exception(f"未找到门店 {store_name} 的cookies")
            
            if platform == '美团':
                # 获取wmPoiId和acctId
                wm_poi_id = cookies.get('wmPoiId')
                acct_id = cookies.get('acctId')
                if not wm_poi_id or not acct_id:
                    raise Exception(f"未找到门店 {store_name} 的wmPoiId或acctId")
                
                # 构建URL
                url = f"https://waimaieapp.meituan.com/marketing/shangou/activity/pc/merchant/discount/list?wmPoiId={wm_poi_id}&acctId={acct_id}"
                
                # 创建并显示编辑窗口
                edit_window = MeituanEditWindow(url, cookies, store_name, "美团", self)
                self.edit_windows.append(edit_window)
                edit_window.show()
                
            else:  # 饿了么平台
                # 获取当前商品数据
                current_product = None
                for product in self.all_products:
                    if str(product['id']) == product_id and product['store']['name'] == store_name:
                        current_product = product
                        break
                
                if not current_product:
                    raise Exception("未找到对应的商品数据")
                
                # 构建store对象以获取API实例
                store = {
                    'id': current_product['store']['id'],
                    'name': store_name,
                    'platform': platform,
                    'cookies': cookies
                }
                
                # 获取API实例
                api = await self.product_sync.get_api_instance(store)
                if not api:
                    raise Exception(f"未找到门店API实例: {store_name}")
                
                # 获取activity_id
                activity_id = api.api.discount.activity_id
                if not activity_id:
                    raise Exception(f"未找到门店 {store_name} 的activity_id")
                
                # 构建URL
                url = f"https://nr.ele.me/eleme_nr_bfe_retail/eb_common#/pc/shopActivitiesPc/specialOffer/v2/detail/{activity_id}"
                
                # 创建并显示编辑窗口
                edit_window = MeituanEditWindow(url, cookies, store_name, "饿了么", self)
                self.edit_windows.append(edit_window)
                edit_window.show()
            
            InfoBar.success(
                title='提示',
                content=f'已打开{store_name}的折扣编辑页面',
                parent=self
            )
            
        except Exception as e:
            default_logger.error(f"打开折扣编辑页面失败: {e}")
            InfoBar.error(
                title='错误',
                content=f'打开折扣编辑页面失败: {str(e)}',
                parent=self
            )

    def on_clear_clicked(self):
        """清空按钮点击处理"""
        self.store_combo.setCurrentIndex(0)  # 选择"全部门店"
        self.keyword_input.clear()

    @asyncSlot()
    async def on_search_clicked_wrapper(self):
        """搜索按钮点击处理的包装器"""
        # 防止重复触发
        if hasattr(self, '_is_searching') and self._is_searching:
            return
        self._is_searching = True
        try:
            default_logger.info("搜索触发 - 通过wrapper")
            await self.on_search_clicked()
        finally:
            self._is_searching = False

    def selectedItems(self):
        """获取选中的商品"""
        selected_items = []
        for row in range(self.products_table.rowCount()):
            checkbox = self.products_table.cellWidget(row, 0)
            if checkbox and checkbox.isChecked():
                selected_items.append(self.products_table.item(row, 1))
        return selected_items

    def get_selected_stores(self) -> List[Dict]:
        """获取选中的门店列表"""
        selected_store = self.store_combo.currentData()
        if not selected_store:
            return []

        # 如果是"全部门店"选项，返回所有门店
        if selected_store.get('id') == 'all':
            stores = []
            for i in range(1, self.store_combo.count()):
                store_data = self.store_combo.itemData(i)
                if store_data and store_data.get('id'):
                    stores.append(store_data)
            return stores

        # 返回选中的单个门店
        return [selected_store] if selected_store.get('id') else []
        
    async def on_search_clicked(self):
        """搜索按钮点击处理"""
        try:
            # 禁用界面
            self.setEnabled(False)
            self.setCursor(Qt.WaitCursor)
            
            # 获取搜索关键字
            keyword = self.keyword_input.text().strip()
            
            # 获取选中的门店
            selected_stores = self.get_selected_stores()
            if not selected_stores:
                InfoBar.warning(
                    title='提示',
                    content='请选择要搜索的门店',
                    parent=self
                )
                return
                
            # 清空当前数据
            self.all_products = []
            self.products_table.setRowCount(0)
            # 清除表格排序
            self.products_table.setSortingEnabled(False)
            
            # 创建所有门店的搜索任务并并行执行
            tasks = [self.product_sync.get_store_products(store, keyword) for store in selected_stores]
            products_list = await asyncio.gather(*tasks)
            
            # 合并所有结果
            for products in products_list:
                if products:
                    self.all_products.extend(products)
            
            # 更新表格显示
            if self.all_products:
                
                # 更新表格 - 使用asyncio.gather等待所有商品添加完成
                await asyncio.gather(*[self.add_product_to_table(product) for product in self.all_products])
                    
                # 调整列宽
                self.products_table.resizeColumnsToContents()
                self.products_table.horizontalHeader().setStretchLastSection(True)
                # 启用表格排序
                self.products_table.setSortingEnabled(True)
            else:
                InfoBar.warning(
                    title='提示',
                    content='未找到任何商品',
                    parent=self
                )
            
        except Exception as e:
            InfoBar.error(
                title='错误',
                content=f'搜索商品失败: {str(e)}',
                parent=self
            )
            default_logger.error(f"搜索商品失败: {e}")
            
        finally:
            # 恢复界面状态
            self.setEnabled(True)
            self.unsetCursor()
    
    def on_store_changed(self, index):
        """门店选择改变时的处理函数"""
        default_logger.info("门店选择改变")
        store_name = self.store_combo.currentText()
        store_id = self.store_combo.currentData()
        
        default_logger.info(f"门店切换: {store_name} (ID: {store_id})")
        
        # TODO: 根据选择的门店刷新商品列表

    def on_platform_changed(self, index):
        """平台选择改变时的处理函数"""
        platform = self.platform_combo.currentText()
        
        # 重新加载门店列表
        self.load_stores()

    def on_master_store_table_key_pressed(self, event):
        # 在这里处理按键事件
        # event.key() 可以获取按键代码
        # event.text() 可以获取按键文本
        print("Key pressed:", event.key(), event.text())
        # 获取当前选中的单元格
        current_item = self.master_store_table.currentItem()
        if current_item:
            row = current_item.row()
            column = current_item.column()
            self.on_cell_changed(row, column)

    # 创建一个非异步的中间处理函数
    def on_cell_changed(self,row, column):
            asyncio.create_task(self.on_master_store_cell_changed(row, column))

    def setup_master_store_table(self):
        """设置总店数据表格"""
        # 设置单元格可编辑
        self.master_store_table.setEditTriggers(TableWidget.DoubleClicked | TableWidget.EditKeyPressed)
        
        # 连接单元格编辑完成信号到中间处理函数
        self.master_store_table.cellChanged.connect(self.on_cell_changed)
        # 连接单元格回车键按下信号到中间处理函数
        self.master_store_table.keyPressEvent = self.on_master_store_table_key_pressed
        
        # 存储编辑前的值，用于比较变化
        self.previous_value = None
        self.master_store_table.cellDoubleClicked.connect(self.store_previous_value)

        # 设置状态按钮
        status_widget = QWidget()
        status_layout = QHBoxLayout()
        status_layout.setContentsMargins(0, 0, 0, 0)
        status_layout.setAlignment(Qt.AlignCenter)
        status_widget.setLayout(status_layout)

        # 创建上架和下架按钮
        online_btn = PushButton('上架', self)
        offline_btn = PushButton('下架', self)

        # 设置按钮样式
        online_btn.setStyleSheet("""
            QPushButton {
                color: white;
                background-color: #52c41a;
                border: none;
                border-radius: 4px;
                padding: 4px 12px;
                min-width: 60px;
            }
            QPushButton:hover {
                background-color: #73d13d;
            }
            QPushButton:pressed {
                background-color: #389e0d;
            }
        """)
        offline_btn.setStyleSheet("""
            QPushButton {
                color: white;
                background-color: #ff4d4f;
                border: none;
                border-radius: 4px;
                padding: 4px 12px;
                min-width: 60px;
            }
            QPushButton:hover {
                background-color: #ff7875;
            }
            QPushButton:pressed {
                background-color: #d9363e;
            }
        """)

        # 连接点击事件
        online_btn.clicked.connect(lambda: asyncio.create_task(self.on_master_status_clicked('上架')))
        offline_btn.clicked.connect(lambda: asyncio.create_task(self.on_master_status_clicked('下架')))

        status_layout.addWidget(online_btn)
        status_layout.addWidget(offline_btn)
        self.master_store_table.setCellWidget(0, 11, status_widget)

        # 设置删除按钮
        delete_widget = QWidget()
        delete_layout = QHBoxLayout()
        delete_layout.setContentsMargins(0, 0, 0, 0)
        delete_layout.setAlignment(Qt.AlignCenter)
        delete_widget.setLayout(delete_layout)

        delete_btn = PushButton('删除', self)
        delete_btn.setStyleSheet("""
            QPushButton {
                color: white;
                background-color: #ff4d4f;
                border: none;
                border-radius: 4px;
                padding: 4px 12px;
                min-width: 60px;
            }
            QPushButton:hover {
                background-color: #ff7875;
            }
            QPushButton:pressed {
                background-color: #d9363e;
            }
        """)
        delete_btn.clicked.connect(lambda: asyncio.create_task(self.on_master_delete_clicked()))
        delete_layout.addWidget(delete_btn)
        self.master_store_table.setCellWidget(0, 12, delete_widget)

        # 设置图片点击事件
        image_label = ImageLabel(self, event_loop=self.loop)
        # 断开默认的预览连接
        image_label.image_clicked.disconnect(image_label.show_preview)
        # 连接到图片管理对话框
        image_label.image_clicked.connect(
            lambda: asyncio.create_task(self.show_master_image_manager())
        )
        self.master_store_table.setCellWidget(0, 1, image_label)

        # 添加选择框并连接状态变化事件
        checkbox = QCheckBox(self)
        checkbox.stateChanged.connect(self.on_master_checkbox_changed)
        self.master_store_table.setCellWidget(0, 0, checkbox)

    async def show_master_image_manager(self):
        try:
            # 获取所有选中的店铺数据
            store_products = {}
            allcookies = {}
            spuid_shopname = {}
            pictures_shopname = {}
            ele_obj = {}
            cateId_shopname = {}
            
            # 获取所有门店
            selected_stores = []
            for i in range(1, self.store_combo.count()):  # 从1开始跳过"全部门店"
                store_data = self.store_combo.itemData(i)
                if store_data:
                    selected_stores.append(store_data)
            
            if not selected_stores:
                InfoBar.warning(
                    title='警告',
                    content='没有找到门店数据',
                    parent=self
                )
                return
            
            # 按店铺名称组织商品数据
            store_product_map = {}
            for product in self.all_products:
                store_name_platform = f"{product['store']['name']}-{product['store']['platform']}"
                if store_name_platform not in store_product_map:
                    store_product_map[store_name_platform] = []
                store_product_map[store_name_platform].append(product)
            
            for store in selected_stores:
                store_name_platform = f"{store['name']}-{store['platform']}"
                products = store_product_map.get(store_name_platform, [])
                if not products:
                    continue
                    
                store_products[store_name_platform] = products
                product = products[0]  # 获取第一个商品
                
                # 收集各店铺的数据
                if '美团' in store_name_platform:
                    allcookies[store_name_platform] = store['cookies']
                    spuid_shopname[store_name_platform] = product['id']
                    pictures_shopname[store_name_platform] = product.get('images', [''] * 10)  # 美团需要10个占位
                else:  # 饿了么
                    allcookies[store_name_platform] = store['cookies']
                    spuid_shopname[store_name_platform] = product['id']
                    pictures_shopname[store_name_platform] = product.get('images', [])
                    # 等待获取API实例
                    api = await self.product_sync.get_api_instance(store)
                    ele_obj[store_name_platform] = api
                    cateId_shopname[store_name_platform] = product.get('cateId', '')

            if not store_products:
                InfoBar.warning(
                    title='警告',
                    content='没有找到商品数据',
                    parent=self
                )
                return

            # 创建并显示图片上传对话框
            dialog = changePicture(
                allcookies=allcookies,
                spuid_shopname=spuid_shopname,
                pictures_shopname=pictures_shopname,
                ele_obj=ele_obj,
                cateId_shopname=cateId_shopname
            )
            dialog.show()  # 使用show()而不是exec_()

        except Exception as e:
            default_logger.error(f"显示图片管理对话框失败: {e}")
            InfoBar.error(
                title='错误',
                content=f'显示图片管理对话框失败: {str(e)}',
                parent=self
            )

    def on_master_checkbox_changed(self, state):
        """处理总店表格选择框状态变化"""
        try:
            # 遍历商品表格中的所有行
            for row in range(self.products_table.rowCount()):
                checkbox = self.products_table.cellWidget(row, 0)
                if checkbox:
                    checkbox.setChecked(state == Qt.Checked)
        except Exception as e:
            default_logger.error(f"更新商品选择状态失败: {e}")

    async def on_master_status_clicked(self, action: str):
        """处理总店状态按钮点击
        Args:
            action: '上架' 或 '下架'
        """
        try:
            # 获取选中的商品行数
            selected_rows = []
            for row in range(self.products_table.rowCount()):
                checkbox = self.products_table.cellWidget(row, 0)
                if checkbox and checkbox.isChecked():
                    selected_rows.append(row)

            if not selected_rows:
                InfoBar.warning(
                    title='提示',
                    content='请先选择要操作的商品',
                    parent=self
                )
                return

            success_count = 0
            error_count = 0

            # 遍历选中的商品行
            for row in selected_rows:
                try:
                    # 获取当前状态按钮
                    status_widget = self.products_table.cellWidget(row, 12)
                    if not status_widget:
                        continue

                    status_btn = status_widget.layout().itemAt(0).widget()
                    current_status = status_btn.text()

                    # 如果当前状态与目标操作相反，则执行操作
                    if (action == '下架' and current_status == '下架') or (action == '上架' and current_status == '上架'):
                        # 调用现有的状态切换方法
                        await self.on_status_clicked(row, current_status)
                        
                        # 更新按钮状态
                        new_text = '下架' if action == '上架' else '上架'
                        status_btn.setText(new_text)
                        
                        # 更新按钮样式
                        if action == '上架':  # 切换到在线状态 - 绿色
                            status_btn.setStyleSheet("""
                                QPushButton {
                                    color: white;
                                    background-color: #52c41a;
                                    border: none;
                                    border-radius: 4px;
                                    padding: 4px 12px;
                                    min-width: 60px;
                                }
                                QPushButton:hover {
                                    background-color: #73d13d;
                                }
                                QPushButton:pressed {
                                    background-color: #389e0d;
                                }
                            """)
                        else:  # 切换到下架状态 - 红色
                            status_btn.setStyleSheet("""
                                QPushButton {
                                    color: white;
                                    background-color: #ff4d4f;
                                    border: none;
                                    border-radius: 4px;
                                    padding: 4px 12px;
                                    min-width: 60px;
                                }
                                QPushButton:hover {
                                    background-color: #ff7875;
                                }
                                QPushButton:pressed {
                                    background-color: #d9363e;
                                }
                            """)
                        
                        success_count += 1
                    else:
                        # 跳过已经是目标状态的商品
                        continue

                except Exception as e:
                    error_count += 1
                    default_logger.error(f"处理第 {row} 行商品状态失败: {e}")
                    continue

            # 显示操作结果
            if error_count == 0:
                InfoBar.success(
                    title='成功',
                    content=f'成功{action} {success_count} 个商品',
                    parent=self
                )
            else:
                InfoBar.warning(
                    title='警告',
                    content=f'成功{action} {success_count} 个商品，失败 {error_count} 个',
                    parent=self
                )

        except Exception as e:
            default_logger.error(f"批量{action}失败: {e}")
            InfoBar.error(
                title='错误',
                content=f'批量{action}失败: {str(e)}',
                parent=self
            )

    def store_previous_value(self, row, column):
        """存储编辑前的单元格值"""
        item = self.master_store_table.item(row, column)
        self.previous_value = item.text() if item else None

    async def on_master_store_cell_changed(self, row, column):
        """处理总店表格单元格编辑完成事件"""
        try:
            if self.previous_value is None:
                return

            new_value = self.master_store_table.item(row, column).text()
            # if new_value == self.previous_value:
            #     return

            # 获取选中的商品
            selected_products = []
            selected_rows = []
            for row in range(self.products_table.rowCount()):
                checkbox = self.products_table.cellWidget(row, 0)
                if checkbox and checkbox.isChecked():
                    # 获取当前商品数据
                    product_id = self.products_table.item(row, 4).text()
                    store_info = self.products_table.item(row, 5).text()
                    store_name = store_info.split(' (')[0]
                    
                    # 在all_products中查找对应的商品
                    for product in self.all_products:
                        if str(product['id']) == product_id and product['store']['name'] == store_name:
                            selected_products.append(product)
                            selected_rows.append(row)
                            break

            if not selected_products:
                InfoBar.warning(
                    title='提示',
                    content='请先选择要修改的商品',
                    parent=self
                )
                # 恢复原值
                item = self.master_store_table.item(row, column)
                item.setText(self.previous_value)
                return

            # 根据列类型处理不同的数据更新
            if column == 5:  # 原价列
                await self.update_all_stores_price(float(new_value.replace('¥', '')), products=selected_products)
            elif column == 6:  # 折扣价列
                await self.update_all_stores_discount_price(float(new_value.replace('¥', '')), products=selected_products)
            elif column == 7:  # 库存列
                await self.update_all_stores_stock(int(new_value), products=selected_products)
            elif column == 8:  # 限购数量列
                await self.update_all_stores_order_limit(int(new_value), products=selected_products)
            elif column == 9:  # 折扣排序列
                await self.update_all_stores_sort_index(int(new_value), products=selected_products)
            elif column == 10:  # 起购数量列
                await self.update_all_stores_min_order_count(int(new_value), products=selected_products)

        except ValueError as e:
            InfoBar.error(
                title='错误',
                content=f'输入的值格式不正确: {str(e)}',
                parent=self,
            )
            # 恢复原值
            item = self.master_store_table.item(row, column)
            item.setText(self.previous_value)
        except Exception as e:
            InfoBar.error(
                title='错误',
                content=f'更新数据失败: {str(e)}',
                parent=self,
            )
            # 恢复原值
            item = self.master_store_table.item(row, column)
            item.setText(self.previous_value)
        finally:
            self.previous_value = None

    def update_product_name_header(self, product_name: str):
        """更新商品名称列头，显示字数"""
        char_count = len(product_name)
        self.master_store_table.setHorizontalHeaderItem(
            2, 
            QTableWidgetItem(f'商品名称 ({char_count}字)')
        )

    async def update_all_stores_name(self, new_name: str):
        """更新商品名称 - 并发版本
        Args:
            new_name: 新的商品名称
        """
        info_bar = InfoBar(
            icon=FluentIcon.SYNC,
            title='正在更新',
            content='正在更新商品名称，请稍候...',
            isClosable=False,
            duration=-1,
            position=InfoBarPosition.TOP,
            parent=self
        )
        
        try:
            success_count = 0
            error_count = 0
            error_details = []
            
            # 创建任务列表
            update_tasks = []
            
            # 按平台和店铺分组商品
            platform_store_products = {}
            
            # 遍历products_table中所有商品进行分组
            for row in range(self.products_table.rowCount()):
                product_name = self.products_table.item(row, 2).text()
                product_id = self.products_table.item(row, 4).text()
                store_info = self.products_table.item(row, 5).text()
                store_name = store_info.split(' (')[0]
                platform = store_info.split('(')[1].rstrip(')')
                
                # 获取当前商品数据
                current_product = None
                for product in self.all_products:
                    if str(product['id']) == product_id and product['store']['name'] == store_name:
                        current_product = product
                        break
                
                if not current_product:
                    error_msg = "未找到对应的商品数据"
                    error_details.append(f"{store_name}: {error_msg}")
                    error_count += 1
                    continue
                
                key = f"{platform}_{store_name}"
                if key not in platform_store_products:
                    platform_store_products[key] = {
                        'store': {
                            'id': current_product['store']['id'],
                            'name': store_name,
                            'platform': platform,
                            'cookies': None
                        },
                        'products': []
                    }
                platform_store_products[key]['products'].append({
                    'product': current_product,
                    'row': row
                })
                    
            # 为每个店铺创建一个更新任务
            async def update_store_products(store_info, products_info):
                api = await self.product_sync.get_api_instance(store_info)
                if not api:
                    return {
                        'success': False,
                        'store_name': store_info['name'],
                        'error': f"未找到门店 {store_info['name']} 的API实例",
                        'affected_rows': [p['row'] for p in products_info]
                    }
                
                results = []
                for product_info in products_info:
                    product = product_info['product']
                    try:
                        if store_info['platform'] == '美团':
                            result = await api.update_name(str(product['id']), new_name)
                            success = result and result.get('code') == 0
                            error = result.get('msg', '未知错误') if not success else None
                        else:  # 饿了么
                            updates = {"title": new_name}
                            result = await api.api.product.update_product(str(product['id']), updates)
                            # 饿了么API返回布尔值，True表示成功
                            success = bool(result)
                            error = '修改失败' if not success else None
                        
                        results.append({
                            'success': success,
                            'row': product_info['row'],
                            'error': error
                        })
                    except Exception as e:
                        results.append({
                            'success': False,
                            'row': product_info['row'],
                            'error': str(e)
                        })
                
                return {
                    'store_name': store_info['name'],
                    'results': results
                }
            
            # 创建并发任务
            update_tasks = [
                update_store_products(store_data['store'], store_data['products'])
                for store_data in platform_store_products.values()
            ]
            
            # 并发执行所有任务
            results = await asyncio.gather(*update_tasks, return_exceptions=True)
            
            # 处理结果
            for store_result in results:
                if isinstance(store_result, Exception):
                    error_count += 1
                    error_details.append(f"执行失败: {str(store_result)}")
                    continue
                    
                store_name = store_result['store_name']
                for result in store_result['results']:
                    if result['success']:
                        success_count += 1
                        # 更新表格显示
                        row = result['row']
                        item = self.products_table.item(row, 2)
                        if item:
                            old_name = item.text()
                            item.setText(new_name)
                            default_logger.info(f"[界面更新] 第{row}行商品名称: {old_name} -> {new_name}")
                        else:
                            default_logger.warning(f"[界面更新] 第{row}行第2列的item为空")
                    else:
                        error_count += 1
                        error_details.append(f"{store_name}: {result['error']}")
            
            # 关闭加载动画
            info_bar.close()

            # 强制刷新表格显示
            self.products_table.viewport().update()
            default_logger.info(f"[界面更新] 批量修改完成，强制刷新表格")

            total_count = self.products_table.rowCount()
            if error_count == 0:
                InfoBar.success(
                    title='成功',
                    content=f'成功更新 {success_count} 个商品的名称',
                    parent=self
                )
            else:
                error_summary = '\n'.join(error_details)
                InfoBar.warning(
                    title='警告',
                    content=f'部分商品名称更新失败，成功 {success_count} 个，失败 {error_count} 个',
                    parent=self
                )
        except Exception as e:
            info_bar.close()
            raise e

    def get_product_sku(self, product, spec_value=None,platform=None):
        """根据规格值获取商品SKU
        Args:
            product: 商品数据
            spec_value: 规格值，如果为None则返回第一个SKU
        Returns:
            找到的SKU数据
        """
        if not product.get('wmProductSkus'):
            return None
            
        # 如果没有指定规格值，返回第一个SKU
        if not spec_value:
            return product['wmProductSkus'][0]
            
        # 查找匹配规格值的SKU
        for sku in product['wmProductSkus']:
            if platform == '美团':
                if sku.get('spec') == spec_value:
                    return sku
            else:
                if sku.get('salePropertyList'):
                    for sale_property in sku.get('salePropertyList'):
                        if sale_property['propText'] == "规格":
                            if sale_property['valueText'] == spec_value:
                                return sku
                else:
                    if sku.get('spec') == spec_value:
                        return sku  
                
        # 如果找不到匹配的规格，返回第一个SKU
        return product['wmProductSkus'][0]

    async def update_all_stores_price(self, new_price: float, products=None,row=None):
        """更新商品原价
        Args:
            new_price: 新的原价
            products: 要更新的商品列表，如果为None则更新所有商品
        """
        # 显示加载动画
        info_bar = InfoBar(
            icon=FluentIcon.SYNC,
            title='正在更新',
            content='正在更新商品原价，请稍候...',
            isClosable=False,
            duration=-1,
            position=InfoBarPosition.TOP,
            parent=self
        )
        
        # 临时断开表格信号连接，避免循环触发
        self.products_table.blockSignals(True)
        
        try:
            success_count = 0
            error_count = 0
            error_details = []
            
            # 使用指定的商品列表或所有商品
            target_products = products if products is not None else self.all_products
            # if row is  None:
            #     # 弹窗提示：有多规格商品，不能批量修改
            #     InfoBar.warning(
            #         title='警告',
            #         content='有多规格商品，不能批量修改',
            #         parent=self,
                    
            #     )
            # 遍历商品
            if row is  None:
                row = 0
            for product in target_products:
                try:
                    # 获取商品基本信息
                    store_info = product.get('store', {})
                    store_name = store_info.get('name', '')
                    platform = store_info.get('platform', '')
                    store_id = store_info.get('id')
                    product_name = product.get('name', '未知商品')
                    product_id = str(product.get('id', ''))
                    
                    if not store_id:
                        error_msg = f"未找到门店ID: {store_name} ({platform})"
                        default_logger.error(error_msg)
                        error_count += 1
                        error_details.append(f"{store_name}: {error_msg}")
                        continue
                
                    # 获取商品SKU ID
                    # 从products_table中获取规格值
                    spec_value = None
                    
                    spec_value = self.products_table.item(row, 3).text()

                    sku = self.get_product_sku(product, spec_value,platform)
                    if not sku:
                        error_msg = f"商品 {product_name} 没有SKU信息"
                        default_logger.warning(error_msg)
                        error_details.append(f"{store_name}: {error_msg}")
                        continue
                        
                    if platform == '美团':
                        sku_id = sku['id']
                    else:
                        if sku.get('salePropertyList'):
                            sku_id = sku['itemSkuId']
                        else:
                            sku_id = sku['id']

                    # 构建store对象以获取API实例
                    store = {
                        'id': store_id,
                        'name': store_name,
                        'platform': platform,
                        'cookies': None
                    }

                    # 获取或创建API实例
                    api = await self.product_sync.get_api_instance(store)
                    if not api:
                        error_msg = f"未找到门店API实例: {store_name} ({store_id})"
                        default_logger.error(error_msg)
                        error_count += 1
                        error_details.append(f"{store_name}: {error_msg}")
                        continue

                    # 根据平台调用不同的API
                    if platform == '美团':
                        default_logger.info(f"调用美团门店{store_name}API实例：{store_id}")
                        result = await api.update_price(sku_id, new_price)
                        if not result or result.get('code') != 0:
                            error_msg = result.get('msg', '未知错误') if result else '接口调用失败'
                            error_details.append(f"{store_name}: {error_msg}")
                            error_count += 1
                            continue
                        success_count += 1
                    else:  # 饿了么
                        default_logger.info(f"调用饿了么门店{store_name}API实例：{store_id}")
                        data = {
                            "price": new_price
                        }
                        if len(product['wmProductSkus']) == 1:
                            item_id = product.get('id')
                            result = await api.api.product.update_product(item_id, data)
                            if not result:
                                error_msg = f'修改{store_name}商品原价失败'
                                error_details.append(f"{store_name}: {error_msg}")
                                error_count += 1
                                continue
                            success_count += 1
                        else:  # 多规格商品调用更新sku
                            # 准备所有规格的更新数据
                            skus_info = []
                            for sku in product['wmProductSkus']:
                                if sku['itemSkuId'] == sku_id:
                                    sku_info = {
                                        'itemSkuId': sku.get('itemSkuId'),
                                        'barCode': sku.get('barCode', ''),
                                        'productSkuId': sku.get('productSkuId', ''),
                                        'price': new_price,
                                        'quantity': int(sku.get('quantity', 0)),
                                        'skuOuterId': sku.get('skuOuterId', '')
                                }
                                else:
                                    sku_info = {
                                        'itemSkuId': sku.get('itemSkuId'),
                                        'barCode': sku.get('barCode', ''),
                                        'productSkuId': sku.get('productSkuId', ''),
                                        'price': sku.get('price', ''),
                                        'quantity': int(sku.get('quantity', 0)),
                                        'skuOuterId': sku.get('skuOuterId', '')
                                    }
                                
                                # 复制销售属性（如果有）
                                if 'salePropertyList' in sku:
                                    sku_info['salePropertyList'] = sku['salePropertyList']
                                
                                skus_info.append(sku_info)
                            
                            # 调用批量更新方法
                            result = await api.api.product.update_product_skus(
                                item_id=product_id,
                                skus_info=skus_info
                            )
                            
                            if not result:
                                error_msg = f'修改{store_name}多规格商品原价失败'
                                error_details.append(f"{store_name}: {error_msg}")
                                error_count += 1
                                continue
                            success_count += 1
                    
                    # 更新表格显示

                    price_item = self.products_table.item(row, 6)
                    print('row',row)

                    if price_item:
                        price_item.setText(f"¥{new_price:.2f}")

                    row += 1

                except Exception as e:
                    error_msg = f"更新商品 {product.get('name')} 原价失败: {e}"
                    default_logger.error(error_msg)
                    error_count += 1
                    error_details.append(f"{store_name}: {str(e)}")
                    continue

            # 关闭加载动画
            info_bar.close()

            total_count = len(target_products)
            if error_count == 0:
                InfoBar.success(
                    title='成功',
                    content=f'成功更新 {success_count} 个商品的原价',
                    parent=self,

                )
            else:
                error_summary = '\n'.join(error_details)
                InfoBar.warning(
                    title='警告',
                    content=f'部分商品原价更新失败，成功 {success_count} 个，失败 {error_count} 个',
                    parent=self,

                )
        except Exception as e:
            # 确保加载动画被关闭
            info_bar.close()
            raise e
        finally:
            # 恢复表格信号连接
            self.products_table.blockSignals(False)

    async def update_all_stores_discount_price(self, new_price: float, products=None, spec_value=None, row=None):
        """更新商品折扣价
        Args:
            new_price: 新的折扣价
            products: 要更新的商品列表，如果为None则更新所有商品
            spec_value: 规格值
            row: 起始行号
        """
        # 显示加载动画
        info_bar = InfoBar(
            icon=FluentIcon.SYNC,
            title='正在更新',
            content='正在更新商品折扣价，请稍候...',
            isClosable=False,
            duration=-1,
            position=InfoBarPosition.TOP,
            parent=self
        )

        # 临时断开表格信号连接，避免循环触发
        self.products_table.blockSignals(True)
        
        try:
            success_count = 0
            error_count = 0
            error_details = []
            
            # 使用指定的商品列表或所有商品
            target_products = products if products is not None else self.all_products
            
            # 初始化行号（如果未提供）
            if row is None:
                row = 0
                
            # 遍历商品
            for product in target_products:
                try:
                    # 获取商品基本信息
                    store_info = product.get('store', {})
                    store_name = store_info.get('name', '')
                    platform = store_info.get('platform', '')
                    store_id = store_info.get('id')
                    product_name = product.get('name', '未知商品')
                    product_id = str(product.get('id', ''))
                    
                    if not store_id:
                        error_msg = f"未找到门店ID: {store_name} ({platform})"
                        default_logger.error(error_msg)
                        error_count += 1
                        error_details.append(f"{store_name}: {error_msg}")
                        continue
                
                    # 从products_table中获取规格值
                    if not spec_value:
                        spec_value = self.products_table.item(row, 3).text() if self.products_table.item(row, 3) else ""
                        
                    # 获取商品SKU
                    sku = self.get_product_sku(product, spec_value, platform)
                    if not sku:
                        error_msg = f"商品 {product_name} 没有SKU信息"
                        default_logger.warning(error_msg)
                        error_details.append(f"{store_name}: {error_msg}")
                        continue
                        
                    if platform == '美团':
                        sku_id = sku['id']
                    else:  # 饿了么
                        sku_id = sku.get('itemSkuId', sku.get('id'))

                    # 构建store对象以获取API实例
                    store = {
                        'id': store_id,
                        'name': store_name,
                        'platform': platform,
                        'cookies': None
                    }

                    # 获取或创建API实例
                    api = await self.product_sync.get_api_instance(store)
                    if not api:
                        error_msg = f"未找到门店API实例: {store_name} ({store_id})"
                        default_logger.error(error_msg)
                        error_count += 1
                        error_details.append(f"{store_name}: {error_msg}")
                        continue

                    # 根据平台调用不同的API
                    has_discount = float(sku.get('discount_price', 0)) > 0
                    
                    if platform == '美团':
                        default_logger.info(f"调用美团门店{store_name}API实例：{store_id}")
                        if has_discount:
                            result = await api.update_discount(
                                item_act_id=sku.get('itemActId', ''),
                                act_price=new_price,
                                store_id=store_id,
                                order_limit_count=sku.get('orderLimit', 0)
                            )
                            if not result or result.get('data', {}).get('failCount', 0) != 0:
                                error_msg = result.get('data', {}).get('errMessage', '未知错误') if result else '接口调用失败'
                                error_details.append(f"{store_name}: {error_msg}")
                                error_count += 1
                                continue
                        else:
                            # 创建新折扣
                            result = await api.create_discount(
                                discount_price=new_price,
                                origin_price=sku['price'],
                                itemName=product['name'],
                                spuId=product['id'],
                                wmSkuId=sku_id
                            )
                            if not result or result.get('msg', '') != '设置活动成功':
                                error_msg = result.get('msg', '未知错误') if result else '接口调用失败'
                                error_details.append(f"{store_name}: {error_msg}")
                                error_count += 1
                                continue
                        success_count += 1
                    else:  # 饿了么
                        default_logger.info(f"调用饿了么门店{store_name}API实例：{store_id}")
                        if has_discount:
                            # 更新已有折扣 - 使用正确的manage_discount方法
                            try:
                                # 获取条码 - 使用正确的直接匹配方式
                                target_sku = None
                                for sku_item in product['wmProductSkus']:
                                    if sku_item.get('itemSkuId', sku_item.get('id')) == sku_id:
                                        target_sku = sku_item
                                        break

                                if not target_sku:
                                    error_msg = f"未找到目标SKU: {sku_id}"
                                    error_details.append(f"{store_name}: {error_msg}")
                                    error_count += 1
                                    continue

                                # 从SKU中直接获取条码，支持两种字段名
                                bar_code = target_sku.get('barcode') or target_sku.get('barCode')
                                if not bar_code:
                                    error_msg = f"SKU {sku_id} 没有条码信息"
                                    error_details.append(f"{store_name}: {error_msg}")
                                    error_count += 1
                                    continue

                                default_logger.info(f"[饿了么折扣更新] 找到目标SKU: {sku_id}, 规格: {target_sku.get('spec', '')}, 条码: {bar_code}")

                                default_logger.info(f"[饿了么折扣更新] 更新已有折扣: upc={bar_code}, special_price={new_price}")
                                result = await api.api.discount.manage_discount(
                                    mode='edit',
                                    upc=bar_code,
                                    special_price=new_price,
                                    activity_limit_type=1,  # 每单限购
                                    activity_day_limit=999  # 默认限购数量
                                )
                                default_logger.info(f"[饿了么折扣更新] manage_discount返回结果: {result}")

                                if result.get('code', -1) != 0:
                                    error_msg = result.get('message', '未知错误') if result else '接口调用失败'
                                    error_details.append(f"{store_name}: {error_msg}")
                                    error_count += 1
                                    continue
                                success_count += 1
                            except Exception as e:
                                error_msg = f"调用manage_discount失败: {str(e)}"
                                default_logger.error(f"[饿了么折扣更新] {error_msg}")
                                error_details.append(f"{store_name}: {error_msg}")
                                error_count += 1
                                continue
                        else:
                            # 获取条码 - 使用正确的直接匹配方式
                            target_sku = None
                            for sku_item in product['wmProductSkus']:
                                if sku_item.get('itemSkuId', sku_item.get('id')) == sku_id:
                                    target_sku = sku_item
                                    break

                            if not target_sku:
                                error_msg = f"未找到目标SKU: {sku_id}"
                                error_details.append(f"{store_name}: {error_msg}")
                                error_count += 1
                                continue

                            # 从SKU中直接获取条码，支持两种字段名
                            bar_code = target_sku.get('barcode') or target_sku.get('barCode')
                            if not bar_code:
                                error_msg = f"SKU {sku_id} 没有条码信息"
                                error_details.append(f"{store_name}: {error_msg}")
                                error_count += 1
                                continue

                            default_logger.info(f"[饿了么折扣更新] 找到目标SKU: {sku_id}, 规格: {target_sku.get('spec', '')}, 条码: {bar_code}")
                            # 创建新折扣 - 使用manage_discount方法
                            try:
                                default_logger.info(f"[饿了么折扣更新] 创建新折扣: upc={bar_code}, special_price={new_price}")
                                result = await api.api.discount.manage_discount(
                                    mode='add',
                                    upc=bar_code,
                                    special_price=new_price
                                )
                                default_logger.info(f"[饿了么折扣更新] manage_discount返回结果: {result}")

                                if result.get('code', -1) != 0:
                                    error_msg = result.get('message', '未知错误') if result else '接口调用失败'
                                    error_details.append(f"{store_name}: {error_msg}")
                                    error_count += 1
                                    continue
                                success_count += 1
                            except Exception as e:
                                error_msg = f"调用manage_discount失败: {str(e)}"
                                default_logger.error(f"[饿了么折扣更新] {error_msg}")
                                error_details.append(f"{store_name}: {error_msg}")
                                error_count += 1
                                continue
                    
                    # 更新表格显示 - 使用直接行号而不是遍历
                    discount_item = self.products_table.item(row, 7)
                    if discount_item:
                        discount_item.setText(f"¥{new_price:.2f}")
                    
                    # 行号递增
                    row += 1
                        
                except Exception as e:
                    error_msg = f"更新商品 {product.get('name')} 折扣价失败: {e}"
                    default_logger.error(error_msg)
                    error_count += 1
                    error_details.append(f"{store_name}: {str(e)}")
                    continue

            # 关闭加载动画
            info_bar.close()

            total_count = len(target_products)
            if error_count == 0:
                InfoBar.success(
                    title='成功',
                    content=f'成功更新 {success_count} 个商品的折扣价',
                    parent=self,

                )
            else:
                error_summary = '\n'.join(error_details)
                InfoBar.warning(
                    title='警告',
                    content=f'部分商品折扣价更新失败，成功 {success_count} 个，失败 {error_count} 个',
                    parent=self,

                )
        except Exception as e:
            # 确保加载动画被关闭
            info_bar.close()
            raise e
        finally:
            # 恢复表格信号连接
            self.products_table.blockSignals(False)

    async def update_all_stores_stock(self, new_stock: int, products=None,row=None):
        """更新商品库存
        Args:
            new_stock: 新的库存数量
            products: 要更新的商品列表，如果为None则更新所有商品
        """
        # 显示加载动画
        info_bar = InfoBar(
            icon=FluentIcon.SYNC,
            title='正在更新',
            content='正在更新商品库存，请稍候...',
            isClosable=False,
            duration=-1,
            position=InfoBarPosition.TOP,
            parent=self
        )
        
        # 临时断开表格信号连接，避免循环触发
        self.products_table.blockSignals(True)
        
        try:
            success_count = 0
            error_count = 0
            error_details = []
            
            # 使用指定的商品列表或所有商品
            target_products = products if products is not None else self.all_products
            if row is  None:
                row = 0
            
            # 遍历商品
            for product in target_products:
                try:
                    # 获取商品基本信息
                    store_info = product.get('store', {})
                    store_name = store_info.get('name', '')
                    platform = store_info.get('platform', '')
                    store_id = store_info.get('id')
                    product_name = product.get('name', '未知商品')
                    product_id = str(product.get('id', ''))
                    
                    if not store_id:
                        error_msg = f"未找到门店ID: {store_name} ({platform})"
                        default_logger.error(error_msg)
                        error_count += 1
                        error_details.append(f"{store_name}: {error_msg}")
                        continue
                
                    # 从products_table中获取规格值
                    spec_value = self.products_table.item(row, 3).text()
                            
                    # 获取商品SKU
                    sku = self.get_product_sku(product, spec_value,platform)
                    if not sku:
                        error_msg = f"商品 {product_name} 没有SKU信息"
                        default_logger.warning(error_msg)
                        error_details.append(f"{store_name}: {error_msg}")
                        continue
                        
                    if platform == '美团':
                        sku_id = sku['id']
                    else:
                        if sku.get('itemSkuId'):
                            sku_id = sku['itemSkuId']
                        else:
                            sku_id = sku['id']

                    # 构建store对象以获取API实例
                    store = {
                        'id': store_id,
                        'name': store_name,
                        'platform': platform,
                        'cookies': None
                    }

                    # 获取或创建API实例
                    api = await self.product_sync.get_api_instance(store)
                    if not api:
                        error_msg = f"未找到门店API实例: {store_name} ({store_id})"
                        default_logger.error(error_msg)
                        error_count += 1
                        error_details.append(f"{store_name}: {error_msg}")
                        continue

                    # 根据平台调用不同的API
                    if platform == '美团':
                        default_logger.info(f"调用美团门店{store_name}API实例：{store_id}")
                        result = await api.update_stock(sku_id, new_stock)
                        if not result or result.get('msg', '') != '修改成功':
                            error_msg = result.get('msg', '未知错误') if result else '接口调用失败'
                            error_details.append(f"{store_name}: {error_msg}")
                            error_count += 1
                            continue
                        success_count += 1
                    else:  # 饿了么
                        default_logger.info(f"调用饿了么门店{store_name}API实例：{store_id}")
                        data = {
                            "quantity": new_stock
                        }
                        item_id = product.get('id')
                        result = await api.api.product.update_product(item_id, data)
                        if not result:
                            error_msg = '修改失败'
                            error_details.append(f"{store_name}: {error_msg}")
                            error_count += 1
                            continue
                        success_count += 1

                    # 更新表格显示
                    stock_item = self.products_table.item(row, 8)
                    if stock_item:
                        stock_item.setText(str(new_stock))
                    row += 1
                        
                except Exception as e:
                    error_msg = f"更新商品 {product.get('name')} 库存失败: {e}"
                    default_logger.error(error_msg)
                    error_count += 1
                    error_details.append(f"{store_name}: {str(e)}")
                    continue

            # 关闭加载动画
            info_bar.close()

            total_count = len(target_products)
            if error_count == 0:
                InfoBar.success(
                    title='成功',
                    content=f'成功更新 {success_count} 个商品的库存',
                    parent=self,

                )
            else:
                error_summary = '\n'.join(error_details)
                InfoBar.warning(
                    title='警告',
                    content=f'部分商品库存更新失败，成功 {success_count} 个，失败 {error_count} 个',
                    parent=self,

                )
        except Exception as e:
            # 确保加载动画被关闭
            info_bar.close()
            raise e
        finally:
            # 恢复表格信号连接
            self.products_table.blockSignals(False)

    async def update_all_stores_order_limit(self, new_limit: int, products=None,row=None):
        """更新商品限购数量
        Args:
            new_limit: 新的限购数量
            products: 要更新的商品列表，如果为None则更新所有商品
        """
        # 显示加载动画
        info_bar = InfoBar(
            icon=FluentIcon.SYNC,
            title='正在更新',
            content='正在更新商品限购数量，请稍候...',
            isClosable=False,
            duration=-1,
            position=InfoBarPosition.TOP,
            parent=self
        )
        # 临时断开表格信号连接，避免循环触发
        self.products_table.blockSignals(True)
        try:
            success_count = 0
            error_count = 0
            error_details = []
            
            # 使用指定的商品列表或所有商品
            target_products = products if products is not None else self.all_products
            if row is  None:
                row = 0
            
            # 遍历商品
            for product in target_products:
                try:
                    # 获取商品基本信息
                    store_info = product.get('store', {})
                    store_name = store_info.get('name', '')
                    platform = store_info.get('platform', '')
                    store_id = store_info.get('id')
                    product_name = product.get('name', '未知商品')
                    product_id = str(product.get('id', ''))
                    
                    if not store_id:
                        error_msg = f"未找到门店ID: {store_name} ({platform})"
                        default_logger.error(error_msg)
                        error_count += 1
                        error_details.append(f"{store_name}: {error_msg}")
                        continue
                
                    # 从products_table中获取规格值
                    spec_value = self.products_table.item(row, 3).text()
                            
                    # 获取商品SKU
                    sku = self.get_product_sku(product, spec_value,platform)
                    if not sku:
                        error_msg = f"商品 {product_name} 没有SKU信息"
                        default_logger.warning(error_msg)
                        error_details.append(f"{store_name}: {error_msg}")
                        continue
                        
                    # if platform == '美团':
                    #     sku_id = sku['id']
                    # else:
                    #     if sku.get('itemSkuId'):
                    #         sku_id = sku['itemSkuId']
                    #     else:
                    #         sku_id = sku['id']

                    # 构建store对象以获取API实例
                    store = {
                        'id': store_id,
                        'name': store_name,
                        'platform': platform,
                        'cookies': None
                    }

                    # 获取或创建API实例
                    api = await self.product_sync.get_api_instance(store)
                    if not api:
                        error_msg = f"未找到门店API实例: {store_name} ({store_id})"
                        default_logger.error(error_msg)
                        error_count += 1
                        error_details.append(f"{store_name}: {error_msg}")
                        continue

                    # 根据平台调用不同的API
                    if platform == '美团':
                        default_logger.info(f"调用美团门店{store_name}API实例：{store_id}")
                        if float(sku.get('discount_price', 0)) > 0:
                            # 如果有折扣，需要更新折扣的限购数量
                            result = await api.update_discount(
                                item_act_id=sku.get('itemActId', ''),
                                act_price=sku['discount_price'],
                                store_id=store_id,
                                order_limit_count=new_limit
                            )
                            if not result:
                                error_msg = '修改失败'
                                error_details.append(f"{store_name}: {error_msg}")
                                error_count += 1
                                continue
                            if  result.get('code') == 500:
                                error_msg = result.get('msg', '未知错误') if result else '接口调用失败'
                                error_details.append(f"{store_name}: {error_msg}")
                                error_count += 1
                                continue
                            if not result or result.get('data', {}).get('failCount', 0) != 0:
                                error_msg = result.get('data', {}).get('errMessage', '未知错误') if result else '接口调用失败'
                                error_details.append(f"{store_name}: {error_msg}")
                                error_count += 1
                                continue
                        success_count += 1
                    else:  # 饿了么
                        default_logger.info(f"调用饿了么门店{store_name}API实例：{store_id}")
                        discount_price = float(sku.get('discount_price', 0))
                        result = await api.api.discount.manage_discount(
                            mode='edit',
                            # sku_id=sku.get('id'),
                            upc=sku.get('barCode', sku.get('barcode', '')),
                            special_price=discount_price,
                            activity_limit_type=2,
                            activity_day_limit=new_limit
                        )
                        if not result:
                            error_msg = '修改失败'
                            error_details.append(f"{store_name}: {error_msg}")
                            error_count += 1
                            continue
                        success_count += 1
                    
                    # 更新表格显示
                    limit_item = self.products_table.item(row, 9)
                    if limit_item:
                        limit_item.setText(str(new_limit) if new_limit > 0 else "-")
                    row += 1

                except Exception as e:
                    error_msg = f"更新商品 {product.get('name')} 限购数量失败: {e}"
                    default_logger.error(error_msg)
                    error_count += 1
                    error_details.append(f"{store_name}: {str(e)}")
                    continue

            # 关闭加载动画
            info_bar.close()

            total_count = len(target_products)
            if error_count == 0:
                InfoBar.success(
                    title='成功',
                    content=f'成功更新 {success_count} 个商品的限购数量',
                    parent=self,

                )
            else:
                error_summary = '\n'.join(error_details)
                InfoBar.warning(
                    title='警告',
                    content=f'部分商品限购数量更新失败，成功 {success_count} 个，失败 {error_count} 个',
                    parent=self,
                    
                )
        except Exception as e:
            # 确保加载动画被关闭
            info_bar.close()
            raise e
        finally:
            # 恢复表格信号连接
            self.products_table.blockSignals(False)

    async def update_all_stores_sort_index(self, new_index: int, products=None,row=None):
        """更新商品折扣排序
        Args:
            new_index: 新的排序索引
            products: 要更新的商品列表，如果为None则更新所有商品
        """
        # 显示加载动画
        info_bar = InfoBar(
            icon=FluentIcon.SYNC,
            title='正在更新',
            content='正在更新商品折扣排序，请稍候...',
            isClosable=False,
            duration=-1,
            position=InfoBarPosition.TOP,
            parent=self
        )
        # 临时断开表格信号连接，避免循环触发
        self.products_table.blockSignals(True)
        try:
            success_count = 0
            error_count = 0
            error_details = []
            
            # 使用指定的商品列表或所有商品
            target_products = products if products is not None else self.all_products
            
            if row is  None:
                row = 0
            
            # 遍历商品
            for product in target_products:
                try:
                    # 获取商品基本信息
                    store_info = product.get('store', {})
                    store_name = store_info.get('name', '')
                    platform = store_info.get('platform', '')
                    store_id = store_info.get('id')
                    product_name = product.get('name', '未知商品')
                    product_id = str(product.get('id', ''))
                    
                    if not store_id:
                        error_msg = f"未找到门店ID: {store_name} ({platform})"
                        default_logger.error(error_msg)
                        error_count += 1
                        error_details.append(f"{store_name}: {error_msg}")
                        continue

                    # 从products_table中获取规格值
                    spec_value = self.products_table.item(row, 3).text()
                            
                    # 获取商品SKU
                    sku = self.get_product_sku(product, spec_value,platform)
                    if not sku:
                        error_msg = f"商品 {product_name} 没有SKU信息"
                        default_logger.warning(error_msg)
                        error_details.append(f"{store_name}: {error_msg}")
                        continue
                        
                    if platform == '美团':
                        sku_id = sku['id']
                    else:  # 饿了么
                        sku_id = sku.get('itemSkuId', sku.get('id'))

                    # 构建store对象以获取API实例
                    store = {
                        'id': store_id,
                        'name': store_name,
                        'platform': platform,
                        'cookies': None
                    }

                    # 获取或创建API实例
                    api = await self.product_sync.get_api_instance(store)
                    if not api:
                        error_msg = f"未找到门店API实例: {store_name} ({store_id})"
                        default_logger.error(error_msg)
                        error_count += 1
                        error_details.append(f"{store_name}: {error_msg}")
                        continue

                    # 根据平台调用不同的API
                    if platform == '美团':
                        default_logger.info(f"调用美团门店{store_name}API实例：{store_id}")
                        # 在discounts表中获取排序
                        print(f"product_id: {product_id}")
                        with get_session_context() as session:
                            discount = session.query(Discount).filter_by(
                                store_id=store_id,
                                product_id=product_id
                            ).first()

                            # 如果找不到折扣记录
                            if not discount:
                                error_details.append(f"{store_name}: 未找到折扣记录")
                                error_count += 1
                                continue
                                
                            # 在session还活着的时候获取需要的值
                            itemact_id = discount.itemact_id
                    
                        result = await api.update_discount_sort(itemact_id, product_id, sku_id, new_index)
                        if not result or result.get('code') != 0:
                            error_msg = result.get('msg', '未知错误') if result else '接口调用失败'
                            error_details.append(f"{store_name}: {error_msg}")
                            error_count += 1
                            continue
                        success_count += 1
                    else:  # 饿了么
                        # 不涉及排序
                        success_count += 1
                    
                    # 更新表格显示
                    sort_item = self.products_table.item(row, 10)
                    if sort_item:
                        sort_item.setText(str(new_index) if new_index > 0 else "-")
                    row += 1
                        
                except Exception as e:
                    error_msg = f"更新商品 {product.get('name')} 折扣排序失败: {e}"
                    default_logger.error(error_msg)
                    error_count += 1
                    error_details.append(f"{store_name}: {str(e)}")
                    continue

            # 关闭加载动画
            info_bar.close()

            total_count = len(target_products)
            if error_count == 0:
                InfoBar.success(
                    title='成功',
                    content=f'成功更新 {success_count} 个商品的折扣排序',
                    parent=self,

                )
            else:
                error_summary = '\n'.join(error_details)
                InfoBar.warning(
                    title='警告',
                    content=f'部分商品折扣排序更新失败，成功 {success_count} 个，失败 {error_count} 个',
                    parent=self,
                    
                )
        except Exception as e:
            # 确保加载动画被关闭
            info_bar.close()
            raise e
        finally:
            # 恢复表格信号连接
            self.products_table.blockSignals(False)

    async def open_meituan_edit_page(self, spu_id: str, wm_poi_id: str, cookies: dict, store_name: str):
        """打开美团编辑页面"""
        try:
            # 构建URL
            url = f"https://shangoue.meituan.com/reuse/sc/product/views/core/pages/product/edit?spuId={spu_id}&wmPoiId={wm_poi_id}&from=single"
            
            # 创建并显示编辑窗口
            edit_window = MeituanEditWindow(url, cookies, store_name,"美团", self)
            self.edit_windows.append(edit_window)  # 保存窗口引用
            edit_window.show()
            
            InfoBar.success(
                    title='提示',
                content=f'已打开{store_name}的商品编辑页面，请在新窗口中修改起购数量',
                    parent=self
                )
        except Exception as e:
            default_logger.error(f"打开美团编辑页面失败: {e}")
            InfoBar.error(
                title='错误',
                content=f'打开美团编辑页面失败: {e}',
                parent=self
            )

    async def update_all_stores_min_order_count(self, new_count: int, products=None,row=None):
        """更新商品起购数量
        Args:
            new_count: 新的起购数量
            products: 要更新的商品列表，如果为None则更新所有商品
        """
        success_count = 0
        error_count = 0
        
        # 使用指定的商品列表或所有商品
        target_products = products if products is not None else self.all_products
        
        # 遍历商品
        for product in target_products:
            try:
                # 获取商品基本信息
                store_info = product.get('store', {})
                store_name = store_info.get('name', '')
                platform = store_info.get('platform', '')
                store_id = store_info.get('id')
                
                if not store_id:
                    default_logger.error(f"未找到门店ID: {store_name} ({platform})")
                    error_count += 1
                    continue

                # 获取商品SKU信息
                if not product.get('wmProductSkus'):
                    default_logger.warning(f"商品 {product.get('name')} 没有SKU信息")
                    continue

                # 构建store对象以获取API实例
                store = {
                    'id': store_id,
                    'name': store_name,
                    'platform': platform,
                    'cookies': None
                }

                # 获取或创建API实例
                api = await self.product_sync.get_api_instance(store)
                if not api:
                    default_logger.error(f"未找到门店API实例: {store_name} ({store_id})")
                    error_count += 1
                    continue

                # 根据平台调用不同的API
                if platform == '美团':
                    default_logger.info(f"调用美团门店{store_name}API实例：{store_id}")
                    # 获取wmPoiId和cookies
                    wm_poi_id = api.cookies.get('wmPoiId')
                    if not wm_poi_id:
                        default_logger.error(f"{store_name} 未找到wmPoiId")
                        error_count += 1
                        continue
                    
                    # 打开编辑页面
                    await self.open_meituan_edit_page(product['id'], wm_poi_id, api.cookies, store_name)
                    success_count += 1
                    # 由于是打开网页手动修改，这里直接跳过后续商品
                    break
                    
                else:  # 饿了么
                    default_logger.info(f"调用饿了么门店{store_name}API实例：{store_id}")
                    data = {
                        "purchaseQuantity": new_count
                    }
                    item_id = product.get('id')
                    success = await api.api.product.update_product(item_id, data)
                    default_logger.info(f"{store_name} 饿了么更新起购数量：{success}")

                    if success:
                        success_count += 1
                    else:
                        error_count += 1
                    
            except Exception as e:
                default_logger.error(f"更新商品 {product.get('name')} 起购数量失败: {e}")
                error_count += 1
                continue

        # 显示更新结果
        if platform == '美团':
            InfoBar.info(
                title='提示',
                content='请在打开的网页中修改起购数量',
                parent=self
            )
        else:
            if error_count == 0:
                InfoBar.success(
                    title='成功',
                    content=f'成功更新 {success_count} 个商品的起购数量',
                    parent=self
                )
            else:
                InfoBar.warning(
                    title='警告',
                    content=f'成功更新 {success_count} 个商品的起购数量，失败 {error_count} 个',
                    parent=self
                )

    def get_store_cookies(self, store_name: str,platform:str) -> dict:
        """根据店铺名称获取对应的cookies"""
        try:
            with open(self.cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
                
            for object_id, store_data in cache_data['stores'].items():
                if store_data['storeName'] in store_name and store_data['platform'] == platform:
                    return cache_data.get('cookies', {}).get(object_id, {})
                    
        except Exception as e:
            default_logger.error(f"获取店铺cookies失败: {e}")
            return {} 

    async def open_eleme_edit_page(self, item_id: str, cookies: dict, store_name: str):
        """打开饿了么编辑页面"""
        try:
            # 构建URL
            url = f"https://nr.ele.me/eleme-b-newretail/commodity_pc/ebai.html?gray=true&ebQuery=false&nav=agentGroup&ebGroup=commodity#/store/edit?itemId={item_id}"
            
            # 创建并显示编辑窗口
            edit_window = MeituanEditWindow(url, cookies, store_name, '饿了么', self)  # 复用MeituanEditWindow类
            self.edit_windows.append(edit_window)  # 保存窗口引用
            edit_window.show()
            
            InfoBar.success(
                title='提示',
                content=f'已打开{store_name}的商品编辑页面',
                parent=self
            )
        except Exception as e:
            default_logger.error(f"打开饿了么编辑页面失败: {e}")
            InfoBar.error(
                title='错误',
                content=f'打开饿了么编辑页面失败: {e}',
                parent=self
            )

    async def add_product_to_table(self, product):
        """添加商品到表格
        Args:
            product: 商品数据字典
        """
        try:
            # 获取商品基本信息
            product_name = product.get('name', '未知商品')
            store_name = product.get('store', {}).get('name', '未知门店')
            platform = product.get('store', {}).get('platform', '未知平台')

            default_logger.info(f"[表格显示] 开始添加商品到表格: {product_name} ({platform}), SKU数量: {len(product.get('wmProductSkus', []))}")

            # 遍历商品的所有规格
            for sku_index, sku in enumerate(product.get('wmProductSkus', [])):
                try:
                    row = self.products_table.rowCount()
                    self.products_table.insertRow(row)
                    
                    # 选择列 - 复选框
                    checkbox = QCheckBox()
                    checkbox.setChecked(True)
                    self.products_table.setCellWidget(row, 0, checkbox)
                    
                    # 图片列
                    image_label = ImageLabel(self, event_loop=self.loop)
                    image_url = product.get('image', '')
                    if image_url:
                        try:
                            # 处理图片URL
                            if '@' in image_url:  # 如果URL包含尺寸参数
                                image_url = image_url.split('@')[0]  # 移除尺寸参数
                            await image_label.set_image(image_url)
                        except Exception as e:
                            default_logger.error(f"加载商品图片失败: {image_url}, 错误: {str(e)}")
                            image_label._set_default_image()
                    self.products_table.setCellWidget(row, 1, image_label)
                    
                    # 商品名称
                    name_item = QTableWidgetItem(product_name)
                    name_item.setTextAlignment(Qt.AlignCenter)
                    self.products_table.setItem(row, 2, name_item)
                    
                    # 规格
                    if platform == '美团':
                        spec_text = sku.get('spec', '默认规格')
                        spec_item = QTableWidgetItem(spec_text)
                    else:
                        spec_text = '默认规格'
                        if sku.get('salePropertyList'):
                            for sale_property in sku.get('salePropertyList', []):
                                if sale_property['propText'] == '规格':
                                    spec_text = sale_property['valueText']
                                    break
                        else:
                            spec_text = sku.get('spec', '默认规格')
                        spec_item = QTableWidgetItem(spec_text)

                    default_logger.info(f"[表格显示] SKU {sku_index+1}: 规格={spec_text}, SKU_ID={sku.get('itemSkuId', sku.get('id'))}")
                    spec_item.setTextAlignment(Qt.AlignCenter)
                    self.products_table.setItem(row, 3, spec_item)
                    
                    # 商品ID
                    id_item = QTableWidgetItem(str(product['id']))
                    id_item.setTextAlignment(Qt.AlignCenter)
                    self.products_table.setItem(row, 4, id_item)
                    
                    # 所属门店
                    store_item = QTableWidgetItem(f"{store_name} ({platform})")
                    store_item.setTextAlignment(Qt.AlignCenter)
                    # 根据平台设置不同的背景色
                    if platform == '美团':
                        store_item.setBackground(QColor(248, 152, 0))  # 美团橙色，半透明
                    else:  # 饿了么
                        store_item.setBackground(QColor(0, 122, 255))  # 饿了么蓝色，半透明
                    self.products_table.setItem(row, 5, store_item)
                    
                    # 原价
                    price = float(sku.get('price', 0))
                    price_item = QTableWidgetItem(f"¥{price:.2f}")
                    price_item.setTextAlignment(Qt.AlignCenter)
                    self.products_table.setItem(row, 6, price_item)

                    # 折扣价
                    discount_price = float(sku.get('discount_price', 0))
                    if discount_price > 0:
                        discount_item = QTableWidgetItem(f"¥{discount_price:.2f}")
                        default_logger.info(f"[表格显示] SKU {sku_index+1}: 原价=¥{price:.2f}, 折扣价=¥{discount_price:.2f}")
                    else:
                        discount_item = QTableWidgetItem("-")
                        default_logger.info(f"[表格显示] SKU {sku_index+1}: 原价=¥{price:.2f}, 无折扣价")
                    discount_item.setTextAlignment(Qt.AlignCenter)
                    self.products_table.setItem(row, 7, discount_item)
                    
                    # 库存
                    if platform == '美团':
                        stock = int(sku.get('stock', 0))
                    else:
                        if sku.get('quantity'):
                            stock = int(sku.get('quantity', 0))
                        else:
                            stock = int(sku.get('stock', 0))
                    stock_item = QTableWidgetItem(str(stock))
                    stock_item.setTextAlignment(Qt.AlignCenter)
                    self.products_table.setItem(row, 8, stock_item)
                    
                    # 限购数量
                    limit = sku.get('order_limit', 0)
                    limit_item = QTableWidgetItem(str(limit) if limit > 0 else "-")
                    limit_item.setTextAlignment(Qt.AlignCenter)
                    self.products_table.setItem(row, 9, limit_item)
                    
                    # 折扣排序
                    sort_index = sku.get('sort_index', 0)
                    sort_item = QTableWidgetItem(str(sort_index) if sort_index > 0 else "-")
                    sort_item.setTextAlignment(Qt.AlignCenter)
                    self.products_table.setItem(row, 10, sort_item)
                    
                    # 起购数量
                    min_order = sku.get('minOrderCount', 1)
                    min_order_item = QTableWidgetItem(str(min_order))
                    min_order_item.setTextAlignment(Qt.AlignCenter)
                    self.products_table.setItem(row, 11, min_order_item)
                    
                    # 销售状态
                    status_widget = QWidget()
                    status_layout = QHBoxLayout()
                    status_layout.setContentsMargins(0, 0, 0, 0)
                    status_layout.setAlignment(Qt.AlignCenter)
                    status_widget.setLayout(status_layout)
                    
                    # 获取当前状态
                    is_online = sku.get('sellStatus', 0) == 0
                    
                    # 创建状态按钮并设置样式
                    status_btn = PushButton('下架' if is_online else '上架', self)
                    if is_online:
                        # 在线状态 - 绿色
                        status_btn.setStyleSheet("""
                            QPushButton {
                                color: white;
                                background-color: #52c41a;
                                border: none;
                                border-radius: 4px;
                                padding: 4px 12px;
                                min-width: 60px;
                            }
                            QPushButton:hover {
                                background-color: #73d13d;
                            }
                            QPushButton:pressed {
                                background-color: #389e0d;
                            }
                        """)
                    else:
                        # 下架状态 - 红色
                        status_btn.setStyleSheet("""
                            QPushButton {
                                color: white;
                                background-color: #ff4d4f;
                                border: none;
                                border-radius: 4px;
                                padding: 4px 12px;
                                min-width: 60px;
                            }
                            QPushButton:hover {
                                background-color: #ff7875;
                            }
                            QPushButton:pressed {
                                background-color: #d9363e;
                            }
                        """)
                    
                    # 连接点击事件
                    status_btn.clicked.connect(
                        lambda checked, r=row: 
                        asyncio.create_task(self.on_status_clicked(r, self.products_table.cellWidget(r, 12).layout().itemAt(0).widget().text()))
                    )
                    
                    status_layout.addWidget(status_btn)
                    self.products_table.setCellWidget(row, 12, status_widget)
                    
                    # 商品信息
                    product_info_widget = QWidget()
                    product_info_layout = QHBoxLayout(product_info_widget)
                    product_info_layout.setContentsMargins(0, 0, 0, 0)
                    product_info_layout.setAlignment(Qt.AlignCenter)
                    
                    edit_btn = PushButton('编辑', self)
                    edit_btn.clicked.connect(
                        lambda checked, r=row: asyncio.create_task(self.on_edit_clicked(r))
                    )
                    product_info_layout.addWidget(edit_btn)
                    self.products_table.setCellWidget(row, 13, product_info_widget)

                    # 折扣信息
                    discount_info_widget = QWidget()
                    discount_info_layout = QHBoxLayout(discount_info_widget)
                    discount_info_layout.setContentsMargins(0, 0, 0, 0)
                    discount_info_layout.setAlignment(Qt.AlignCenter)

                    discount_btn = PushButton('编辑', self)
                    discount_btn.clicked.connect(
                        lambda checked, r=row: asyncio.create_task(self.on_discount_clicked(r))
                    )
                    discount_info_layout.addWidget(discount_btn)
                    self.products_table.setCellWidget(row, 14, discount_info_widget)    
                    
                    # 设置行高
                    self.products_table.setRowHeight(row, 70)

                    # 在添加折扣信息后，添加删除按钮
                    delete_info_widget = QWidget()
                    delete_info_layout = QHBoxLayout(delete_info_widget)
                    delete_info_layout.setContentsMargins(0, 0, 0, 0)
                    delete_info_layout.setAlignment(Qt.AlignCenter)

                    delete_btn = PushButton('删除', self)
                    delete_btn.setStyleSheet("""
                        QPushButton {
                            color: white;
                            background-color: #ff4d4f;
                            border: none;
                            border-radius: 4px;
                            padding: 4px 12px;
                            min-width: 60px;
                        }
                        QPushButton:hover {
                            background-color: #ff7875;
                        }
                        QPushButton:pressed {
                            background-color: #d9363e;
                        }
                    """)
                    delete_btn.clicked.connect(
                        lambda checked, r=row: asyncio.create_task(self.on_delete_clicked(r))
                    )
                    delete_info_layout.addWidget(delete_btn)
                    self.products_table.setCellWidget(row, 15, delete_info_widget)

                    # 设置行高
                    self.products_table.setRowHeight(row, 70)

                except Exception as e:
                    default_logger.error(f"处理商品SKU数据失败: {str(e)}")
                    continue
                
        except Exception as e:
            default_logger.error(f"处理商品数据失败: {str(e)}")

    async def on_master_delete_clicked(self):
        """处理总店删除按钮点击"""
        try:
            # 获取选中的商品行数
            selected_rows = []
            for row in range(self.products_table.rowCount()):
                checkbox = self.products_table.cellWidget(row, 0)
                if checkbox and checkbox.isChecked():
                    # 保存行的所有必要信息，而不是仅保存行号
                    product_name_item = self.products_table.item(row, 2)
                    product_id_item = self.products_table.item(row, 4)
                    store_info_item = self.products_table.item(row, 5)
                    spec_value_item = self.products_table.item(row, 3)

                    product_name = product_name_item.text() if product_name_item else ""
                    product_id = product_id_item.text() if product_id_item else ""
                    store_info = store_info_item.text() if store_info_item else ""
                    spec_value = spec_value_item.text() if spec_value_item else ""

                    row_data = {
                        'row': row,
                        'product_name': product_name,
                        'product_id': product_id,
                        'store_info': store_info,
                        'spec_value': spec_value
                    }

                    # 添加调试日志
                    default_logger.info(f"选中行 {row}: 商品名={product_name}, ID={product_id}, 门店={store_info}, 规格={spec_value}")

                    # 只要有商品名称和商品ID就认为是有效数据
                    if product_name and product_id:
                        selected_rows.append(row_data)
                    else:
                        default_logger.warning(f"行 {row} 数据不完整，跳过: 商品名={product_name}, ID={product_id}")

            if not selected_rows:
                InfoBar.warning(
                    title='提示',
                    content='请先选择要删除的商品',
                    parent=self
                )
                return

            # 显示确认对话框
            reply = QMessageBox.question(
                self,
                '确认删除',
                '删除后不可恢复，是否确认删除选中的商品？',
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.No:
                return

            success_count = 0
            error_count = 0
            error_details = []
            rows_to_delete = []  # 用于记录需要删除的行数据

            # 遍历选中的商品行
            for row_data in selected_rows:
                try:
                    # 获取商品信息
                    product_name = row_data['product_name']
                    product_id = row_data['product_id']
                    store_info = row_data['store_info']
                    spec_value = row_data['spec_value']
                    row = row_data['row']

                    # 解析门店信息
                    store_name = store_info.split(' (')[0]
                    platform = store_info.split('(')[1].rstrip(')')

                    # 获取当前商品数据
                    current_product = None
                    for product in self.all_products:
                        if str(product['id']) == product_id and product['store']['name'] == store_name:
                            current_product = product
                            break

                    if not current_product:
                        raise Exception("未找到对应的商品数据")

                    # 获取SKU信息
                    sku = self.get_product_sku(current_product, spec_value)
                    if not sku:
                        raise Exception("商品没有SKU信息")
                    if platform == '美团':
                        sku_id = str(sku['id'])
                    else:  # 饿了么
                        sku_id = str(sku.get('itemSkuId', sku.get('id')))

                    # 构建store对象以获取API实例
                    store = {
                        'id': current_product['store']['id'],
                        'name': store_name,
                        'platform': platform,
                        'cookies': None
                    }

                    # 获取API实例
                    api = await self.product_sync.get_api_instance(store)
                    if not api:
                        raise Exception(f"未找到门店API实例: {store_name}")

                    # 根据平台调用不同的API
                    if platform == '美团':
                        result = await api.delete_product(sku_id)
                        if not result or result.get('code') != 0:
                            error_msg = result.get('msg', '未知错误') if result else '接口调用失败'
                            raise Exception(error_msg)
                    else:  # 饿了么
                        result = await api.api.product.delete_products([product_id])
                        if not result or not result.get(product_id, False):
                            error_msg = result.get('data', {}).get('errMessage', '删除失败')
                            raise Exception(error_msg)

                    # 删除成功，记录需要删除的行数据（用于后续匹配删除）
                    delete_record = {
                        'product_name': product_name,
                        'product_id': product_id,
                        'store_info': store_info,
                        'spec_value': spec_value
                    }
                    rows_to_delete.append(delete_record)
                    success_count += 1

                    # 添加调试日志
                    default_logger.info(f"API删除成功，记录删除数据: {delete_record}")

                except Exception as e:
                    error_count += 1
                    error_details.append(f"{store_name} - {product_name}: {str(e)}")
                    continue

            # 删除成功的行 - 使用更可靠的删除策略
            default_logger.info(f"开始删除表格行，需要删除 {len(rows_to_delete)} 行")
            default_logger.info(f"当前表格总行数: {self.products_table.rowCount()}")

            # 方案1：直接使用原始行号删除（从后向前）
            # 收集所有成功删除的原始行号
            original_rows_to_delete = []
            for delete_data in rows_to_delete:
                # 从选中的行数据中找到对应的原始行号
                for selected_row in selected_rows:
                    if (selected_row['product_name'] == delete_data['product_name'] and
                        selected_row['product_id'] == delete_data['product_id'] and
                        selected_row['store_info'] == delete_data['store_info'] and
                        selected_row['spec_value'] == delete_data['spec_value']):
                        original_rows_to_delete.append(selected_row['row'])
                        break

            # 从后向前删除原始行号
            for row in sorted(set(original_rows_to_delete), reverse=True):
                if row < self.products_table.rowCount():
                    default_logger.info(f"删除原始行号 {row}")
                    self.products_table.removeRow(row)
                else:
                    default_logger.warning(f"行号 {row} 超出范围，当前表格行数: {self.products_table.rowCount()}")

            # 方案2：如果方案1失败，使用数据匹配删除作为备用
            remaining_to_delete = []
            for delete_data in rows_to_delete:
                found = False
                for row in range(self.products_table.rowCount()):
                    product_name_item = self.products_table.item(row, 2)
                    product_id_item = self.products_table.item(row, 4)

                    if (product_name_item and product_id_item and
                        product_name_item.text().strip() == delete_data['product_name'].strip() and
                        product_id_item.text().strip() == delete_data['product_id'].strip()):
                        found = True
                        break

                if not found:
                    remaining_to_delete.append(delete_data)

            # 如果还有未删除的数据，使用数据匹配删除
            if remaining_to_delete:
                default_logger.warning(f"使用备用删除方案，还需删除 {len(remaining_to_delete)} 行")
                for delete_data in remaining_to_delete:
                    for row in range(self.products_table.rowCount() - 1, -1, -1):
                        product_name_item = self.products_table.item(row, 2)
                        product_id_item = self.products_table.item(row, 4)

                        if (product_name_item and product_id_item and
                            product_name_item.text().strip() == delete_data['product_name'].strip() and
                            product_id_item.text().strip() == delete_data['product_id'].strip()):
                            default_logger.info(f"备用方案删除行 {row}: {delete_data['product_name']}")
                            self.products_table.removeRow(row)
                            break

            default_logger.info(f"表格行删除完成，最终表格行数: {self.products_table.rowCount()}")

            # 强制刷新表格
            self.products_table.viewport().update()

            # 显示操作结果
            if error_count == 0:
                InfoBar.success(
                    title='成功',
                    content=f'成功删除 {success_count} 个商品',
                    parent=self
                )
            else:
                error_summary = '\n'.join(error_details)
                InfoBar.warning(
                    title='警告',
                    content=f'成功删除 {success_count} 个商品，失败 {error_count} 个\n失败原因：\n{error_summary}',
                    parent=self,
                    duration=-1  # 设置为-1表示永久显示，直到用户手动关闭
                )

        except Exception as e:
            default_logger.error(f"批量删除商品失败: {e}")
            InfoBar.error(
                title='错误',
                content=f'批量删除商品失败: {str(e)}',
                parent=self,
                duration=-1  # 设置为-1表示永久显示，直到用户手动关闭
            )

    async def on_delete_clicked(self, row: int):
        """处理单个商品删除按钮点击"""
        try:
            # 首先验证行号的有效性
            if row < 0 or row >= self.products_table.rowCount():
                default_logger.warning(f"无效的行号: {row}, 当前表格总行数: {self.products_table.rowCount()}")
                return
            
            # 获取商品信息，添加空值检查
            product_name_item = self.products_table.item(row, 2)
            product_id_item = self.products_table.item(row, 4)
            store_info_item = self.products_table.item(row, 5)
            spec_value_item = self.products_table.item(row, 3)
            
            # 检查所有必需的单元格是否存在
            if not all([product_name_item, product_id_item, store_info_item, spec_value_item]):
                default_logger.warning(f"行 {row} 的商品信息不完整")
                return
            
            product_name = product_name_item.text()
            product_id = product_id_item.text()
            store_info = store_info_item.text()  # 格式: "门店名称 (平台)"
            spec_value = spec_value_item.text()
            
            # 解析门店信息
            store_name = store_info.split(' (')[0]
            platform = store_info.split('(')[1].rstrip(')')

            # 显示确认对话框
            reply = QMessageBox.question(
                self,
                '确认删除',
                f'删除后不可恢复，是否确认删除商品 {product_name}？',
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.No:
                return

            # 获取当前商品数据
            current_product = None
            for product in self.all_products:
                if str(product['id']) == product_id and product['store']['name'] == store_name:
                    current_product = product
                    break

            if not current_product:
                raise Exception("未找到对应的商品数据")

            # 获取SKU信息
            sku = self.get_product_sku(current_product, spec_value)
            if not sku:
                raise Exception("商品没有SKU信息")
            if platform == '美团':
                sku_id = str(sku['id'])
            else:  # 饿了么
                sku_id = str(sku.get('itemSkuId', sku.get('id')))

            # 构建store对象以获取API实例
            store = {
                'id': current_product['store']['id'],
                'name': store_name,
                'platform': platform,
                'cookies': None
            }

            # 获取API实例
            api = await self.product_sync.get_api_instance(store)
            if not api:
                raise Exception(f"未找到门店API实例: {store_name}")

            # 根据平台调用不同的API
            if platform == '美团':
                result = await api.delete_product(sku_id)
                if not result or result.get('code') != 0:
                    error_msg = result.get('msg', '未知错误') if result else '接口调用失败'
                    raise Exception(error_msg)
            else:  # 饿了么
                result = await api.api.product.delete_products([product_id])
                if not result or not result.get(product_id, False):
                    error_msg = result.get('data', {}).get('errMessage', '删除失败')
                    raise Exception(error_msg)

            # 删除成功，从表格中移除该行
            self.products_table.removeRow(row)

            # 显示成功消息
            InfoBar.success(
                title='成功',
                content=f'商品 {product_name} 已删除',
                parent=self
            )

        except Exception as e:
            default_logger.error(f"删除商品失败: {e}")
            InfoBar.error(
                title='错误',
                content=f'删除商品失败: {str(e)}',
                parent=self,
                duration=-1  # 设置为-1表示永久显示，直到用户手动关闭
            )
