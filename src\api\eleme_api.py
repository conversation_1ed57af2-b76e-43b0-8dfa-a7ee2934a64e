"""
饿了么API兼容模块
提供与旧版API相同的接口，但使用新的实现
"""
import asyncio
from typing import Dict, Optional, Tuple
from .eleme import ElemeAPI
from utils.logger import default_logger
import json
import aiohttp

class Eleme:
    """饿了么API兼容类"""
    
    def __init__(self, cookie: Dict[str, str], shopname: str):
        """初始化API
        
        Args:
            cookie: cookie字典
            shopname: 店铺名称
        """
        self.api = ElemeAPI(cookie, shopname)
        self.sellerId = None
        self.storeId = None
        
    async def initialize(self):
        """异步初始化方法"""
        try:
            if await self.api.initialize():
                self.sellerId = self.api.seller_id
                self.storeId = self.api.store_id
                return True
            return False
        except Exception as e:
            default_logger.error(f"饿了么API初始化失败: {e}")
            return False
    
    def getShopUserInfo(self) -> Tuple[Optional[str], Optional[str]]:
        """获取店铺用户信息

        Returns:
            (seller_id, store_id)元组
        """
        return self.sellerId, self.storeId

    async def get_shop_user_info(self):
        """获取店铺用户信息（异步方法）"""
        return await self.api.client.make_request('getShopUserInfo')

    # 其他兼容方法...