#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试修复后的cookie设置功能
"""

import sys
import os

# 添加src目录到Python路径
src_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src')
sys.path.insert(0, src_path)

# 添加Lib/site-packages到Python路径
lib_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'Lib', 'site-packages')
if os.path.exists(lib_path):
    sys.path.insert(0, lib_path)

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from cefpython3 import cefpython as cef
from src.ui.meituaneditwindow import MeituanEditWindow

def main():
    app = QApplication(sys.argv)
    
    # 初始化CEF
    try:
        settings = {
            "debug": False,
            "log_severity": cef.LOGSEVERITY_INFO,
            "log_file": "",
            "multi_threaded_message_loop": False,
        }
        
        cef.Initialize(settings)
        print("CEF初始化成功")
    except Exception as e:
        print(f"CEF初始化失败: {e}")
        return
    
    # 创建CEF消息循环定时器
    cef_timer = QTimer()
    cef_timer.timeout.connect(lambda: cef.MessageLoopWork())
    cef_timer.start(10)  # 每10ms处理一次CEF消息
    
    # 测试数据 - 模拟美团的cookies
    test_url = "https://shangoue.meituan.com"
    test_cookies = {
        "token": "test_token_123456",
        "userId": "987654321",
        "sessionId": "session_abc123",
        "loginStatus": "1"
    }
    
    # 创建编辑窗口
    edit_window = MeituanEditWindow(
        url=test_url,
        cookies=test_cookies,
        store_name="测试门店",
        platform="美团",
        islogin=False,
        action=None,
        parent=None,
        on_page_loaded=lambda: print("页面加载完成，cookies应该已设置")
    )
    
    edit_window.show()
    
    # 5秒后检查cookies是否设置成功
    def check_cookies():
        if edit_window.web_view.browser:
            js_code = "console.log('当前cookies:', document.cookie); document.cookie;"
            try:
                edit_window.web_view.browser.GetMainFrame().ExecuteFunction("eval", js_code)
                print("已执行cookie检查JavaScript")
            except Exception as e:
                print(f"检查cookies失败: {e}")
    
    QTimer.singleShot(5000, check_cookies)
    
    try:
        app.exec_()
    finally:
        # 清理CEF资源
        try:
            cef.Shutdown()
            print("CEF清理成功")
        except Exception as e:
            print(f"CEF清理失败: {e}")

if __name__ == '__main__':
    main()
