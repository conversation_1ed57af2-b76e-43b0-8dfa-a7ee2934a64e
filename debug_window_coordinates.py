#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
CEF窗口坐标诊断脚本
用于调试CEF浏览器窗口大小适配问题
"""

import sys
import os
import ctypes
from ctypes import wintypes
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton
from PyQt5.QtCore import QTimer, Qt, QRect
from PyQt5.QtGui import QPainter, QPen

# 添加Lib/site-packages到Python路径以支持CEF Python
lib_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'Lib', 'site-packages')
if lib_path not in sys.path:
    sys.path.insert(0, lib_path)

# CEF Python imports
from cefpython3 import cefpython as cef

# Windows API 常量和函数
user32 = ctypes.windll.user32
kernel32 = ctypes.windll.kernel32

class DiagnosticWidget(QWidget):
    """诊断用的CEF浏览器组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.browser = None
        self.browser_hwnd = None
        self.setFocusPolicy(Qt.StrongFocus)
        
        # 设置窗口属性
        self.setAttribute(Qt.WA_NativeWindow, True)
        self.setAttribute(Qt.WA_DontCreateNativeAncestors, True)
        
        # 设置背景色以便观察
        self.setStyleSheet("background-color: red;")

    def embed_browser(self, url):
        """嵌入CEF浏览器并进行详细诊断"""
        try:
            print("=== 开始CEF浏览器诊断 ===")
            
            # 确保窗口已经显示
            self.show()
            self.repaint()
            QApplication.processEvents()

            # 延迟创建浏览器
            QTimer.singleShot(500, lambda: self._create_browser_with_diagnosis(url))

        except Exception as e:
            print(f"创建CEF浏览器失败: {e}")

    def _create_browser_with_diagnosis(self, url):
        """创建浏览器并进行详细诊断"""
        try:
            print("\n=== Widget尺寸诊断 ===")
            
            # 获取Qt widget信息
            widget_rect = self.rect()
            widget_geometry = self.geometry()
            widget_frame_geometry = self.frameGeometry()
            
            print(f"Widget rect(): {widget_rect}")
            print(f"Widget geometry(): {widget_geometry}")
            print(f"Widget frameGeometry(): {widget_frame_geometry}")
            print(f"Widget size(): {self.size()}")
            print(f"Widget width/height: {self.width()}x{self.height()}")
            
            # 获取父窗口信息
            if self.parent():
                parent_rect = self.parent().rect()
                parent_geometry = self.parent().geometry()
                print(f"Parent rect(): {parent_rect}")
                print(f"Parent geometry(): {parent_geometry}")
            
            print("\n=== Windows API诊断 ===")
            
            # 获取Windows窗口信息
            hwnd = int(self.winId())
            print(f"Qt Widget HWND: {hwnd}")
            
            # 获取窗口矩形
            rect = wintypes.RECT()
            user32.GetWindowRect(hwnd, ctypes.byref(rect))
            print(f"GetWindowRect: left={rect.left}, top={rect.top}, right={rect.right}, bottom={rect.bottom}")
            print(f"GetWindowRect size: {rect.right - rect.left}x{rect.bottom - rect.top}")
            
            # 获取客户区矩形
            client_rect = wintypes.RECT()
            user32.GetClientRect(hwnd, ctypes.byref(client_rect))
            print(f"GetClientRect: left={client_rect.left}, top={client_rect.top}, right={client_rect.right}, bottom={client_rect.bottom}")
            print(f"GetClientRect size: {client_rect.right - client_rect.left}x{client_rect.bottom - client_rect.top}")
            
            print("\n=== DPI诊断 ===")
            
            # 获取DPI信息
            try:
                dpi = user32.GetDpiForWindow(hwnd)
                print(f"Window DPI: {dpi}")
                
                # 计算DPI缩放因子
                scale_factor = dpi / 96.0
                print(f"DPI Scale Factor: {scale_factor}")
            except:
                print("无法获取DPI信息")
            
            print("\n=== CEF浏览器创建 ===")
            
            # 使用客户区尺寸创建CEF浏览器
            cef_width = client_rect.right - client_rect.left
            cef_height = client_rect.bottom - client_rect.top
            
            print(f"CEF浏览器尺寸: {cef_width}x{cef_height}")
            
            # 设置CEF窗口信息
            window_info = cef.WindowInfo()
            rect = [0, 0, cef_width, cef_height]
            window_info.SetAsChild(hwnd, rect)
            print(f"CEF SetAsChild rect: {rect}")

            # 浏览器设置
            browser_settings = {
                "web_security_disabled": True,
                "plugins_disabled": True,
                "javascript_disabled": False,
                "background_color": 0xFFFFFFFF,
            }

            # 创建浏览器
            self.browser = cef.CreateBrowserSync(
                window_info=window_info,
                url=url,
                settings=browser_settings
            )

            print(f"CEF浏览器创建成功")
            
            # 获取浏览器窗口句柄
            QTimer.singleShot(500, self._diagnose_browser_window)

        except Exception as e:
            print(f"创建浏览器失败: {e}")

    def _diagnose_browser_window(self):
        """诊断浏览器窗口"""
        try:
            print("\n=== CEF浏览器窗口诊断 ===")
            
            if self.browser:
                # 获取浏览器窗口句柄
                browser_hwnd = self.browser.GetWindowHandle()
                if browser_hwnd:
                    self.browser_hwnd = browser_hwnd
                    print(f"CEF Browser HWND: {browser_hwnd}")
                    
                    # 获取浏览器窗口矩形
                    rect = wintypes.RECT()
                    user32.GetWindowRect(browser_hwnd, ctypes.byref(rect))
                    print(f"Browser GetWindowRect: left={rect.left}, top={rect.top}, right={rect.right}, bottom={rect.bottom}")
                    print(f"Browser GetWindowRect size: {rect.right - rect.left}x{rect.bottom - rect.top}")
                    
                    # 获取浏览器客户区矩形
                    client_rect = wintypes.RECT()
                    user32.GetClientRect(browser_hwnd, ctypes.byref(client_rect))
                    print(f"Browser GetClientRect: left={client_rect.left}, top={client_rect.top}, right={client_rect.right}, bottom={client_rect.bottom}")
                    print(f"Browser GetClientRect size: {client_rect.right - client_rect.left}x{client_rect.bottom - client_rect.top}")
                    
                    # 检查浏览器窗口是否正确定位
                    parent_hwnd = int(self.winId())
                    parent_rect = wintypes.RECT()
                    user32.GetClientRect(parent_hwnd, ctypes.byref(parent_rect))
                    
                    expected_width = parent_rect.right - parent_rect.left
                    expected_height = parent_rect.bottom - parent_rect.top
                    actual_width = rect.right - rect.left
                    actual_height = rect.bottom - rect.top
                    
                    print(f"\n=== 尺寸对比 ===")
                    print(f"期望尺寸: {expected_width}x{expected_height}")
                    print(f"实际尺寸: {actual_width}x{actual_height}")
                    print(f"宽度差异: {expected_width - actual_width}")
                    print(f"高度差异: {expected_height - actual_height}")
                    
                    if expected_width != actual_width or expected_height != actual_height:
                        print("⚠️ 发现尺寸不匹配！")
                        
                        # 尝试修正
                        print("尝试修正浏览器窗口大小...")
                        result = user32.SetWindowPos(
                            browser_hwnd,
                            0,
                            0,
                            0,
                            expected_width,
                            expected_height,
                            0x0004 | 0x0010  # SWP_NOZORDER | SWP_NOACTIVATE
                        )
                        print(f"SetWindowPos结果: {result}")
                        
                        # 再次检查
                        QTimer.singleShot(200, self._final_check)
                    else:
                        print("✅ 尺寸匹配正确")
                else:
                    print("❌ 无法获取浏览器窗口句柄")
            else:
                print("❌ 浏览器对象为空")
                
        except Exception as e:
            print(f"诊断浏览器窗口失败: {e}")

    def _final_check(self):
        """最终检查"""
        try:
            print("\n=== 最终检查 ===")
            if self.browser_hwnd:
                rect = wintypes.RECT()
                user32.GetWindowRect(self.browser_hwnd, ctypes.byref(rect))
                print(f"修正后浏览器尺寸: {rect.right - rect.left}x{rect.bottom - rect.top}")
        except Exception as e:
            print(f"最终检查失败: {e}")

    def paintEvent(self, event):
        """绘制诊断信息"""
        super().paintEvent(event)
        
        painter = QPainter(self)
        painter.setPen(QPen(Qt.blue, 2))
        
        # 绘制边框
        rect = self.rect()
        painter.drawRect(rect)
        
        # 绘制尺寸信息
        painter.drawText(10, 20, f"Widget: {rect.width()}x{rect.height()}")


class DiagnosticWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("CEF窗口坐标诊断")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 创建诊断widget
        self.diagnostic_widget = DiagnosticWidget()
        layout.addWidget(self.diagnostic_widget)
        
        # 延迟加载页面
        QTimer.singleShot(1000, self.load_page)

    def load_page(self):
        """加载页面"""
        print("开始加载页面进行诊断...")
        self.diagnostic_widget.embed_browser("https://www.baidu.com")


def main():
    print("开始CEF窗口坐标诊断...")
    
    # 初始化CEF
    sys.excepthook = cef.ExceptHook
    
    settings = {
        "debug": False,
        "log_severity": cef.LOGSEVERITY_ERROR,
        "log_file": "",
        "multi_threaded_message_loop": False,
        "command_line_args_disabled": False,
    }
    
    # 命令行参数（保持GPU禁用设置）
    command_line_args = [
        "--disable-gpu",
        "--disable-gpu-compositing", 
        "--disable-gpu-sandbox",
        "--force-device-scale-factor=1",
        "--high-dpi-support=0",
        "--no-sandbox",
    ]
    
    # 应用命令行参数
    if hasattr(cef, 'g_commandLineSwitches'):
        for arg in command_line_args:
            if '=' in arg:
                key, value = arg.split('=', 1)
                cef.g_commandLineSwitches[key.lstrip('-')] = value
            else:
                cef.g_commandLineSwitches[arg.lstrip('-')] = ""
    
    # 初始化CEF
    cef.Initialize(settings)
    print("CEF初始化完成")
    
    # 创建Qt应用
    app = QApplication(sys.argv)
    
    # 创建诊断窗口
    window = DiagnosticWindow()
    window.show()
    
    # CEF消息循环定时器
    cef_timer = QTimer()
    cef_timer.timeout.connect(lambda: cef.MessageLoopWork())
    cef_timer.start(10)
    
    # 运行应用
    app.exec_()
    
    # 清理CEF
    cef.Shutdown()


if __name__ == "__main__":
    main()
