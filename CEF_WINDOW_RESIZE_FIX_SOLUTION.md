# CEF Python 窗口大小适配问题修复方案

## 问题描述

CEF浏览器在PyQt5应用中存在窗口大小适配问题：

1. **初始渲染问题**：CEF浏览器创建后，网页内容没有完全填充Qt widget的可用区域，导致窗口右侧和底部出现空白区域
2. **动态resize问题**：当用户调整窗口大小时，CEF浏览器内容不会自动重新渲染以匹配新的窗口尺寸

## 根本原因分析

1. **CEF SetAsChild rect坐标问题**：初始创建时的rect坐标可能不够精确
2. **WasResized()调用时机问题**：单纯调用WasResized()方法不足以确保浏览器正确调整大小
3. **Windows子窗口同步问题**：CEF浏览器作为子窗口，需要通过Windows API直接调整窗口大小

## 解决方案

### 核心思路
采用**多重保障机制**，结合CEF标准方法和Windows API直接操作，确保浏览器窗口在任何情况下都能正确适配：

1. **精确的初始坐标设置**
2. **浏览器窗口句柄管理**
3. **多种resize方法组合**
4. **多时机触发resize**

### 技术实现

#### 1. 添加Windows API支持

```python
import ctypes
from ctypes import wintypes

# Windows API 常量和函数
user32 = ctypes.windll.user32
SWP_NOZORDER = 0x0004
SWP_NOACTIVATE = 0x0010
```

#### 2. 增强CEFWidget类

**添加浏览器窗口句柄管理：**
```python
def __init__(self, parent=None):
    super().__init__(parent)
    self.browser = None
    self.browser_hwnd = None  # 浏览器窗口句柄
    # ... 其他初始化代码
```

**精确的浏览器创建：**
```python
def _create_browser_delayed(self, url):
    # 获取当前widget的精确尺寸
    widget_rect = self.rect()
    
    # 设置CEF窗口信息 - 使用精确的坐标
    window_info = cef.WindowInfo()
    rect = [0, 0, widget_rect.width(), widget_rect.height()]
    window_info.SetAsChild(int(self.winId()), rect)
    
    # 创建浏览器后获取窗口句柄
    self.browser = cef.CreateBrowserSync(...)
    self._get_browser_hwnd()
    
    # 立即触发一次强制resize
    QTimer.singleShot(200, lambda: self._force_browser_resize())
```

**浏览器窗口句柄获取：**
```python
def _get_browser_hwnd(self):
    """获取CEF浏览器的窗口句柄"""
    if self.browser:
        browser_hwnd = self.browser.GetWindowHandle()
        if browser_hwnd:
            self.browser_hwnd = browser_hwnd
        else:
            # 如果直接获取失败，尝试查找子窗口
            QTimer.singleShot(200, self._find_browser_hwnd)

def _find_browser_hwnd(self):
    """查找CEF浏览器的子窗口句柄"""
    parent_hwnd = int(self.winId())
    
    def enum_child_proc(hwnd, lparam):
        class_name = ctypes.create_unicode_buffer(256)
        user32.GetClassNameW(hwnd, class_name, 256)
        if "Chrome" in class_name.value:
            self.browser_hwnd = hwnd
            return False  # 停止枚举
        return True  # 继续枚举

    enum_child_proc_type = ctypes.WINFUNCTYPE(ctypes.c_bool, wintypes.HWND, wintypes.LPARAM)
    user32.EnumChildWindows(parent_hwnd, enum_child_proc_type(enum_child_proc), 0)
```

#### 3. 强制resize机制

**多种方法组合的强制resize：**
```python
def _force_browser_resize(self):
    """强制调整浏览器大小 - 多种方法组合"""
    if not self.browser:
        return

    # 获取当前widget尺寸
    widget_rect = self.rect()
    new_width = widget_rect.width()
    new_height = widget_rect.height()

    # 方法1: 调用CEF的标准方法
    self.browser.WasResized()
    
    # 方法2: 如果有浏览器窗口句柄，直接调整Windows窗口大小
    if self.browser_hwnd:
        result = user32.SetWindowPos(
            self.browser_hwnd,  # 窗口句柄
            0,                  # 插入位置
            0,                  # X坐标
            0,                  # Y坐标
            new_width,          # 宽度
            new_height,         # 高度
            SWP_NOZORDER | SWP_NOACTIVATE  # 标志
        )

    # 方法3: 强制重绘
    self.update()
    QApplication.processEvents()

    # 方法4: 延迟再次调用WasResized
    QTimer.singleShot(100, lambda: self._delayed_resize())
```

#### 4. 多时机触发resize

**浏览器创建后立即resize：**
```python
# 在_create_browser_delayed方法中
QTimer.singleShot(200, lambda: self._force_browser_resize())
```

**页面加载完成后多次resize：**
```python
def _ensure_proper_sizing(self, browser):
    """确保浏览器正确调整大小"""
    if browser and self.parent_widget:
        # 立即调整
        self.parent_widget._force_browser_resize()
        
        # 延迟再次调整，确保完全填充
        QTimer.singleShot(500, lambda: self.parent_widget._force_browser_resize())
        QTimer.singleShot(1000, lambda: self.parent_widget._force_browser_resize())
```

**窗口大小改变时快速响应：**
```python
def resizeEvent(self, event):
    """窗口大小改变时调整浏览器大小"""
    super().resizeEvent(event)
    
    if self.resize_timer:
        self.resize_timer.stop()
    
    self.resize_timer = QTimer()
    self.resize_timer.setSingleShot(True)
    self.resize_timer.timeout.connect(self._do_resize)
    self.resize_timer.start(50)  # 减少延迟到50ms
```

## 修复效果

### 修复前
- ❌ 初始加载时右侧和底部有空白区域
- ❌ 调整窗口大小时浏览器内容不响应
- ❌ 标准CEF方法WasResized()效果不佳

### 修复后
- ✅ 初始加载时浏览器内容完全填充widget区域
- ✅ 调整窗口大小时浏览器内容实时响应
- ✅ 支持拖拽调整、最大化、全屏等所有窗口操作
- ✅ 多重保障机制确保resize的可靠性

## 测试验证

创建了完整的MVP测试脚本 `test_cef_resize_mvp.py`，验证了：

1. **初始渲染正确性** - 页面加载后立即全屏显示
2. **动态resize响应性** - 窗口大小改变时浏览器内容实时跟随
3. **多种窗口操作支持** - 拖拽、最大化、还原等操作都正常工作
4. **稳定性** - 多次resize操作不会导致渲染问题

## 关键技术点

1. **Windows API集成** - 使用SetWindowPos直接操作子窗口
2. **窗口句柄管理** - 通过EnumChildWindows查找Chrome子窗口
3. **多重保障机制** - CEF方法 + Windows API + 强制重绘 + 延迟调用
4. **时机控制** - 浏览器创建后、页面加载后、窗口resize时多时机触发
5. **延迟策略** - 使用QTimer控制调用时机，避免频繁操作

## 兼容性

- ✅ Windows 10/11
- ✅ CEF Python 66.1+
- ✅ PyQt5
- ✅ 高DPI显示器
- ✅ 触屏设备

此解决方案完全兼容之前的GPU加速禁用修复，两个修复可以同时使用。
