from typing import Dict, List
import asyncio
from datetime import datetime, timedelta

from sqlalchemy import insert, delete, text

from src.api.meituan_api import MeituanAPI
from src.api.eleme import ElemeAPI
from src.api.base_api import AuthenticationExpiredError
from src.models.store_data import Category, Product, Discount, get_session, ProductSku, get_session_context
from src.utils.logger import default_logger
from src.utils.mtgsig import MtgsigGenerator
import json
import concurrent.futures
import traceback


class DataSyncService:
    """数据同步服务"""
    
    def __init__(self, store_id: str, cookies: Dict, platform: str = 'meituan', progress_callback=None):
        """初始化数据同步服务
        
        Args:
            store_id: 店铺ID
            cookies: Cookie信息
            platform: 平台类型 ('饿了么' 或 '美团')
            progress_callback: 进度回调函数
        """
        self.store_id = store_id
        self.platform = platform
        self.progress_callback = progress_callback
        self.api = None
        
        # 根据平台类型初始化API
        if platform == '饿了么':
            self.api = ElemeAPI(cookies=cookies,shop_name=store_id)
        else:
            sign_generator = MtgsigGenerator('http://113.44.82.43:3000/calculate_signature')
            self.api = MeituanAPI(cookies, sign_generator, store_name=store_id)
    
    def emit_progress(self, message: str):
        """发送进度信息"""
        if self.progress_callback:
            self.progress_callback(message)
        default_logger.info(message)
    
    async def sync_all_data(self):
        """同步所有数据"""
        try:
            self.emit_progress("0% 开始同步店铺数据...")
            
            # 如果是饿了么平台，先初始化API
            if self.platform == '饿了么':
                if not await self.api.initialize():
                    raise Exception("饿了么API初始化失败")
            
            # 使用会话上下文管理器
            with get_session_context() as session:
                # 同步分类数据
                self.emit_progress("20% 开始同步分类数据...")
                await self.sync_categories(session)
                self.emit_progress("40% 分类数据同步完成")
                
                # 同步商品数据
                self.emit_progress("40% 开始同步商品数据...")
                await self.sync_products(session)
                self.emit_progress("80% 商品数据同步完成")
                
                # 同步折扣数据
                self.emit_progress("80% 开始同步折扣数据...")
                await self.sync_discounts(session)
                self.emit_progress("100% 折扣数据同步完成")
            
            self.emit_progress("100% 数据同步完成")

        except AuthenticationExpiredError as e:
            # 认证过期异常，记录但不中断整个流程
            error_msg = f"认证已过期: {e.message}"
            self.emit_progress(error_msg)
            default_logger.warning(error_msg)
            raise  # 重新抛出，让上层处理
        except Exception as e:
            error_msg = f"数据同步失败: {e}"
            self.emit_progress(error_msg)
            default_logger.error(error_msg)
            raise
        finally:
            # 确保关闭API客户端
            if self.platform == '饿了么':
                await self.api.close()
    
    async def sync_categories(self, session):
        """同步分类数据"""
        self.emit_progress("20% 同步分类数据...")
        
        try:
            # 删除旧数据
            session.query(Category).filter_by(store_id=self.store_id).delete()
            
            # 获取分类列表
            if self.platform == '饿了么':
                categories = await self.api.category.get_category_list()
                # 给categories添加sequence
                for i, category in enumerate(categories):
                    category['sequence'] = i
                self.store_id = self.api.category.client.store_id
                if categories:
                    category_list = categories
                else:
                    self.emit_progress("25% 未获取到分类数据")
                    return
            else:
                # 美团的原有逻辑
                categories = await self.api.get_category_list()
                if not categories or 'data' not in categories or 'tagList' not in categories['data']:
                    self.emit_progress("25% 未获取到分类数据")
                    return
                category_list = categories['data']['tagList']


            
            total_categories = len(category_list)
            self.emit_progress(f"25% 共发现 {total_categories} 个分类")

            if self.platform == '饿了么':
                # 查询未分类产品的数量
                count = await self.api.product.get_product_count(hasFrontCategory=False)
                category = Category(
                            store_id=self.store_id,
                            category_id=str(0),
                            name='未分类',
                            parent_id=0,
                            productCount=count,
                            sequence=0
                )
                session.add(category)

            # 插入新数据
            for index, cat in enumerate(category_list, 1):
                try:
                    if self.platform == '饿了么':
                        category = Category(
                            store_id=self.store_id,
                            category_id=str(cat.get('customCategoryId')),
                            name=cat.get('customCategoryName'),
                            parent_id=cat.get('parentId', 0),
                            productCount=cat.get('count', 0),
                            sequence=cat.get('rank', 0)
                        )
                    else:
                        sequence = cat.get('sequence', 0)
                        if cat.get('name') == '未分类':
                            sequence = 0
                        category = Category(
                            store_id=self.store_id,
                            category_id=str(cat['id']),
                            name=cat['name'],
                            parent_id=cat.get('parentId', 0),
                            productCount=cat.get('productCount', 0),
                            sequence=sequence
                        )
                    session.add(category)

                    progress = 25 + min(15, int(index * 15 / total_categories))
                    # self.emit_progress(f"{progress}% 正在同步分类数据: {index}/{total_categories}")

                except Exception as e:
                    self.emit_progress(f"35% 处理分类数据失败: {e}")
                    continue

            session.commit()
            self.emit_progress(f"40% 分类数据同步完成，共同步 {total_categories} 条")
            
        except Exception as e:
            session.rollback()
            self.emit_progress(f"35% 同步分类数据失败: {e}")
            raise
    
    async def fetch_product_page(self, page, page_size):
        """Fetch a single page of products."""
        return await self.api.get_product_list(page_num=page, page_size=page_size)

    async def fetch_discount_page(self, page, page_size):
        """Fetch a single page of discounts."""
        return await self.api.get_discount_list(page_no=page, page_size=page_size)

    async def sync_products(self, session):
        """同步商品数据"""
        try:
            start_time = datetime.now()
            self.emit_progress("40% 同步商品数据...")
            page_size = 100
        
            # 删除旧数据前先提交之前的事务
            session.commit()
            
            # 删除旧数据
            self.emit_progress("42% 清理旧数据...")
            session.query(ProductSku).filter_by(store_id=self.store_id).delete(synchronize_session='fetch')
            session.query(Product).filter_by(store_id=self.store_id).delete(synchronize_session='fetch')
            session.commit()

            # 初始化跟踪集合
            processed_combinations = set()
            
            # 获取商品总数
            if self.platform == '饿了么':
                total_products = await self.api.product.get_product_count()
            else:
                response = await self.api.get_product_list(page_num=1, page_size=1)
                total_products = response['data'].get('totalCount', 0) if response and 'data' in response else 0
            
            self.emit_progress(f"45% 共发现 {total_products} 个商品")
            
            # 并发获取数据
            tasks = []
            for page in range(1, (total_products // page_size) + 2):
                if self.platform == '饿了么':
                    tasks.append(self.api.product.get_product_list(page_num=page, page_size=page_size))
                else:
                    tasks.append(self.fetch_product_page(page, page_size))
            
            responses = await asyncio.gather(*tasks)
            
            # 批量处理数据
            products_to_add = []
            skus_to_add = []
            total_count = 0
            
            for i, response in enumerate(responses):
                if self.platform == '饿了么':
                    if not response or 'data' not in response or 'data' not in response['data']:
                        self.emit_progress(f"跳过无效响应: {response}")
                        continue
                    
                    products = response['data']['data']
                    if not products:
                        self.emit_progress("获取到空的商品列表")
                        continue
                    
                    progress = 45 + min(30, int(i * 30 / len(responses)))
                    self.emit_progress(f"{progress}% 正在同步商品数据: {i+1}/{len(responses)}")
                    
                    for prod in products:
                        try:
                            # 收集所有分类ID
                            category_ids = []
                            for category in prod.get('customCategoryList', []):
                                category_ids.append(str(category.get('customCategoryId', '')))
                            # 合并分类ID为逗号分隔的字符串
                            combined_category_ids = ','.join(filter(None, category_ids))
                            
                            if prod.get('hasSku'):
                                skus = prod.get('itemSkuList')
                                prices = [float(sku.get('price', 0)) for sku in skus]
                                min_price = min(prices)
                                max_price = max(prices)
                                has_multi_specs = len(skus) > 1
                                for sku in skus:
                                    combination = (self.store_id, str(prod['eleSkuId']), str(sku['itemSkuId']))
                                    if combination not in processed_combinations:
                                        # 安全获取图片URL
                                        picture_url = ''
                                        try:
                                            sale_properties = sku.get('salePropertyList', [])
                                            if sale_properties and len(sale_properties) > 0:
                                                images = sale_properties[0].get('images', [])
                                                if images and len(images) > 0:
                                                    picture_url = images[0].get('url', '')
                                            if not picture_url:
                                                picture_url = prod.get('picUrl', '')  # 回退到商品主图
                                        except Exception as e:
                                            self.emit_progress(f"获取商品图片失败: {e}")
                                            picture_url = prod.get('picUrl', '')  # 回退到商品主图

                                        product = Product(
                                            store_id=self.store_id,
                                            product_id=str(prod['itemId']),
                                            sku_id=str(sku['itemSkuId']),
                                            name=prod['title'],
                                            description=prod.get('description', ''),
                                            picture=picture_url,
                                            pictures=json.dumps(prod.get('descImageList', [])),
                                            category_id=combined_category_ids,
                                            min_price=min_price,
                                            max_price=max_price,
                                            total_stock=sum(int(s.get('quantity', 0)) for s in skus),
                                            status=prod.get('status', 0),
                                            sequence=0,  # 由于多个分类，使用默认排序
                                            has_multi_specs=has_multi_specs
                                        )
                                        session.merge(product)
                                        processed_combinations.add(combination)
                                        
                                        # 安全获取规格文本
                                        spec = '默认规格'
                                        try:
                                            if sku.get('salePropertyList') and len(sku['salePropertyList']) > 0:
                                                spec = sku['salePropertyList'][0].get('valueText', '默认规格')
                                        except Exception as e:
                                            self.emit_progress(f"获取规格文本失败: {e}")

                                        sku_obj = ProductSku(
                                            store_id=self.store_id,
                                            product_id=str(prod['itemId']),
                                            sku_id=str(sku['itemSkuId']),
                                            spec=spec,
                                            price=float(sku.get('price', 0)),
                                            stock=int(sku.get('quantity', 0)),
                                            min_order_count=int(prod.get('purchaseQuantity', 1)),
                                            box_price=float(sku.get('boxPrice', 0)),
                                            box_quantity=float(sku.get('boxNum', 0)),
                                            weight=float(sku.get('itemWeight', 0)),
                                            weight_unit=sku.get('weight_unit', ''),
                                            status=prod.get('status', 0),
                                            upc_code=sku.get('barCode', sku.get('barcode', '')),
                                            source_food_code=sku.get('sourceFoodCode', '')
                                        )
                                        session.merge(sku_obj)
                            else:
                                prices = prod['price']
                                min_price = prices
                                max_price = prices
                                has_multi_specs = False

                                combination = (self.store_id, str(prod['eleSkuId']), str(prod['eleSkuId']))
                                if combination not in processed_combinations:
                                    product = Product(
                                        store_id=self.store_id,
                                        product_id=str(prod['itemId']),
                                        sku_id=str(prod['eleSkuId']),
                                        name=prod['title'],
                                        description=prod.get('description', ''),
                                        picture=prod.get('picUrl', ''),
                                        pictures=json.dumps(prod.get('descImageList', [])),
                                        category_id=combined_category_ids,
                                        min_price=min_price,
                                        max_price=max_price,
                                        total_stock=prod.get('quantity', 0),
                                        status=prod.get('status', 0),
                                        sequence=0,  # 由于多个分类，使用默认排序
                                        has_multi_specs=has_multi_specs
                                    )
                                    session.merge(product)
                                    processed_combinations.add(combination)

                                    sku_obj = ProductSku(
                                        store_id=self.store_id,
                                        product_id=str(prod['itemId']),
                                        sku_id=str(prod['eleSkuId']),
                                        spec='默认规格',
                                        price=prices,
                                        stock=prod.get('quantity', 0),
                                        min_order_count=int(prod.get('purchaseQuantity', 1)),
                                        box_price=float(prod.get('boxPrice', 0)),
                                        box_quantity=float(prod.get('boxNum', 0)),
                                        weight=float(prod.get('itemWeight', 0)),
                                        weight_unit=prod.get('weight_unit', ''),
                                        status=prod.get('status', 0),
                                        upc_code=prod.get('barCode', ''),
                                        source_food_code=prod.get('sourceFoodCode', '')
                                    )
                                    session.merge(sku_obj)
                                
                            # 每处理一定数量就提交
                            if len(processed_combinations) % 1000 == 0:
                                session.commit()
                            
                        except Exception as e:
                            import traceback
                            error_line = traceback.extract_stack()[-1][1]
                            self.emit_progress(f"处理商品数据失败, line {error_line}: {e}")
                            continue
                        
                    # 每1000条数据批量提交
                    if len(products_to_add) >= 1000:
                        for product in products_to_add:
                            session.merge(product)
                        for sku in skus_to_add:
                            session.merge(sku)
                        session.commit()
                        products_to_add = []
                        skus_to_add = []
                else:
                    # 美团数据格式解析
                    if not response or 'data' not in response or 'productList' not in response['data']:
                        continue

                    products = response['data']['productList']
                    if not products:
                        continue

                    for prod in products:
                        try:
                            skus = prod.get('wmProductSkus', [])
                            if not skus:
                                continue
                            
                            # 计算价格范围
                            prices = [float(sku.get('price', 0)) for sku in skus]
                            min_price = min(prices)
                            max_price = max(prices)
                            has_multi_specs = len(skus) > 1
                            
                            for sku in skus:
                                combination = (self.store_id, str(prod['id']), str(sku['id']))
                                if combination not in processed_combinations:
                                    product = Product(
                                store_id=self.store_id,
                                product_id=str(prod['id']),
                                    sku_id=str(sku['id']),
                                name=prod['name'],
                                description=prod.get('description', ''),
                                picture=prod.get('picture', ''),
                                pictures=json.dumps(prod.get('pictures', [])),
                                category_id= prod.get('tagId'),
                                min_price=min_price,
                                max_price=max_price,
                                    total_stock=sum(int(s.get('stock', 0)) for s in skus),
                                status=prod.get('sellStatus', 0),
                                sequence=prod.get('sequence', 0),
                                    has_multi_specs=has_multi_specs
                            )
                                    session.merge(product)
                                    processed_combinations.add(combination)

                                    sku_obj = ProductSku(
                                        store_id=self.store_id,
                                        product_id=str(prod['id']),
                                        sku_id=str(sku['id']),
                                        spec=sku.get('spec', ''),
                                        price=float(sku.get('price', 0)),
                                        stock=int(sku.get('stock', 0)),
                                        min_order_count=int(sku.get('minOrderCount', 1)),
                                        box_price=float(sku.get('boxPrice', 0)),
                                        box_quantity=float(sku.get('boxNum', 0)),
                                        weight=float(sku.get('weight', 0)),
                                        weight_unit=sku.get('weight_unit', ''),
                                        status=int(sku.get('sellStatus', 0)),
                                        upc_code=sku.get('upcCode', ''),
                                        source_food_code=sku.get('sourceFoodCode', '')
                                    )
                                    session.merge(sku_obj)
                                    
                            # 每处理一定数量就提交
                            if len(processed_combinations) % 1000 == 0:
                                session.commit()

                        except Exception as e:
                            import traceback
                            error_line = traceback.extract_stack()[-1][1]
                            self.emit_progress(f"处理商品数据失败, line {error_line}: {e}")
                            continue
            
                    # 每1000条数据批量提交
                    if len(products_to_add) >= 1000:
                        for product in products_to_add:
                            session.merge(product)
                        for sku in skus_to_add:
                            session.merge(sku)
                        session.commit()
                        products_to_add = []
                        skus_to_add = []
                    
                # 最后提交剩余数据
                session.commit()
            
            self.emit_progress(f"80% 商品数据同步完成，共同步 {total_count} 条")
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            self.emit_progress(f"80% 商品数据同步耗时: {duration:.2f} 秒")
            
        except Exception as e:
            session.rollback()
            self.emit_progress(f"60% 同步商品数据失败: {e}, {traceback.format_exc()}")
            raise
    
    async def sync_discounts(self, session):
        """同步折扣数据"""
        try:
            start_time = datetime.now()
            self.emit_progress("80% 开始同步折扣数据...")
            
            try:
                # 删除该店铺的所有旧折扣数据
                self.emit_progress("82% 清理旧折扣数据...")
                session.execute(text(f"DELETE FROM discounts WHERE store_id = '{self.store_id}'"))
                session.commit()
            except Exception as e:
                self.emit_progress(f"清理旧数据失败: {e}")
                session.rollback()
                raise
            
            total_count = 0
            
            # 获取折扣数据列表
            if self.platform == '饿了么':
                # 饿了么平台获取折扣数据
                page_size = 20
                page = 1
                first_response = await self.api.discount.get_discount_list(page=page, page_size=page_size)
                if not first_response or 'data' not in first_response or 'data' not in first_response['data']:
                    self.emit_progress("85% 未获取到折扣数据")
                    return
                
                total_pages = first_response['data']['data']['pageCount']
                self.emit_progress(f"85% 共发现 {total_pages} 页折扣数据")
                
                # 创建所有页面的请求任务
                tasks = [self.api.discount.get_discount_list(page=p, page_size=100)
                        for p in range(1, total_pages + 1)]
                
            else:
                # 美团平台获取折扣数据
                page_size = 100
                first_page = await self.fetch_discount_page(1, page_size)
                if not first_page or 'data' not in first_page:
                    self.emit_progress("85% 未获取到折扣数据")
                    return
                    
                total_count = first_page['data'].get('respPage', {}).get('page',{}).get('totalCount', 0)
                total_pages = (total_count + page_size - 1) // page_size
                
                tasks = [self.fetch_discount_page(page, page_size) 
                        for page in range(1, total_pages + 1)]
            
            responses = await asyncio.gather(*tasks)
            processed_combinations = set()
            
            for i, response in enumerate(responses):
                if self.platform == '饿了么':
                    # 计算饿了么平台的进度（85-95%的范围内）
                    progress = 85 + min(10, int(i * 10 / len(responses)))
                    self.emit_progress(f"{progress}% 正在同步饿了么折扣数据: {i + 1}/{len(responses)}")
                    
                    if not response or 'data' not in response or 'data' not in response['data']:
                        continue
                    
                    discounts = response['data']['data'].get('list', [])
                    if not discounts:
                        continue
                    
                    for disc in discounts:
                        try:
                            id = disc.get('eleItemId')
                            # 通过id在product表中查询product_id
                            # print('id',id)
                            product = session.query(Product).filter_by(sku_id=id).first()
                            if product is not None:
                                product_id = product.product_id
                            else:
                               continue 

                            sku_id = str(disc.get('itemSkuId'))
                            if sku_id == 'None':
                                # sku_id = product_id
                                sku_id = disc.get('skuId')

                            # 检查是否已处理过该组合
                            combination = (self.store_id, product_id, sku_id)
                            if combination in processed_combinations:
                                continue
                            
                            processed_combinations.add(combination)
                            
                            # 处理饿了么的时间格式
                            try:
                                start_time = datetime.fromtimestamp(disc.get('startTime', 0) / 1000)
                                end_time = datetime.fromtimestamp(disc.get('endTime', 0) / 1000)
                            except (TypeError, ValueError):
                                start_time = datetime.now()
                                end_time = start_time + timedelta(days=30)
                            
                            discount = Discount(
                                store_id=self.store_id,
                                product_id=product_id,
                                sku_id=sku_id,
                                itemact_id=product_id,
                                discount_price=float(disc['activityPrice']),
                                start_time=start_time,
                                end_time=end_time,
                                order_limit=int(disc.get('activityDayLimit', 0)),
                                sort_index=0,
                                status=1 if disc.get('verifyStatus') == 2 else 0,
                                verify_status=disc.get('verifyStatus'),
                                verify_msg=disc.get('verifyMsg', ''),
                                updated_at=datetime.now()
                            )
                            
                            session.add(discount)
                            total_count += 1
                            
                        except Exception as e:
                            self.emit_progress(f"{progress}% 处理饿了么折扣数据失败: {e}")
                            continue
                        
                else:
                    # 美团平台的处理逻辑
                    if not response or 'data' not in response or 'respPage' not in response['data']:
                        continue

                    discounts = response['data']['respPage'].get('pageContent', [])
                    if not discounts:
                        continue

                    # 计算折扣同步进度（85-95%的范围内）
                    progress = 85 + min(10, int(i * 10 / len(responses)))
                    self.emit_progress(f"{progress}% 正在同步折扣数据: {i + 1}/{len(responses)}")

                    for disc in discounts:
                        try:
                            if disc.get('actStatus') == 2:
                                product_id = str(disc['spuId'])
                                sku_id = str(disc.get('skuId'))

                                # 检查是否已处理过该组合
                                combination = (self.store_id, product_id, sku_id)
                                if combination in processed_combinations:
                                    continue

                                processed_combinations.add(combination)

                                verify_msg_map = {
                                    1: "待生效",
                                    2: "进行中",
                                    3: "已过期",
                                    4: "已下线"
                                }
                                verify_msg = verify_msg_map.get(disc.get('actStatus'), "未知状态")

                                discount = Discount(
                                store_id=self.store_id,
                                product_id=product_id,
                                    sku_id=sku_id,
                                    itemact_id=disc.get('itemActId'),
                                    actId=disc.get('actId', '0'),
                                discount_price=float(disc['actPrice']),
                                start_time=datetime.fromtimestamp(disc['startTime']),
                                end_time=datetime.fromtimestamp(disc['endTime']),
                                order_limit=int(disc.get('orderLimit', 0)),
                                    sort_index=int(disc.get('sortNumber') or 0),
                                    status=1 if disc.get('actStatus') == 2 else 0,
                                    verify_status=disc.get('actStatus'),
                                    verify_msg=verify_msg,
                                    updated_at=datetime.now()
                                    )

                                session.add(discount)
                            else:
                                continue
                            total_count += 1

                        except Exception as e:
                            self.emit_progress(f"{progress}% 处理折扣数据失败: {e}")
                            continue

                # 每1000条提交一次
                if total_count % 1000 == 0:
                    try:
                        session.commit()
                    except Exception as e:
                        self.emit_progress(f"批量提交失败: {e}")
                        session.rollback()
                
            # 最后提交剩余数据
            try:
                session.commit()
            except Exception as e:
                self.emit_progress(f"最终提交失败: {e}")
                session.rollback()
                raise
            
            self.emit_progress(f"95% 折扣数据同步完成，共同步 {total_count} 条")
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            self.emit_progress(f"100% 折扣数据同步耗时: {duration:.2f} 秒")
            
        except Exception as e:
            session.rollback()
            self.emit_progress(f"90% 同步折扣数据失败: {e}")
            raise

