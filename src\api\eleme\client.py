"""
饿了么API请求客户端模块
"""
from typing import Dict, Any, Optional
import json
import time
import hashlib
import aiohttp
import asyncio
import traceback
from .config import ElemeAPIConfig
from .auth import ElemeAuth
from src.utils.logger import default_logger
from pathlib import Path

class ElemeClient:
    """饿了么API请求客户端"""
    
    def __init__(self, cookies: Dict[str, str], shop_name: str):
        """初始化客户端
        
        Args:
            cookies: 初始cookie字典
            shop_name: 店铺名称
        """
        self.shop_name = shop_name
        
        # 记录初始化信息
        default_logger.info(f"===== 初始化饿了么API客户端 =====")
        default_logger.info(f"店铺名称: {shop_name}")
        
        # 记录关键cookies
        key_cookies = ['WMUSS', 'WMSTOKEN', '_m_h5_tk', '_m_h5_tk_enc']
        cookies_log = {k: v for k, v in cookies.items() if k in key_cookies}
        default_logger.info(f"初始Cookies: {cookies_log}")
        
        self.auth = ElemeAuth(cookies)
        self.auth.shop_name = shop_name  # 设置auth对象的shop_name属性
        self.seller_id = None
        self.store_id = None
        self._session = None
        
        # 合并默认请求头
        self.headers = ElemeAPIConfig.DEFAULT_HEADERS.copy()
        self.headers.update(self.auth.headers)
        
        default_logger.info(f"===== 初始化完成 =====")
    
    def _get_timestamp(self) -> str:
        """获取当前时间戳
        
        Returns:
            时间戳字符串
        """
        return str(int(time.time() * 1000))
    
    def _get_sign(self, token: str, timestamp: str, app_key: str, data: str) -> str:
        """生成API签名
        
        Args:
            token: token值
            timestamp: 时间戳
            app_key: 应用key
            data: 请求数据
            
        Returns:
            签名字符串
        """
        # 获取token的第一部分
        token = token.split('_')[0] if token else ''
        
        # 构造签名字符串
        sign_str = f"{token}&{timestamp}&{app_key}&{data}"
        
        # 计算MD5
        return hashlib.md5(sign_str.encode('utf-8')).hexdigest()
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        if not self._session:
            self._session = aiohttp.ClientSession(
                headers=self.headers,
                cookies=self.auth.cookies,
                timeout=aiohttp.ClientTimeout(total=30)
            )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()
    
    async def make_request(self, 
                         endpoint: str, 
                         method: str = 'GET', 
                         data: Dict = None,
                         **kwargs) -> Optional[Dict]:
        """发送API请求"""
        try:
            # 获取端点配置
            endpoint_config = ElemeAPIConfig.get_endpoint(endpoint)
            url = endpoint_config['url']
            
            # 准备基础参数
            timestamp = self._get_timestamp()
            

            data_str = json.dumps(data, separators=(',', ':')) if data else '{}'
            
            # 准备请求参数
            params = endpoint_config['params'].copy()
            params.update({
                't': timestamp,
                'data': data_str,
                'type': 'originaljson',
                'dataType': 'json'
            })
            
            # 生成签名
            token = self.auth.cookies.get('_m_h5_tk', '')
            sign = self._get_sign(token, timestamp, ElemeAPIConfig.APP_KEY, data_str)
            params['sign'] = sign
            
            # 准备请求头
            headers = self.headers.copy()
            if 'headers' in kwargs:
                headers.update(kwargs.pop('headers'))
            
            # 准备cookies
            cookies = self.auth.cookies.copy()
            if 'cookies' in kwargs:
                cookies.update(kwargs.pop('cookies'))
            
            # 添加详细日志
            default_logger.info(f"===== 饿了么API请求 =====")
            default_logger.info(f"店铺名称: {self.shop_name}")
            default_logger.info(f"店铺ID: {self.store_id}")
            default_logger.info(f"商家ID: {self.seller_id}")
            default_logger.info(f"请求端点: {endpoint}")
            default_logger.info(f"请求URL: {url}")
            default_logger.info(f"请求方法: {method}")
            
            # 记录关键cookies
            key_cookies = ['WMUSS', 'WMSTOKEN', '_m_h5_tk', '_m_h5_tk_enc']
            cookies_log = {k: v for k, v in cookies.items() if k in key_cookies}
            default_logger.info(f"关键Cookies: {cookies_log}")
            
            if data:
                default_logger.info(f"请求数据: {json.dumps(data, ensure_ascii=False)[:200]}")
            
            # 确保session已创建
            if not self._session:
                await self.__aenter__()
            
            # 发送请求
            start_time = time.time()
            async with self._session.request(
                method, 
                url, 
                params=params,
                headers=headers,
                cookies=cookies,
                ssl=False,
                **kwargs
            ) as response:
                elapsed = time.time() - start_time
                default_logger.info(f"请求耗时: {elapsed:.2f}秒")
                
                if response.status == 200:
                    result = await response.json()
                    # 记录响应结果摘要
                    result_str = str(result)
                    default_logger.info(f"响应状态: {response.status}")
                    default_logger.info(f"响应结果: {result_str[:200]}{'...' if len(result_str) > 200 else ''}")
                    
                    # 检查登录状态
                    if '过期' in str(result):
                        default_logger.warning(f"饿了么-{self.shop_name}的Token已过期: {result}")
                        # 导入认证过期异常
                        from src.api.base_api import AuthenticationExpiredError
                        raise AuthenticationExpiredError('饿了么', self.shop_name, f"饿了么-{self.shop_name}的Token已过期，请更新登录信息")
                    
                    # 检查响应状态
                    if result.get('ret', [''])[0] != 'SUCCESS::调用成功':
                        default_logger.error(
                            f"API响应错误: {endpoint}, "
                            f"返回值: {result.get('ret')}"
                        )
                        return None
                    
                    default_logger.info(f"===== 请求成功结束 =====")    
                    return result
                else:
                    default_logger.error(
                        f"请求失败: {endpoint}, 状态码: {response.status}, URL: {url}"
                    )
                    default_logger.info(f"===== 请求失败结束 =====")
                    return None
                    
        except Exception as e:
            default_logger.error(f"请求异常: {endpoint}, 错误: {str(e)}")
            default_logger.error(f"错误详情: {traceback.format_exc()}")
            default_logger.info(f"===== 请求异常结束 =====")
            return None
    

    
    def _get_account_info(self) -> Optional[Dict[str, str]]:
        """获取账号信息"""
        # TODO: 从配置文件或安全的存储中获取账号信息
        # 临时使用硬编码的账号信息，后续需要改进
        account_map = {
            # 星辰鲜花店
            '**********': {'account': 'eleme_15928716442', 'password': 'Ofz96835'},
            '星辰': {'account': 'eleme_15928716442', 'password': 'Ofz96835'},
            # 繁花觅
            '*********': {'account': 'eleme_15828112127dnl', 'password': 'Yuy01733'},
            '繁花觅': {'account': 'eleme_15828112127dnl', 'password': 'Yuy01733'},
            # 繁花里
            '***********': {'account': 'eleme_15928716442yQh', 'password': 'Nmk57743'},
            '繁花里': {'account': 'eleme_15928716442yQh', 'password': 'Nmk57743'}
        }
        print(f'{self.shop_name}获取账号信息,account_map: {account_map}')
        return account_map.get(self.shop_name)

    def _update_headers_from_cookies(self):
        """根据cookies更新headers"""
        # 更新auth的headers
        self.auth._update_headers()
        # 更新客户端headers
        self.headers.update(self.auth.headers)

    async def init_shop_info(self) -> bool:
        """初始化店铺信息"""
        # 如果已经初始化过，直接返回
        if self.seller_id and self.store_id:
            return True
            
        try:
            result = await self.make_request('getShopUserInfo')
            if not result:
                return False
                
            shop_info = result.get('data', {}).get('data', {}).get('shopInfo', {})
            self.seller_id = shop_info.get('sellerId')
            self.store_id = shop_info.get('storeId')
            
            return bool(self.seller_id and self.store_id)
            
        except Exception as e:
            default_logger.error(f"初始化店铺信息失败: {str(e)}")
            return False
    
    async def close(self):
        """关闭客户端"""
        try:
            if self._session and not self._session.closed:
                await self._session.close()
                self._session = None
        except Exception as e:
            default_logger.error(f"关闭API客户端时出错: {str(e)}")
    
    def __del__(self):
        """清理资源"""
        try:
            if self._session and not self._session.closed:
                try:
                    loop = asyncio.get_event_loop()
                    if loop.is_running():
                        loop.create_task(self.close())
                except Exception:
                    pass
        except Exception as e:
            default_logger.error(f"清理API客户端资源时出错: {str(e)}")
    
    async def make_post_request(self, 
                              endpoint: str, 
                              data: Dict = None,
                              **kwargs) -> Optional[Dict]:
        """发送POST API请求

        Args:
            endpoint: API端点名称
            data: 请求数据
            **kwargs: 额外的请求参数

        Returns:
            响应数据字典，失败返回None
        """
        try:
            # 获取端点配置
            endpoint_config = ElemeAPIConfig.get_endpoint(endpoint)
            url = endpoint_config['url']

            # 准备基础参数
            timestamp = self._get_timestamp()

            # 准备请求参数
            params = endpoint_config['params'].copy()
            params.update({
                't': timestamp,
                'type': 'originaljson',
                'dataType': 'json'
            })

            # 生成签名
            token = self.auth.cookies.get('_m_h5_tk', '')
            sign = self._get_sign(token, timestamp, ElemeAPIConfig.APP_KEY, data['data'])
            params['sign'] = sign
            params['type'] = 'json'

            # 准备请求头
            headers = self.headers.copy()
            if 'headers' in kwargs:
                headers.update(kwargs.pop('headers'))
            headers['content-type'] = 'application/x-www-form-urlencoded'

            # 准备cookies
            cookies = self.auth.cookies.copy()
            if 'cookies' in kwargs:
                cookies.update(kwargs.pop('cookies'))

            # 添加详细日志
            default_logger.info(f"===== 饿了么API POST请求 =====")
            default_logger.info(f"店铺名称: {self.shop_name}")
            default_logger.info(f"店铺ID: {self.store_id}")
            default_logger.info(f"商家ID: {self.seller_id}")
            default_logger.info(f"请求端点: {endpoint}")
            default_logger.info(f"请求URL: {url}")
            
            # 记录关键cookies
            key_cookies = ['WMUSS', 'WMSTOKEN', '_m_h5_tk', '_m_h5_tk_enc']
            cookies_log = {k: v for k, v in cookies.items() if k in key_cookies}
            default_logger.info(f"关键Cookies: {cookies_log}")
            
            if data:
                default_logger.info(f"请求数据: {json.dumps(data, ensure_ascii=False)[:200]}")

            # 确保session已创建
            if not self._session:
                await self.__aenter__()

            # 发送POST请求
            start_time = time.time()
            async with self._session.post(
                url,
                params=params,
                data=data,  # POST请求的数据需要放在body中
                headers=headers,
                cookies=cookies,
                ssl=False,
                **kwargs
            ) as response:
                elapsed = time.time() - start_time
                default_logger.info(f"请求耗时: {elapsed:.2f}秒")
                
                if response.status == 200:
                    result = await response.json()
                    # 记录响应结果摘要
                    result_str = str(result)
                    default_logger.info(f"响应状态: {response.status}")
                    default_logger.info(f"响应结果: {result_str[:200]}{'...' if len(result_str) > 200 else ''}")

                    # 检查登录状态
                    if '过期' in str(result):
                        default_logger.warning(f"饿了么-{self.shop_name}的Token已过期: {result}")
                        # 导入认证过期异常
                        from src.api.base_api import AuthenticationExpiredError
                        raise AuthenticationExpiredError('饿了么', self.shop_name, f"饿了么-{self.shop_name}的Token已过期，请更新登录信息")

                    # 检查响应状态
                    if result.get('ret', [''])[0] != 'SUCCESS::调用成功':
                        default_logger.error(
                            f"API响应错误: {endpoint}, "
                            f"返回值: {result.get('ret')}"
                        )
                        return None
                    
                    default_logger.info(f"===== POST请求成功结束 =====")
                    return result
                else:
                    default_logger.error(
                        f"请求失败: {endpoint}, 状态码: {response.status}, URL: {url}"
                    )
                    default_logger.info(f"===== POST请求失败结束 =====")
                    return None

        except Exception as e:
            default_logger.error(f"请求异常: {endpoint}, 错误: {str(e)}")
            default_logger.error(f"错误详情: {traceback.format_exc()}")
            default_logger.info(f"===== POST请求异常结束 =====")
            return None