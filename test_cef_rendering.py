#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试CEF渲染修复的脚本
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtCore import QTimer, Qt

# 添加Lib/site-packages到Python路径以支持CEF Python
lib_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'Lib', 'site-packages')
if lib_path not in sys.path:
    sys.path.insert(0, lib_path)

# CEF Python imports
from cefpython3 import cefpython as cef

class ClientHandler:
    """CEF客户端处理器，用于处理页面加载事件"""

    def __init__(self, parent_widget):
        self.parent_widget = parent_widget

    def OnLoadEnd(self, browser, **_):
        """页面加载完成时的回调"""
        try:
            print("页面加载完成")
            # 确保浏览器正确渲染
            QTimer.singleShot(500, lambda: self._ensure_proper_rendering(browser))
            
        except Exception as e:
            print(f"页面加载完成回调失败: {e}")

    def _ensure_proper_rendering(self, browser):
        """确保浏览器正确渲染"""
        try:
            if browser and self.parent_widget:
                # 触发重绘
                browser.WasResized()
                self.parent_widget.update()
                print("触发浏览器重绘")
        except Exception as e:
            print(f"触发浏览器重绘失败: {e}")

    def OnPaint(self, browser, element_type, dirty_rects, paint_buffer, width, height):
        """处理绘制事件，避免重复渲染"""
        try:
            # 这里可以添加自定义的绘制逻辑来避免重复渲染问题
            # 目前保持默认行为
            _ = browser, element_type, dirty_rects, paint_buffer, width, height  # 忽略未使用的参数
            return False  # 返回False表示使用默认处理
        except Exception as e:
            print(f"绘制事件处理失败: {e}")
            return False


class CEFWidget(QWidget):
    """CEF浏览器组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.browser = None
        self.resize_timer = None
        self.setFocusPolicy(Qt.StrongFocus)
        # 设置窗口属性以避免重绘问题
        self.setAttribute(Qt.WA_NativeWindow, True)
        self.setAttribute(Qt.WA_DontCreateNativeAncestors, True)

    def embed_browser(self, url):
        """嵌入CEF浏览器"""
        try:
            # 如果已经有浏览器实例，先关闭它
            if self.browser:
                self.browser.CloseBrowser(True)
                self.browser = None

            # 确保窗口已经显示并获得正确的尺寸
            self.show()
            self.repaint()
            QApplication.processEvents()

            # 设置CEF窗口信息
            window_info = cef.WindowInfo()
            window_info.SetAsChild(int(self.winId()), [0, 0, self.width(), self.height()])

            # 浏览器设置 - 添加更多渲染优化选项
            browser_settings = {
                "web_security_disabled": True,
                "file_access_from_file_urls_allowed": True,
                "universal_access_from_file_urls_allowed": True,
                "plugins_disabled": True,
                "javascript_disabled": False,
                "java_disabled": True,
                "application_cache_disabled": True,
                "databases_disabled": True,
                "local_storage_disabled": False,
                "webgl_disabled": False,
                "background_color": 0xFFFFFFFF,  # 设置白色背景
            }

            # 创建浏览器
            self.browser = cef.CreateBrowserSync(
                window_info=window_info,
                url=url,
                settings=browser_settings
            )

            # 设置客户端处理器
            client_handler = ClientHandler(self)
            self.browser.SetClientHandler(client_handler)

            print(f"CEF浏览器创建成功: {url}")

        except Exception as e:
            print(f"创建CEF浏览器失败: {e}")

    def resizeEvent(self, event):
        """窗口大小改变时调整浏览器大小"""
        super().resizeEvent(event)
        if self.browser:
            # 使用定时器延迟调整大小，避免频繁调用导致的渲染问题
            if self.resize_timer:
                self.resize_timer.stop()
            
            self.resize_timer = QTimer()
            self.resize_timer.setSingleShot(True)
            self.resize_timer.timeout.connect(self._do_resize)
            self.resize_timer.start(100)  # 延迟100ms执行

    def _do_resize(self):
        """实际执行浏览器大小调整"""
        if self.browser:
            try:
                # 使用WasResized方法而不是SetBounds，这样更稳定
                self.browser.WasResized()
                print(f"浏览器大小已调整: {self.width()}x{self.height()}")
            except Exception as e:
                print(f"调整浏览器大小失败: {e}")

    def closeEvent(self, event):
        """关闭时清理浏览器"""
        try:
            # 停止定时器
            if self.resize_timer:
                self.resize_timer.stop()
                self.resize_timer = None
            
            # 关闭浏览器
            if self.browser:
                self.browser.CloseBrowser(True)
                self.browser = None
                
            print("CEF浏览器已清理")
        except Exception as e:
            print(f"关闭浏览器失败: {e}")
        super().closeEvent(event)


class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("CEF渲染测试")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 创建CEF widget
        self.cef_widget = CEFWidget()
        layout.addWidget(self.cef_widget)
        
        # 加载测试页面
        self.cef_widget.embed_browser("https://www.baidu.com")


def main():
    # 初始化CEF
    sys.excepthook = cef.ExceptHook  # To shutdown all CEF processes on error

    # CEF设置 - 使用有效的设置选项
    settings = {
        "debug": False,
        "log_severity": cef.LOGSEVERITY_INFO,
        "log_file": "",
        "multi_threaded_message_loop": False,
        "auto_zooming": "system_dpi",
        "ignore_certificate_errors": True,
        "remote_debugging_port": -1,
    }

    cef.Initialize(settings)

    # 创建Qt应用
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = TestWindow()
    window.show()
    
    # CEF消息循环定时器
    cef_timer = QTimer()
    cef_timer.timeout.connect(lambda: cef.MessageLoopWork())
    cef_timer.start(10)  # 每10ms处理一次CEF消息
    
    # 运行应用
    app.exec_()
    
    # 清理CEF
    cef.Shutdown()


if __name__ == "__main__":
    main()
