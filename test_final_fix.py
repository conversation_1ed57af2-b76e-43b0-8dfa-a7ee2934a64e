#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
CEF窗口大小适配最终修复测试
"""

import sys
import os
import ctypes
from ctypes import wintypes
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtCore import QTimer, Qt

# 添加Lib/site-packages到Python路径以支持CEF Python
lib_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'Lib', 'site-packages')
if lib_path not in sys.path:
    sys.path.insert(0, lib_path)

# CEF Python imports
from cefpython3 import cefpython as cef

# Windows API 常量和函数
user32 = ctypes.windll.user32
SWP_NOZORDER = 0x0004
SWP_NOACTIVATE = 0x0010

class FinalFixCEFWidget(QWidget):
    """最终修复版本的CEF浏览器组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.browser = None
        self.browser_hwnd = None
        self.setFocusPolicy(Qt.StrongFocus)
        
        # 设置窗口属性
        self.setAttribute(Qt.WA_NativeWindow, True)
        self.setAttribute(Qt.WA_DontCreateNativeAncestors, True)

    def embed_browser(self, url):
        """嵌入CEF浏览器"""
        try:
            print("开始创建CEF浏览器...")
            
            # 确保窗口已经显示
            self.show()
            self.repaint()
            QApplication.processEvents()

            # 延迟创建浏览器
            QTimer.singleShot(500, lambda: self._create_browser(url))

        except Exception as e:
            print(f"创建CEF浏览器失败: {e}")

    def _create_browser(self, url):
        """创建浏览器"""
        try:
            # 获取Windows客户区的精确尺寸
            hwnd = int(self.winId())
            client_rect = ctypes.wintypes.RECT()
            user32.GetClientRect(hwnd, ctypes.byref(client_rect))
            
            cef_width = client_rect.right - client_rect.left
            cef_height = client_rect.bottom - client_rect.top
            
            print(f"创建浏览器尺寸: {cef_width}x{cef_height}")

            # 设置CEF窗口信息
            window_info = cef.WindowInfo()
            rect = [0, 0, cef_width, cef_height]
            window_info.SetAsChild(hwnd, rect)

            # 浏览器设置
            browser_settings = {
                "web_security_disabled": True,
                "plugins_disabled": True,
                "javascript_disabled": False,
                "background_color": 0xFFFFFFFF,
            }

            # 创建浏览器
            self.browser = cef.CreateBrowserSync(
                window_info=window_info,
                url=url,
                settings=browser_settings
            )

            print("CEF浏览器创建成功")
            
            # 等待浏览器完全初始化后再进行resize操作
            QTimer.singleShot(1000, self._post_creation_setup)

        except Exception as e:
            print(f"创建浏览器失败: {e}")

    def _post_creation_setup(self):
        """浏览器创建后的设置"""
        try:
            print("开始浏览器创建后设置...")
            
            # 获取浏览器窗口句柄
            if self.browser:
                browser_hwnd = self.browser.GetWindowHandle()
                if browser_hwnd:
                    self.browser_hwnd = browser_hwnd
                    print(f"获取到浏览器窗口句柄: {browser_hwnd}")
                    
                    # 执行强制resize
                    self._aggressive_resize()
                else:
                    print("无法获取浏览器窗口句柄")
            
        except Exception as e:
            print(f"浏览器创建后设置失败: {e}")

    def _aggressive_resize(self):
        """激进的resize操作"""
        try:
            print("执行激进resize操作...")
            
            # 获取正确尺寸
            hwnd = int(self.winId())
            client_rect = ctypes.wintypes.RECT()
            user32.GetClientRect(hwnd, ctypes.byref(client_rect))
            
            correct_width = client_rect.right - client_rect.left
            correct_height = client_rect.bottom - client_rect.top
            
            print(f"目标尺寸: {correct_width}x{correct_height}")
            
            if self.browser_hwnd:
                # 步骤1: 先设置一个明显不同的尺寸
                print("步骤1: 设置临时尺寸")
                user32.SetWindowPos(
                    self.browser_hwnd, 0, 0, 0,
                    correct_width // 2, correct_height // 2,
                    SWP_NOZORDER | SWP_NOACTIVATE
                )
                
                # 步骤2: 等待一下再设置正确尺寸
                QTimer.singleShot(100, lambda: self._set_final_size(correct_width, correct_height))
            
            # 同时调用CEF方法
            if self.browser:
                self.browser.WasResized()
                try:
                    self.browser.NotifyMoveOrResizeStarted()
                except:
                    pass
            
        except Exception as e:
            print(f"激进resize失败: {e}")

    def _set_final_size(self, width, height):
        """设置最终正确尺寸"""
        try:
            print(f"步骤2: 设置最终尺寸 {width}x{height}")
            
            if self.browser_hwnd:
                # 设置正确尺寸
                result = user32.SetWindowPos(
                    self.browser_hwnd, 0, 0, 0,
                    width, height,
                    SWP_NOZORDER | SWP_NOACTIVATE
                )
                print(f"SetWindowPos结果: {result}")
                
                # 强制刷新
                user32.InvalidateRect(self.browser_hwnd, None, True)
                user32.UpdateWindow(self.browser_hwnd)
                print("强制刷新完成")
            
            # 再次调用CEF方法
            if self.browser:
                self.browser.WasResized()
                print("调用WasResized完成")
            
            # 强制重绘Qt widget
            self.update()
            self.repaint()
            QApplication.processEvents()
            print("Qt重绘完成")
            
            # 最后再确认一次
            QTimer.singleShot(500, self._final_confirmation)
            
        except Exception as e:
            print(f"设置最终尺寸失败: {e}")

    def _final_confirmation(self):
        """最终确认"""
        try:
            print("最终确认...")
            if self.browser:
                self.browser.WasResized()
            print("最终确认完成")
        except Exception as e:
            print(f"最终确认失败: {e}")

    def resizeEvent(self, event):
        """窗口大小改变时的处理"""
        super().resizeEvent(event)
        print(f"窗口大小改变: {self.width()}x{self.height()}")
        
        # 延迟执行resize
        QTimer.singleShot(50, self._handle_resize)

    def _handle_resize(self):
        """处理resize"""
        if self.browser and self.browser_hwnd:
            self._aggressive_resize()


class FinalTestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("CEF窗口大小适配最终修复测试")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 创建CEF widget
        self.cef_widget = FinalFixCEFWidget()
        layout.addWidget(self.cef_widget)
        
        # 延迟加载页面
        QTimer.singleShot(1000, self.load_page)

    def load_page(self):
        """加载页面"""
        print("开始加载页面...")
        self.cef_widget.embed_browser("https://www.baidu.com")


def main():
    print("开始CEF窗口大小适配最终修复测试...")
    
    # 初始化CEF
    sys.excepthook = cef.ExceptHook
    
    settings = {
        "debug": False,
        "log_severity": cef.LOGSEVERITY_ERROR,
        "log_file": "",
        "multi_threaded_message_loop": False,
        "command_line_args_disabled": False,
    }
    
    # 命令行参数
    command_line_args = [
        "--disable-gpu",
        "--disable-gpu-compositing", 
        "--disable-gpu-sandbox",
        "--force-device-scale-factor=1",
        "--high-dpi-support=0",
        "--no-sandbox",
    ]
    
    # 应用命令行参数
    if hasattr(cef, 'g_commandLineSwitches'):
        for arg in command_line_args:
            if '=' in arg:
                key, value = arg.split('=', 1)
                cef.g_commandLineSwitches[key.lstrip('-')] = value
            else:
                cef.g_commandLineSwitches[arg.lstrip('-')] = ""
    
    # 初始化CEF
    cef.Initialize(settings)
    print("CEF初始化完成")
    
    # 创建Qt应用
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = FinalTestWindow()
    window.show()
    
    # CEF消息循环定时器
    cef_timer = QTimer()
    cef_timer.timeout.connect(lambda: cef.MessageLoopWork())
    cef_timer.start(10)
    
    # 运行应用
    app.exec_()
    
    # 清理CEF
    cef.Shutdown()


if __name__ == "__main__":
    main()
