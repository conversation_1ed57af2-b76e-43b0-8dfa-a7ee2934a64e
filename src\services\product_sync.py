import os
import json
import asyncio
import traceback
from datetime import datetime, timed<PERSON><PERSON>
from concurrent.futures import ThreadPoolExecutor
from typing import List, Dict, Any

from src.api.meituan_api import  MeituanAPI
from src.utils.logger import default_logger
from src.models.store_data import Product, Discount, ProductSku, get_session_context
from src.utils.mtgsig import MtgsigGenerator
from src.api.eleme_api import Eleme
from src.api.base_api import AuthenticationExpiredError
from qfluentwidgets import InfoBar, InfoBarPosition


class ProductSyncService:
    """商品数据同步服务"""
    
    # 类级别的API对象缓存
    _api_instances = {}
    
    def __init__(self, cache_dir='cache'):
        self.cache_dir = cache_dir
        self.products_cache_file = os.path.join(cache_dir, 'products_cache.json')
        self.discounts_cache_file = os.path.join(cache_dir, 'discounts_cache.json')
        self.executor = ThreadPoolExecutor(max_workers=10)  # 最多5个并发线程

    @classmethod
    async def get_api_instance(cls, store: Dict) -> Any:
        """获取API实例的类方法"""
        store_id = store['id']
        
        # 如果实例已存在，直接返回
        if store_id in cls._api_instances:
            default_logger.info(f"使用缓存的API实例: {store['name']} ({store_id})")
            return cls._api_instances[store_id]
        
        # 创建新实例
        try:
            default_logger.info(f"===== 创建新的API实例 =====")
            default_logger.info(f"店铺名称: {store['name']}")
            default_logger.info(f"店铺ID: {store_id}")
            default_logger.info(f"平台: {store['platform']}")
            
            if store['platform'] == '美团':
                # 记录美团cookies关键信息
                key_cookies = ['token', 'wmPoiId', 'acctId']
                cookies_log = {k: v for k, v in store['cookies'].items() if k in key_cookies}
                default_logger.info(f"美团关键Cookies: {cookies_log}")
                
                api = MeituanAPI(
                    cookies=store['cookies'],
                    sign_generator=MtgsigGenerator(),
                    store_name=store['name']
                )
            else:  # 饿了么
                # 记录饿了么cookies关键信息
                key_cookies = ['WMUSS', 'WMSTOKEN', '_m_h5_tk', '_m_h5_tk_enc']
                cookies_log = {k: v for k, v in store['cookies'].items() if k in key_cookies}
                default_logger.info(f"饿了么关键Cookies: {cookies_log}")
                
                api = Eleme(store['cookies'], store['name'])
                # 初始化饿了么API
                default_logger.info(f"开始初始化饿了么API: {store['name']}")
                if not await api.initialize():
                    default_logger.error(f"饿了么API初始化失败: {store['name']}")
                    return None
                if not api.sellerId or not api.storeId:
                    default_logger.error(f"饿了么API初始化失败，未获取到店铺信息: {store['name']}")
                    return None
                default_logger.info(f"饿了么API初始化成功: {store['name']}, 商家ID: {api.sellerId}, 店铺ID: {api.storeId}")
            
            cls._api_instances[store_id] = api
            default_logger.info(f"创建{store['platform']}门店{store['name']}API实例：{store_id}")
            default_logger.info(f"===== API实例创建完成 =====")
            return api
        except Exception as e:
            default_logger.error(f"创建API实例失败: {store['name']} ({store_id}): {e}")
            default_logger.error(f"错误详情: {traceback.format_exc()}")
            return None

    @classmethod
    def get_cached_api_instance(cls, store_id: str) -> Any:
        """获取已缓存的API实例"""
        return cls._api_instances.get(store_id)

    @classmethod
    def clear_store_api_instance(cls, store_id: str) -> bool:
        """清除指定门店的API实例缓存"""
        try:
            if store_id in cls._api_instances:
                api = cls._api_instances[store_id]

                # 如果是饿了么API，需要关闭连接
                if hasattr(api, 'close'):
                    try:
                        # 尝试异步关闭
                        loop = asyncio.get_event_loop()
                        if loop.is_running():
                            loop.create_task(api.close())
                        else:
                            loop.run_until_complete(api.close())
                    except Exception as e:
                        default_logger.error(f"关闭API实例 {store_id} 时出错: {e}")

                # 从缓存中移除
                del cls._api_instances[store_id]
                default_logger.info(f"已清除门店 {store_id} 的API实例缓存")
                return True
            else:
                default_logger.info(f"门店 {store_id} 的API实例不存在于缓存中")
                return False
        except Exception as e:
            default_logger.error(f"清除门店 {store_id} API实例缓存时出错: {e}")
            return False

    @classmethod
    def refresh_store_api_instance(cls, store: Dict) -> bool:
        """刷新指定门店的API实例（清除旧实例，强制重新创建）"""
        try:
            store_id = store['id']
            store_name = store.get('name', '未知门店')

            # 清除旧实例
            cls.clear_store_api_instance(store_id)

            default_logger.info(f"已刷新门店 {store_name} ({store_id}) 的API实例缓存")
            return True
        except Exception as e:
            default_logger.error(f"刷新门店API实例缓存时出错: {e}")
            return False

    @classmethod
    async def validate_store_cookies(cls, store: Dict) -> Dict[str, Any]:
        """验证门店cookie是否有效"""
        try:
            store_id = store['id']
            store_name = store.get('name', '未知门店')
            platform = store.get('platform', '未知平台')

            default_logger.info(f"开始验证门店 {store_name} ({platform}) 的认证信息...")

            # 获取API实例（这会创建新实例或使用缓存）
            api = await cls.get_api_instance(store)
            if not api:
                return {
                    'success': False,
                    'message': f"无法创建{platform}API实例",
                    'store_name': store_name,
                    'platform': platform
                }

            # 根据平台进行不同的验证
            if platform == '美团':
                try:
                    # 尝试获取商品列表的第一页来验证cookie
                    result = await api.get_product_list(page_num=1, page_size=1)
                    if result and isinstance(result, dict):
                        if result.get('code') == 1001:
                            return {
                                'success': False,
                                'message': f"美团-{store_name}的cookies已失效，请更新登录信息",
                                'store_name': store_name,
                                'platform': platform
                            }
                        elif result.get('code') == 0:
                            return {
                                'success': True,
                                'message': f"美团-{store_name}的cookies验证成功",
                                'store_name': store_name,
                                'platform': platform
                            }
                        else:
                            return {
                                'success': False,
                                'message': f"美团-{store_name}返回未知错误: {result.get('msg', '未知错误')}",
                                'store_name': store_name,
                                'platform': platform
                            }
                    else:
                        return {
                            'success': False,
                            'message': f"美团-{store_name}返回数据格式异常",
                            'store_name': store_name,
                            'platform': platform
                        }
                except AuthenticationExpiredError as e:
                    return {
                        'success': False,
                        'message': e.message,
                        'store_name': store_name,
                        'platform': platform
                    }
                except Exception as e:
                    return {
                        'success': False,
                        'message': f"美团-{store_name}验证时发生异常: {str(e)}",
                        'store_name': store_name,
                        'platform': platform
                    }

            elif platform == '饿了么':
                try:
                    # 尝试获取店铺用户信息来验证Token
                    result = await api.get_shop_user_info()
                    if result and isinstance(result, dict):
                        # 检查是否包含成功标识
                        if result.get('ret', [''])[0] == 'SUCCESS::调用成功':
                            return {
                                'success': True,
                                'message': f"饿了么-{store_name}的Token验证成功",
                                'store_name': store_name,
                                'platform': platform
                            }
                        else:
                            return {
                                'success': False,
                                'message': f"饿了么-{store_name}的Token已过期或无效",
                                'store_name': store_name,
                                'platform': platform
                            }
                    else:
                        return {
                            'success': False,
                            'message': f"饿了么-{store_name}返回数据格式异常",
                            'store_name': store_name,
                            'platform': platform
                        }
                except AuthenticationExpiredError as e:
                    return {
                        'success': False,
                        'message': e.message,
                        'store_name': store_name,
                        'platform': platform
                    }
                except Exception as e:
                    return {
                        'success': False,
                        'message': f"饿了么-{store_name}验证时发生异常: {str(e)}",
                        'store_name': store_name,
                        'platform': platform
                    }

            else:
                return {
                    'success': False,
                    'message': f"不支持的平台: {platform}",
                    'store_name': store_name,
                    'platform': platform
                }

        except Exception as e:
            default_logger.error(f"验证门店认证信息时发生异常: {e}")
            return {
                'success': False,
                'message': f"验证过程中发生异常: {str(e)}",
                'store_name': store.get('name', '未知门店'),
                'platform': store.get('platform', '未知平台')
            }
        
    @classmethod
    def clear_api_cache(cls):
        """清除API实例缓存"""
        # 关闭所有API实例的连接
        for store_id, api in cls._api_instances.items():
            try:
                if hasattr(api, 'close') and callable(api.close):
                    # 创建事件循环来执行异步关闭操作
                    loop = asyncio.get_event_loop()
                    if loop.is_running():
                        loop.create_task(api.close())
                    else:
                        loop.run_until_complete(api.close())
            except Exception as e:
                default_logger.error(f"关闭API实例 {store_id} 时出错: {e}")
        
        # 清空缓存字典
        cls._api_instances = {}
        default_logger.info("已清除所有API实例缓存")
        
        # 删除cache/eleme目录下的所有缓存文件
        try:
            eleme_cache_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'cache', 'eleme')
            if os.path.exists(eleme_cache_dir):
                for cache_file in os.listdir(eleme_cache_dir):
                    try:
                        file_path = os.path.join(eleme_cache_dir, cache_file)
                        if os.path.isfile(file_path):
                            os.remove(file_path)
                            default_logger.info(f"已删除饿了么缓存文件: {cache_file}")
                    except Exception as e:
                        default_logger.error(f"删除饿了么缓存文件 {cache_file} 时出错: {e}")
                default_logger.info("已清除所有饿了么缓存文件")
        except Exception as e:
            default_logger.error(f"清除饿了么缓存文件时出错: {e}")

    def load_cache(self, cache_file: str) -> Dict:
        """加载缓存数据"""
        try:
            if os.path.exists(cache_file):
                with open(cache_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            default_logger.error(f"加载缓存失败 {cache_file}: {e}")
            return {}
            
    def save_cache(self, cache_file: str, data: Dict):
        """保存缓存数据"""
        try:
            os.makedirs(self.cache_dir, exist_ok=True)
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            default_logger.error(f"保存缓存失败 {cache_file}: {e}")
            
    

    async def get_store_products(self, store: Dict, keyword: str) -> List[Dict]:
        """获取单个门店的商品数据"""
        try:
            all_products = []
            page_size = 100  # 每页获取100条数据
            
            # 获取或创建API实例
            api = await self.get_api_instance(store)
            if not api:
                default_logger.error(f"无法获取API实例: {store['name']}")
                return []
                
            if store['platform'] == '美团':
                # 如果是搜索，只获取第一页
                result = await api.get_product_list(
                    page_num=1,
                    page_size=page_size,
                    search_word=keyword,
                    tag_id=None,
                    state=0  # 获取全部商品
                )
                print('result',result)
                

                
                products_map = {}  # 初始化products_map
                
                if result and result.get('data', {}).get('productList'):
                    products = result['data']['productList']
                    # 更新数据库中product表的信息
                    try:
                        with get_session_context() as session:
                            for prod in products:
                                try:
                                    skus = prod.get('wmProductSkus', [])
                                    if not skus:
                                        continue
                                            
                                    # 计算价格范围
                                    prices = [float(sku.get('price', 0)) for sku in skus]
                                    min_price = min(prices)
                                    max_price = max(prices)
                                    has_multi_specs = len(skus) > 1
                                    
                                    # 构建product对象
                                    product = Product(
                                        store_id=store['id'],
                                        product_id=str(prod['id']),
                                        name=prod['name'],
                                        description=prod.get('description', ''),
                                        picture=prod.get('picture', ''),
                                        pictures=json.dumps(prod.get('pictures', [])),
                                        category_id=prod.get('tagId'),
                                        min_price=min_price,
                                        max_price=max_price,
                                        total_stock=sum(int(s.get('stock', 0)) for s in skus),
                                        status=prod.get('sellStatus', 0),
                                        sequence=prod.get('sequence', 0),
                                        has_multi_specs=has_multi_specs
                                    )

                                    # 检查product是否存在
                                    existing_product = session.query(Product).filter_by(
                                        store_id=store['id'],
                                        product_id=str(prod['id'])
                                    ).first()

                                    if existing_product:
                                        # 更新现有记录
                                        for key, value in product.__dict__.items():
                                            if not key.startswith('_'):  # 跳过SQLAlchemy内部属性
                                                setattr(existing_product, key, value)
                                    else:
                                        # 插入新记录
                                        session.add(product)

                                    # 处理SKUs
                                    for sku in skus:
                                        sku_obj = ProductSku(
                                            store_id=store['id'],
                                            product_id=str(prod['id']),
                                            sku_id=str(sku['id']),
                                            spec=sku.get('spec', ''),
                                            price=float(sku.get('price', 0)),
                                            stock=int(sku.get('stock', 0)),
                                            min_order_count=int(sku.get('minOrderCount', 1)),
                                            box_price=float(sku.get('boxPrice', 0)),
                                            box_quantity=float(sku.get('boxNum', 0)),
                                            weight=float(sku.get('weight', 0)),
                                            weight_unit=sku.get('weight_unit', ''),
                                            status=int(sku.get('sellStatus', 0)),
                                            upc_code=sku.get('upcCode', ''),
                                            source_food_code=sku.get('sourceFoodCode', '')
                                        )

                                        # 检查sku是否存在
                                        existing_sku = session.query(ProductSku).filter_by(
                                            store_id=store['id'],
                                            product_id=str(prod['id']),
                                            sku_id=str(sku['id'])
                                        ).first()

                                        if existing_sku:
                                            # 更新现有记录
                                            for key, value in sku_obj.__dict__.items():
                                                if not key.startswith('_'):
                                                    setattr(existing_sku, key, value)
                                        else:
                                            # 插入新记录
                                            session.add(sku_obj)

                                except Exception as e:
                                    default_logger.error(f"处理商品 {prod.get('id')} 时出错: {str(e)}")
                                    continue

                            # 批量提交所有更改
                            session.commit()

                    except Exception as e:
                        default_logger.error(f"同步商品数据时出错: {str(e)}")
                        raise

                    all_products.extend(products)

               
                # 获取折扣信息
                discount_page = 1
                discount_size = 100
                all_discounts = []
                
                # 根据商品ID获取折扣信息
                sku_ids = []
                for product in all_products:
                    if 'wmProductSkus' in product:
                        for sku in product['wmProductSkus']:
                            if 'id' in sku:
                                sku_ids.append(str(sku['id']))

                if sku_ids:
                    discount_result = await api.get_discount_list(
                        page_no=discount_page,
                        page_size=discount_size,
                        search_params={'skuIdList': sku_ids}
                    )

                    if discount_result and discount_result.get('data', {}).get('respPage',{}).get('pageContent',[]):
                        discounts = discount_result['data']['respPage']['pageContent']
                        try:
                            with get_session_context() as session:
                                for disc in discounts:
                                    try:
                                        # 只处理状态为2（进行中）的折扣
                                        if disc.get('actStatus') != 2:
                                            continue

                                        product_id = str(disc['spuId'])
                                        sku_id = str(disc.get('skuId'))

                                        # 状态映射
                                        verify_msg_map = {
                                            1: "待生效",
                                            2: "进行中",
                                            3: "已过期",
                                            4: "已下线"
                                        }
                                        verify_msg = verify_msg_map.get(disc.get('actStatus'), "未知状态")

                                        # 构建折扣对象
                                        discount = Discount(
                                            store_id=store['id'],
                                            product_id=product_id,
                                            sku_id=sku_id,
                                            itemact_id=disc.get('itemActId'),
                                            actId=disc.get('actId', '0'),
                                            discount_price=float(disc['actPrice']),
                                            start_time=datetime.fromtimestamp(disc['startTime']),
                                            end_time=datetime.fromtimestamp(disc['endTime']),
                                            order_limit=int(disc.get('orderLimit', 0)),
                                            sort_index=int(disc.get('sortNumber') or 0),
                                            status=1 if disc.get('actStatus') == 2 else 0,
                                            verify_status=disc.get('actStatus'),
                                            verify_msg=verify_msg,
                                            updated_at=datetime.now()
                                        )

                                        # 检查折扣是否已存在
                                        existing_discount = session.query(Discount).filter_by(
                                            store_id=store['id'],
                                            product_id=product_id,
                                            sku_id=sku_id
                                        ).first()

                                        if existing_discount:
                                            # 更新现有记录
                                            for key, value in discount.__dict__.items():
                                                if not key.startswith('_'):  # 跳过SQLAlchemy内部属性
                                                    setattr(existing_discount, key, value)
                                        else:
                                            # 插入新记录
                                            session.add(discount)

                                    except Exception as e:
                                        default_logger.error(f"处理折扣数据失败: {e}")
                                        default_logger.error(f"错误详情: {traceback.format_exc()}")
                                        continue

                                # 批量提交所有更改
                                try:
                                    session.commit()
                                    default_logger.info("折扣数据同步完成")
                                except Exception as e:
                                    session.rollback()
                                    default_logger.error(f"提交折扣数据时出错: {e}")
                                    raise

                        except Exception as e:
                            default_logger.error(f"同步折扣数据时出错: {str(e)}")
                            raise

                        all_discounts.extend(discounts)
                
                # 构建商品ID到折扣信息的映射
                discount_map = {}
                for discount in all_discounts:
                    sku_id = discount.get('skuId')
                    spu_id = discount.get('spuId')
                    if sku_id:
                        if spu_id not in discount_map:
                            discount_map[spu_id] = {}
                        discount_map[spu_id][sku_id] = {
                            'discount_price': discount.get('actPrice'),
                            'order_limit': discount.get('orderLimit', 0),
                            'sort_index': discount.get('sortNumber', 0),
                            'itemActId': discount.get('itemActId', ''),
                            'orderLimit': discount.get('orderLimit', 0)
                        }

                # 合并商品和折扣信息
                for product in all_products:
                    product_id = product['id']
                    products_map[product_id] = {
                        'id': product_id,
                        'name': product['name'],
                        'wmProductSkus': product.get('wmProductSkus', []),
                        'image': product.get('picture', ''),
                        'images': product.get('pictures', []),
                        'store': {
                            'id': store['id'],
                            'name': store['name'],
                            'platform': store['platform']
                        }
                    }
                    
                    # 添加折扣信息
                    if product_id in discount_map:
                        for sku in products_map[product_id]['wmProductSkus']:
                            for sku_id, discount in discount_map[product_id].items():
                                if sku['id'] == sku_id:
                                    sku.update(discount)

            else:  # 饿了么平台
                # 只获取第一页商品列表
                default_logger.info(f"[饿了么数据请求] 开始获取门店 {store['name']} 的商品列表，关键字: {keyword}")
                result = await api.api.product.get_product_list(page_num=1, page_size=page_size,search_word=keyword)
                default_logger.info(f"[饿了么数据请求] API返回结果: {result is not None}, 数据结构: {type(result)}")
                
                products_map = {}  # 初始化products_map
                
                if result and 'data' in result and 'data' in result['data']:
                    products = result['data']['data']
                    default_logger.info(f"[饿了么数据解析] 获取到 {len(products) if products else 0} 个商品")
                    if products:
                        # 更新数据库中的商品信息
                        try:
                            with get_session_context() as session:
                                for prod in products:
                                    try:
                                        # 处理商品图片
                                        pictures = [img['url'] for img in prod.get('images', [])]
                                        
                                        # 获取SKU列表
                                        skus = []
                                        if prod.get('hasSku'):
                                            skus = prod.get('itemSkuList', [])
                                        else:
                                            # 单规格商品
                                            skus = [{
                                                'itemSkuId': prod['eleSkuId'],
                                                'salePropertyList': [{'valueText': '默认规格'}],
                                                'price': float(prod.get('price', 0)),
                                                'quantity': int(prod.get('quantity', 0))
                                            }]
                                        
                                        if not skus:
                                            continue
                                            
                                        # 计算价格范围
                                        prices = [float(sku.get('price', 0)) for sku in skus]
                                        min_price = min(prices)
                                        max_price = max(prices)
                                        has_multi_specs = len(skus) > 1
                                        
                                        # 构建product对象
                                        product = Product(
                                            store_id=store['id'],
                                            product_id=str(prod['itemId']),
                                            name=prod['title'],
                                            description=prod.get('description', ''),
                                            picture=prod.get('picUrl', ''),
                                            pictures=json.dumps(pictures),
                                            category_id=prod.get('cateId'),
                                            min_price=min_price,
                                            max_price=max_price,
                                            total_stock=sum(int(s.get('quantity', 0)) for s in skus),
                                            status=prod.get('status', 0),
                                            sequence=prod.get('sequence', 0),
                                            has_multi_specs=has_multi_specs
                                        )

                                        # 检查product是否存在
                                        existing_product = session.query(Product).filter_by(
                                            store_id=store['id'],
                                            product_id=str(prod['itemId'])
                                        ).first()

                                        if existing_product:
                                            # 更新现有记录
                                            for key, value in product.__dict__.items():
                                                if not key.startswith('_'):  # 跳过SQLAlchemy内部属性
                                                    setattr(existing_product, key, value)
                                        else:
                                            # 插入新记录
                                            session.add(product)

                                        # 处理SKUs
                                        for sku in skus:
                                            sku_obj = ProductSku(
                                                store_id=store['id'],
                                                product_id=str(prod['itemId']),
                                                sku_id=str(sku['itemSkuId']),
                                                spec=sku['salePropertyList'][0]['valueText'],
                                                price=float(sku.get('price', 0)),
                                                stock=int(sku.get('quantity', 0)),
                                                min_order_count=int(prod.get('purchaseQuantity', 1)),
                                                box_price=float(prod.get('boxPrice', 0)),
                                                box_quantity=float(prod.get('boxNum', 0)),
                                                weight=float(prod.get('weight', 0)),
                                                weight_unit=prod.get('weight_unit', ''),
                                                status=prod.get('status', 0),
                                                upc_code=prod.get('barCode', ''),
                                                source_food_code=''
                                            )

                                            # 检查sku是否存在
                                            existing_sku = session.query(ProductSku).filter_by(
                                                store_id=store['id'],
                                                product_id=str(prod['itemId']),
                                                sku_id=str(sku['itemSkuId'])
                                            ).first()

                                            if existing_sku:
                                                # 更新现有记录
                                                for key, value in sku_obj.__dict__.items():
                                                    if not key.startswith('_'):
                                                        setattr(existing_sku, key, value)
                                            else:
                                                # 插入新记录
                                                session.add(sku_obj)

                                    except Exception as e:
                                        default_logger.error(f"处理商品 {prod.get('itemId')} 时出错: {str(e)}")
                                        continue

                                # 批量提交所有更改
                                session.commit()

                        except Exception as e:
                            default_logger.error(f"同步饿了么商品数据时出错: {str(e)}")
                            raise

                        # 处理饿了么商品数据
                        for prod in products:
                            if keyword and keyword not in prod['title']:
                                continue

                            default_logger.info(f"[饿了么商品解析] 处理商品: {prod['title']}, ID: {prod['itemId']}, 多规格: {prod.get('hasSku', False)}")
                            default_logger.info(f"[饿了么商品解析] 商品条码: {prod['barCode']}")

                            product_data = {
                                'id': prod['itemId'],
                                'name': prod['title'],
                                'barCode': prod['barCode'],
                                'wmProductSkus': [],
                                'image': prod.get('picUrl', ''),
                                'images': [img['url'] for img in prod.get('images', [])],
                                'cateId': prod.get('cateId', ''),
                                'store': {
                                    'id': store['id'],
                                    'name': store['name'],
                                    'platform': store['platform']
                                }
                            }
                                
                            # 处理SKU信息
                            if prod.get('hasSku'):
                                sku_list = prod.get('itemSkuList', [])
                                default_logger.info(f"[饿了么SKU解析] 多规格商品 {prod['title']} 有 {len(sku_list)} 个规格")
                                for i, sku in enumerate(sku_list):
                                    spec_name = sku.get('salePropertyList', [{}])[0].get('valueText', '未知')
                                    # 检查两个可能的条码字段
                                    sku_barcode = sku.get('barcode', sku.get('barCode', '无条码'))
                                    # 输出完整的SKU数据结构用于分析
                                    default_logger.info(f"[饿了么SKU解析] 规格 {i+1}: ID={sku.get('itemSkuId')}, 价格={sku.get('price')}, 规格名={spec_name}, 条码={sku_barcode}")
                                    default_logger.info(f"[饿了么SKU解析] 完整SKU数据: {sku}")
                                product_data['wmProductSkus'] = sku_list
                            else:
                                # 单规格商品
                                sku_data = {
                                    'id': prod['eleSkuId'],
                                    'spec': '默认规格',
                                    'barCode': prod['barCode'],
                                    'price': float(prod.get('price', 0)),
                                    'stock': int(prod.get('quantity', 0)),
                                    'minOrderCount': int(prod.get('purchaseQuantity', 1)),
                                    'sellStatus': -2 if prod.get('status') == -2 else 0
                                }
                                default_logger.info(f"[饿了么SKU解析] 单规格商品 {prod['title']}: ID={sku_data['id']}, 价格={sku_data['price']}")
                                product_data['wmProductSkus'].append(sku_data)
                            
                            all_products.append(product_data)
                            products_map[str(prod['itemId'])] = product_data
                            
                # 获取折扣信息
                try:
                    barCodes_str = all_products[0]['barCode'] if all_products else None
                    default_logger.info(f"[饿了么折扣请求] 开始获取折扣信息，商品数量: {len(all_products)}, 条码: {barCodes_str}")
                    if barCodes_str:  # 添加对None的检查
                        barCodes_list = barCodes_str.split(',')
                        for barCode in barCodes_list:
                            all_discounts = []
                            
                            # 获取折扣活动列表
                            default_logger.info(f"[饿了么折扣请求] 查询条码 {barCode} 的折扣信息")
                            discount_result = await api.api.discount.get_discount_list_upc(
                                page=1,
                                page_size=100,
                                upc=barCode
                            )

                            if discount_result and 'data' in discount_result and 'data' in discount_result['data']:
                                discounts = discount_result['data']['data'].get('list', [])
                                default_logger.info(f"[饿了么折扣解析] 条码 {barCode} 找到 {len(discounts)} 个折扣活动")
                                if discounts:
                                    # 更新数据库中的折扣信息
                                    with get_session_context() as session:
                                        for disc in discounts:
                                            try:
                                                id = disc.get('productId')
                                                # 通过id在product表中查询product_id
                                                # print('id',id)
                                                product = session.query(Product).filter_by(sku_id=id).first()
                                                if product is not None:
                                                    product_id = product.product_id
                                                else:
                                                    continue

                                                sku_id = str(disc.get('itemSkuId'))
                                                if sku_id == 'None':
                                                    sku_id = product_id

                                                # 处理饿了么的时间格式
                                                try:
                                                    start_time = datetime.fromtimestamp(disc.get('startTime', 0) / 1000)
                                                    end_time = datetime.fromtimestamp(disc.get('endTime', 0) / 1000)
                                                except (TypeError, ValueError):
                                                    start_time = datetime.now()
                                                    end_time = start_time + timedelta(days=30)
                                                
                                                # 检查折扣是否已存在
                                                existing_discount = session.query(Discount).filter_by(
                                                    store_id=store['id'],
                                                    product_id=product_id,
                                                    sku_id=sku_id
                                                ).first()

                                                if existing_discount:
                                                    # 更新现有记录
                                                    existing_discount.discount_price = float(disc['lowestActivityPrice'])
                                                    existing_discount.start_time = start_time
                                                    existing_discount.end_time = end_time
                                                    existing_discount.order_limit = int(disc.get('lowestActivityDayLimit', 0))
                                                    existing_discount.sort_index = 0
                                                    existing_discount.status = 1 if disc.get('verifyStatus') == 2 else 0
                                                    existing_discount.verify_status = disc.get('verifyStatus')
                                                    existing_discount.verify_msg = disc.get('verifyMsg', '')
                                                    existing_discount.updated_at = datetime.now()
                                                    default_logger.debug(f"更新折扣记录: 商品ID {product_id}, SKU ID {sku_id}")
                                                else:
                                                    # 创建新记录
                                                    discount = Discount(
                                                        store_id=store['id'],
                                                        product_id=product_id,
                                                        sku_id=sku_id,
                                                        itemact_id=disc.get('activityId'),
                                                        discount_price=float(disc['lowestActivityPrice']),
                                                        start_time=start_time,
                                                        end_time=end_time,
                                                        order_limit=int(disc.get('lowestActivityDayLimit', 0)),
                                                        sort_index=0,
                                                        status=1 if disc.get('verifyStatus') == 2 else 0,
                                                        verify_status=disc.get('verifyStatus'),
                                                        verify_msg=disc.get('verifyMsg', ''),
                                                        updated_at=datetime.now()
                                                    )
                                                    session.add(discount)
                                                    default_logger.debug(f"新增折扣记录: 商品ID {product_id}, SKU ID {sku_id}")
                                                
                                            except Exception as e:
                                                default_logger.error(f"处理折扣数据失败: {e}")
                                                default_logger.error(f"错误详情: {traceback.format_exc()}")
                                                continue

                                        # 批量提交所有更改
                                        try:
                                            session.commit()
                                            default_logger.info("折扣数据同步完成")
                                        except Exception as e:
                                            session.rollback()
                                            default_logger.error(f"提交折扣数据时出错: {e}")
                                            raise

                                    all_discounts.extend(discounts)
                            
                            # 添加折扣信息到商品
                            default_logger.info(f"[饿了么折扣匹配] 开始将折扣信息匹配到商品，商品数量: {len(all_products)}, 折扣数量: {len(all_discounts)}")
                            for product in all_products:
                                if 'barCode' in product and product['barCode']:
                                    barCodes_list = product['barCode'].split(',')
                                    default_logger.info(f"[饿了么折扣匹配] 处理商品 {product['name']}, 条码列表: {barCodes_list}")

                                    for barCode in barCodes_list:
                                        for discount in all_discounts:
                                            if str(discount['upc']) == barCode:
                                                default_logger.info(f"[饿了么折扣匹配] 找到匹配的折扣: 条码={barCode}, 折扣ID={discount.get('itemSkuId')}, 折扣价={discount.get('activityPrice', discount.get('lowestActPrice'))}")

                                                # 获取商品的索引
                                                index = barCodes_list.index(barCode)

                                                # 检查是否是多规格商品的折扣
                                                if discount.get('itemSkuId', None) is not None:
                                                    # 多规格商品，需要匹配具体的SKU
                                                    default_logger.info(f"[饿了么折扣匹配] 多规格商品折扣，SKU ID: {discount['itemSkuId']}")
                                                    for sku_index, sku in enumerate(product['wmProductSkus']):
                                                        if str(discount['itemSkuId']) == str(sku.get('itemSkuId', sku.get('id'))):
                                                            default_logger.info(f"[饿了么折扣匹配] 匹配到SKU {sku_index}: {sku.get('itemSkuId', sku.get('id'))}, 更新折扣价: {discount['activityPrice']}")
                                                            sku.update({
                                                                'discount_price': float(discount['activityPrice']),
                                                                'order_limit': int(discount.get('lowestActivityDayLimit', 0)),
                                                                'sort_index': 0  # 饿了么没有排序字段
                                                            })
                                                            break
                                                    else:
                                                        default_logger.warning(f"[饿了么折扣匹配] 未找到匹配的SKU: {discount['itemSkuId']}")
                                                else:
                                                    # 单规格商品的折扣 - 根据条码直接匹配对应的SKU
                                                    default_logger.info(f"[饿了么折扣匹配] 单规格商品折扣，折扣价: {discount.get('lowestActPrice')}")

                                                    # 根据条码匹配对应的SKU
                                                    sku_found = False
                                                    for sku_index, sku in enumerate(product['wmProductSkus']):
                                                        # 检查两个可能的条码字段
                                                        sku_barcode = sku.get('barcode', sku.get('barCode', ''))
                                                        default_logger.info(f"[饿了么折扣匹配] 检查SKU {sku_index}: 条码={sku_barcode}, 目标条码={barCode}")

                                                        if sku_barcode == barCode:
                                                            default_logger.info(f"[饿了么折扣匹配] 找到匹配的SKU {sku_index}: ID={sku.get('itemSkuId', sku.get('id'))}, 条码={sku_barcode}")

                                                            sku.update({
                                                                'discount_price': float(discount['lowestActPrice']),
                                                                'order_limit': int(discount.get('lowestActivityDayLimit', 0)),
                                                                'sort_index': 0
                                                            })
                                                            default_logger.info(f"[饿了么折扣匹配] 已更新SKU {sku_index} 的折扣价为: {discount['lowestActPrice']}")
                                                            sku_found = True
                                                            break

                                                    if not sku_found:
                                                        default_logger.warning(f"[饿了么折扣匹配] 未找到条码 {barCode} 对应的SKU")
                        
                            default_logger.info(f"成功获取 {len(all_discounts)} 个折扣信息")
                        else:
                            default_logger.warning(f"店铺 {store['name']} 没有获取到商品条码信息，跳过折扣处理")
                            all_discounts = []
                    
                except Exception as e:
                    default_logger.error(f"获取饿了么折扣信息失败: {e}")
                    default_logger.error(f"错误详情: {traceback.format_exc()}")
            
            default_logger.info(f"成功获取门店 {store['name']} 的商品数据: {len(products_map)} 个商品")
            return list(products_map.values())

        except AuthenticationExpiredError as e:
            # 认证过期，显示友好提示
            default_logger.warning(f"门店 {store['name']} 认证过期: {e.message}")

            # 使用InfoBar显示给用户
            from PyQt5.QtWidgets import QApplication
            app = QApplication.instance()
            if app and app.activeWindow():
                InfoBar.warning(
                    title="认证过期",
                    content=e.message,
                    orient=1,
                    isClosable=True,
                    position=InfoBarPosition.TOP,
                    duration=5000,
                    parent=app.activeWindow()
                )

            return []  # 返回空列表，表示没有找到商品
        except Exception as e:
            default_logger.error(f"获取门店商品数据失败 {store['name']}: {e}")
            return []
            
    async def sync_products(self, stores: List[Dict], keyword: str = '') -> List[Dict]:
        """同步多个门店的商品数据"""
        try:
            # 加载缓存
            products_cache = self.load_cache(self.products_cache_file)
            
            # 并发获取所有门店的商品数据
            tasks = [self.get_store_products(store,keyword) for store in stores]
            products_list = await asyncio.gather(*tasks)
            
            # 合并所有商品数据
            all_products = []
            for products in products_list:
                all_products.extend(products)
            
            # 更新缓存
            new_cache = {
                'last_update': datetime.now().isoformat(),
                'products': {p['id']: p for p in all_products}
            }
            self.save_cache(self.products_cache_file, new_cache)
            
            # 根据关键字筛选
            if keyword:
                keyword = keyword.lower()
                all_products = [
                    p for p in all_products 
                    if keyword in p['name'].lower() or 
                       keyword in p.get('store', {}).get('name', '').lower()
                ]
            
            return all_products
            
        except Exception as e:
            default_logger.error(f"同步商品数据失败: {e}")
            return []
            
    def format_product_data(self, product: Dict) -> List:
        """格式化商品数据为表格显示格式"""
        return [
            product['name'],
            product['id'],
            f"{product['store']['name']} ({product['store']['platform']})",
            str(product['price']),
            str(product.get('discount_price', '')),
            str(product['stock']),
            str(product.get('order_limit', '')),
            str(product.get('sort_index', '')),
            str(product.get('min_order_count', 1)),
            '上架' if product['status'] == 1 else '下架',
            product.get('image', '')
        ] 