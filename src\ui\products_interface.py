from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer, QRectF  # 添加 QRectF
from PyQt5.QtGui import QPixmap, QDoubleValidator, QIntValidator, QPainter, QColor, QFont, QImage
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTreeWidget, QTreeWidgetItem, 
    QTableWidget, QTableWidgetItem, QHeaderView, QLabel, QComboBox, 
    QSplitter, QFrame, QLineEdit, QButtonGroup, QDialog, QAbstractItemView,
    QSpinBox, QCheckBox, QProgressBar,QMessageBox,QPushButton,QFileDialog,QLabel,
    QMenu, QAction
)
from qfluentwidgets import (PushButton, SearchLineEdit, ComboBox, InfoBar, TableWidget, 
                          MessageBox, TreeWidget, ImageLabel, PrimaryPushButton, 
                          LineEdit, ProgressBar, InfoBarPosition, StateToolTip)
import asyncio
import requests
from io import BytesIO
import json
import os
import aiohttp
import aiofiles
import hashlib
from sqlalchemy import and_, func  # 添加 func 导入
from typing import Dict, List
from src.ui.rank_item_eleme import generate_sort_params_by_list
from src.ui.rank_move import rank_move

from src.api.eleme import ElemeAPI
from src.api.base_api import AuthenticationExpiredError
from src.services.data_sync import DataSyncService
from src.models.store_data import get_session, Product, Category, ProductSku, Discount, Store, get_session_context
from src.api.store_manager import StoreManager
from src.ui.paginator import Paginator
from src.utils.logger import default_logger
from src.api.meituan_api import MeituanAPI
from src.utils.mtgsig import MtgsigGenerator
from qasync import QEventLoop, asyncSlot, asyncClose, QApplication
from PyQt5.QtCore import QTimer
from src.ui.data_sync_thread import DataSyncThread
from src.ui.dialogs import ProductNameManagement_ui  # New import for the extracted class
from .components.editable_label import EditableLabel
from .components.status_button import StatusButton
from .components.cancel_discount_button import CancelDiscountButton
from .components.multi_spec_widget import MultiSpecWidget
from .components.spec_info_widget import SpecInfoWidget
from PyQt5.QtCore import pyqtSignal as Signal
from .components.loading_mask import LoadingMask  # 添加导入
# from src.ui.components.batch_operation_thread import BatchOperationThread
from qfluentwidgets import MessageBox, InfoBar, InfoBarPosition
from src.ui.dialogs.batch_category_edit import BatchCategoryEditDialog  # 添加导入
from .components.category_tree import CategoryTree  # 添加导入
from src.ui.dialogs.store_selector import StoreSelectorDialog  # 添加导入
from src.ui.dialogs.sync_progress_dialog import SyncProgressDialog  # 添加导入
from .components.product_name_manager import ProductNameManager
import time
from src.ui.dialogs.product_excel_download_dialog import ProductExcelDownloadDialog
from .components.image_label import ImageLabel  # 新增导入ImageLabel





class ProductsInterface(QWidget):
    def __init__(self, sign_generator=None, loop=None):
        super().__init__()
        self.sign_generator = sign_generator
        self.api = None
        self.current_api = None  # 添加当前API实例
        self.current_platform = None  # 添加当前平台
        self.loop = loop or asyncio.get_event_loop()
        self.current_store_id = None
        self.current_category_id = None  # 添加当前分类ID的跟踪
        self.download_tasks = []
        
        # 创建加载遮罩层
        self.loading_mask = LoadingMask(self)
        
        self.root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        self.cache_file = os.path.join(self.root_dir, 'cache', 'store_cache.json')
        self.image_cache_dir = os.path.join(self.root_dir, 'cache', 'images')
        os.makedirs(self.image_cache_dir, exist_ok=True)
        
        # 添加排序相关的状态变量
        self.is_sequence_editing = False  # 是否处于排序编辑模式
        self.original_table_data = []  # 存储进入排序模式时的原始数据
        self.last_selected_stores = set()  # 记住上次选择的门店
        self.column_visibility = []  # 存储列的可见性状态
        
        # 添加折扣价格备份相关的状态变量
        self.discount_backup_file = None
        self.is_restoring_discount = False
        self.restore_progress = 0
        self.restore_total = 0
        self.restore_results = []
        
        # 添加同步线程列表
        self.sync_threads = []

        self.init_ui()
        self.load_stores()
        
        # 连接分页器的信号
        self.paginator.page_changed.connect(self.on_page_changed)
        self.paginator.page_size_changed.connect(self.on_page_size_changed)
    
    def init_ui(self):
        """初始化界面"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)  # 设置边距
        main_layout.setSpacing(10)  # 设置组件间距
        
        # 顶部工具栏
        tool_layout = QHBoxLayout()
        tool_layout.setContentsMargins(0, 0, 0, 0)
        
        # 门店选择
        self.store_combo = ComboBox(self)
        self.store_combo.setMinimumWidth(200)
        self.store_combo.setPlaceholderText('请选择门店')
        self.store_combo.currentIndexChanged.connect(self._handle_store_selected)
        tool_layout.addWidget(self.store_combo)

        # 同步门店按钮
        self.sync_store_button = PrimaryPushButton('同步门店', self)
        self.sync_store_button.clicked.connect(self._handle_sync_store_click)
        tool_layout.addWidget(self.sync_store_button)

        # 同步数据按钮
        self.sync_button = PrimaryPushButton('同步商品数据', self)
        self.sync_button.setEnabled(False)
        self.sync_button.clicked.connect(self.handle_sync_button_click)
        tool_layout.addWidget(self.sync_button)
        
        # 批量操作按钮组
        self.batch_buttons_layout = QHBoxLayout()
        self.batch_buttons_layout.setContentsMargins(0, 0, 0, 0)
        
        # 批量上架按钮
        self.batch_online_button = PushButton('批量上架', self)
        self.batch_online_button.clicked.connect(lambda: self._handle_batch_status_change(1))
        self.batch_online_button.setVisible(False)
        self.batch_buttons_layout.addWidget(self.batch_online_button)
        
        # 批量下架按钮
        self.batch_offline_button = PushButton('批量下架', self)
        self.batch_offline_button.clicked.connect(lambda: self._handle_batch_status_change(0))
        self.batch_offline_button.setVisible(False)
        self.batch_buttons_layout.addWidget(self.batch_offline_button)
        
        # 批量修改分类按钮
        self.batch_category_button = PushButton('批量修改分类', self)
        self.batch_category_button.clicked.connect(lambda: QTimer.singleShot(0, lambda: asyncio.create_task(self.handle_batch_category_change())))
        self.batch_category_button.setVisible(False)
        self.batch_buttons_layout.addWidget(self.batch_category_button)
        
        # 取消选择按钮
        self.cancel_select_button = PushButton('取消选择', self)
        self.cancel_select_button.clicked.connect(self.cancel_selection)
        self.cancel_select_button.setVisible(False)
        self.batch_buttons_layout.addWidget(self.cancel_select_button)
        
        tool_layout.addLayout(self.batch_buttons_layout)
        
        # 同步进度条
        self.progress_bar = ProgressBar(self)
        self.progress_bar.setFixedWidth(200)  # 设置进度条宽度
        self.progress_bar.setVisible(False)  # 初始时隐藏进度条
        tool_layout.addWidget(self.progress_bar)
        
        # 进度文本标签
        self.progress_label = QLabel('', self)
        self.progress_label.setVisible(False)  # 初始时隐藏标签
        tool_layout.addWidget(self.progress_label)
        
        # 在工具栏添加排序相关按钮
        self.edit_sequence_btn = PushButton("修改排序")
        self.edit_product_name_btn = PushButton("标题管理")
        self.save_sequence_btn = PushButton("保存")
        self.cancel_sequence_btn = PushButton("取消")
        
        # 设置按钮样式
        self.save_sequence_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 5px 10px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        
        self.cancel_sequence_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 5px 10px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
        """)
        
        # 初始时隐藏保存和取消按钮
        self.save_sequence_btn.hide()
        self.cancel_sequence_btn.hide()
        
        # 将按钮添加到工具栏
        tool_layout.addWidget(self.edit_sequence_btn)
        tool_layout.addWidget(self.edit_product_name_btn)
        tool_layout.addWidget(self.save_sequence_btn)
        tool_layout.addWidget(self.cancel_sequence_btn)
        
        # 连接按钮信号
        self.edit_sequence_btn.clicked.connect(self.enter_sequence_edit_mode)
        self.edit_product_name_btn.clicked.connect(self.enter_product_name_edit_mode)
        self.save_sequence_btn.clicked.connect(lambda: asyncio.create_task(self.save_sequence()))
        self.cancel_sequence_btn.clicked.connect(self.cancel_sequence_edit)
        
        tool_layout.addStretch()
        main_layout.addLayout(tool_layout, stretch=0)  # 工具栏不拉伸
        
        # 分割器
        splitter = QSplitter(Qt.Horizontal)
        splitter.setChildrenCollapsible(False)  # 禁止完全折叠
        
        # 左侧分类面板
        category_widget = QWidget()
        left_layout = QVBoxLayout(category_widget)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(5)
        
        # 使用新的CategoryTree组件
        self.category_tree = CategoryTree(self)
        self.category_tree.category_selected.connect(self._handle_category_selected)
        left_layout.addWidget(self.category_tree)
        
        # 右侧商品面板
        product_widget = QWidget()
        right_layout = QVBoxLayout(product_widget)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(5)
        
        # 搜索框
        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(0, 0, 0, 0)
        self.search_edit = SearchLineEdit(self)
        self.search_edit.setPlaceholderText('搜索商品')
        self.search_edit.returnPressed.connect(lambda: asyncio.create_task(self.refresh_table()))
        search_layout.addWidget(self.search_edit)
        right_layout.addLayout(search_layout)
        
        # 添加状态筛选按钮组
        status_layout = QHBoxLayout()
        status_layout.setContentsMargins(0, 0, 0, 10)  # 设置下边距
        
        # 添加全选的checkbox
        self.select_all_checkbox = QCheckBox("全选", self)
        self.select_all_checkbox.stateChanged.connect(self.toggle_select_all)
        status_layout.addWidget(self.select_all_checkbox)
        
        # 创建状态按钮组
        self.status_button_group = QButtonGroup(self)
        self.status_button_group.setExclusive(True)  # 设置为互斥
        
        # 定义状态选项
        status_options = [
            {'text': '全部商品', 'value': None},
            {'text': '售卖中', 'value': 0},  # 1表示上架
            {'text': '已售罄', 'value': 2},  # 2表示售罄（特殊状态）
            {'text': '已下架', 'value': 1},  # 0表示下架
        ]
        
        # 创建状态按钮
        for option in status_options:
            btn = PushButton(option['text'], self)
            btn.setCheckable(True)  # 设置为可选中
            btn.setProperty('value', option['value'])  # 存储状态值
            status_layout.addWidget(btn)
            self.status_button_group.addButton(btn)
            
            # 设置样式
            btn.setStyleSheet("""
                QPushButton {
                    padding: 4px 12px;
                    border: 1px solid #dcdcdc;
                    border-radius: 4px;
                    background: white;
                }
                QPushButton:checked {
                    background: #e6f7ff;
                    border-color: #1890ff;
                    color: #1890ff;
                }
                QPushButton:hover {
                    border-color: #1890ff;
                    color: #1890ff;
                }
            """)
        
        # 默认选中"全部商品"
        self.status_button_group.buttons()[0].setChecked(True)
        
        # 连接状态切换信号
        self.status_button_group.buttonClicked.connect(
            lambda btn: QTimer.singleShot(0, lambda: asyncio.create_task(self._handle_status_filter()))
        )
        
        status_layout.addStretch()  # 添加弹性空间
        right_layout.addLayout(status_layout)
        
        # 商品表格
        self.table = TableWidget(self)
        self.table.setColumnCount(12)
        self.table.setHorizontalHeaderLabels([
            '商品ID', '商品名称', '图片', '规格', '原价', '折扣价格',
            '折扣排序', '限购数量', '取消折扣', '库存', '上下架', '编辑',
            
        ])
        
        # 设置表格选择模式为多选
        self.table.setSelectionMode(QTableWidget.MultiSelection)
        self.table.setSelectionBehavior(QTableWidget.SelectRows)  # 整行选择
        
        # 连接选择变化信号
        self.table.itemSelectionChanged.connect(self.handle_selection_changed)
        
        # 设置表格列宽
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeToContents)
        self.table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Fixed)  # 商品名称列固定宽度
        self.table.setColumnWidth(1, 200)  # 设置商品名称列宽度为200
        self.table.horizontalHeader().setSectionResizeMode(2, QHeaderView.Fixed)  # 图片列固定宽度
        self.table.setColumnWidth(2, 120)  # 设置图片列宽度
        
        # 设置表格行高
        self.table.verticalHeader().setSectionResizeMode(QHeaderView.Fixed)
        self.table.verticalHeader().setDefaultSectionSize(120)  # 设置行高
        
        # 设置表格其他属性
        # self.table.setShowGrid(True)  # 显示网格线
        self.table.setAlternatingRowColors(True)  # 交替行颜色
        self.table.setWordWrap(True)  # 允许文字换行
        
        right_layout.addWidget(self.table)
        
        # 分页器
        self.paginator = Paginator(self)
        right_layout.addWidget(self.paginator)
        
        # 添加部件到分割器
        splitter.addWidget(category_widget)
        splitter.addWidget(product_widget)
        
        # 设置分割器初始比例
        splitter.setStretchFactor(0, 1)  # 左侧分类面板比例
        splitter.setStretchFactor(1, 4)  # 右侧商品面板比例
        splitter.setSizes([200, 800])  # 设置初始宽度
        
        # 添加分割器到主布局，并设置拉伸
        main_layout.addWidget(splitter, stretch=1)  # 分割器占满剩余空间
        
        # 添加折扣价格备份和恢复按钮
        self.backup_discount_btn = PushButton('备份折扣价格', self)
        self.restore_discount_btn = PushButton('恢复折扣价格', self)
        self.backup_discount_btn.clicked.connect(self.handle_backup_discount)
        self.restore_discount_btn.clicked.connect(self.handle_restore_discount)
        
        # 将按钮添加到布局中
        self.buttonLayout = QHBoxLayout()
        self.buttonLayout.addWidget(self.backup_discount_btn)
        self.buttonLayout.addWidget(self.restore_discount_btn)
        
        # 将按钮添加到布局中
         # 添加备份商品信息按钮
        self.backup_products_btn = PushButton('备份商品信息', self)
        self.backup_products_btn.clicked.connect(self.handle_backup_products)
        self.product_button_layout = QHBoxLayout()
        self.product_button_layout.addWidget(self.backup_products_btn)

        self.restore_products_btn = PushButton('恢复商品信息', self)
        # self.restore_products_btn.clicked.connect(self.handle_restore_products)
        # 设置为不可点击
        self.restore_products_btn.setEnabled(False)
        self.product_button_layout.addWidget(self.restore_products_btn)
        


        main_layout.addLayout(self.buttonLayout)
        main_layout.addLayout(self.product_button_layout)
        
       
        
 
        
       
    def update_status_counts(self, all_count, selling_count, soldout_count, suspended_count):
        """更新状态按钮显示的数量"""
        # 遍历所有按钮
        for button in self.status_button_group.buttons():
            value = button.property('value')
            if value is None:
                button.setText(f'全部商品 ({all_count})')
            elif value == 0:
                button.setText(f'售卖中 ({selling_count})')
            elif value == 2:
                button.setText(f'已售罄 ({soldout_count})')
            elif value == 1:
                button.setText(f'已下架 ({suspended_count})')

    def toggle_select_all(self, state):
        """全选/取消全选表格中的所有行"""
        for row in range(self.table.rowCount()):
            if state == Qt.Checked:
                self.table.selectRow(row)
            else:
                self.table.clearSelection()

    def enter_product_name_edit_mode(self):
        """进入商品标题编辑模式"""
        try:
            # 获取选中的商品名称列表
            product_name_list = []
            for i in range(self.table.rowCount()):
                if self.table.item(i, 0).isSelected():
                    product_name_list.append(self.table.item(i, 1).text())

            if not product_name_list:
                InfoBar.warning(
                    title='提示',
                    content='请先选择商品',
                    parent=self,
                    position=InfoBarPosition.TOP,
                    duration=2000
                )
                return

            # 创建并显示标题管理窗口
            self.product_name_manager = ProductNameManager(self)
            
            # 加载选中的商品
            self.product_name_manager.load_products(product_name_list)
            
            # 显示窗口
            self.product_name_manager.show()

        except Exception as e:
            default_logger.error(f"打开标题管理窗口失败: {e}")
            InfoBar.error(
                title='错误',
                content=f"打开标题管理窗口失败: {e}",
                parent=self,
                position=InfoBarPosition.TOP,
                duration=2000
            )
    
    def load_stores(self):
        """从缓存文件加载门店列表"""
        try:
            # 检查文件是否存在
            if not os.path.exists(self.cache_file):
                default_logger.error(f"门店缓存文件不存在，路径: {self.cache_file}")
                InfoBar.error(
                    title='错误',
                    content=f'门店缓存文件不存在\n路径: {self.cache_file}',
                    parent=self
                )
                return
                
            # 读取缓存文件
            with open(self.cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
                
            default_logger.info(f"成功读取缓存文件，包含 {len(cache_data.get('stores', {}))} 个门店")
            
            # 清空并添加门店
            self.store_combo.clear()
            self.store_combo.addItem('请选择门店', userData=None)
            
            # 遍历门店数据
            for object_id, store_data in cache_data['stores'].items():
                # 获取cookies
                cookies = cache_data.get('cookies', {}).get(object_id)
                
                # 获取店铺ID (从cookies中获取)
                store_id = None
                if cookies:
                    if store_data['platform'] == '美团':
                        store_id = cookies.get('wmPoiId')
                    elif store_data['platform'] == '饿了么':
                        if store_data['storeName'] == '星辰':
                            store_id = '1078371359'
                        elif store_data['storeName'] == '繁花觅':
                            store_id = '872232395'
                        elif store_data['storeName'] == '繁花里':
                            store_id = '20010011159'






                
                # 显示格式：店铺名称 (平台)
                display_name = f"{store_data['storeName']} ({store_data['platform']})"
                
                # 使用完整数据作为userData，确保包含所有必要的字段
                store_info = {
                    'id': store_id,  # 使用wmPoiId作为店铺ID
                    'object_id': object_id,
                    'cookies': cookies,
                    'platform': store_data['platform'],
                    'name': store_data['storeName']
                }
                
                if store_info['id']:  # 只添加有效的店铺ID
                    self.store_combo.addItem(display_name, userData=store_info)
                else:
                    default_logger.warning(f"店铺 {display_name} 没有有效的店铺ID，已跳过")
                
            default_logger.info(f"成功加载 {self.store_combo.count() - 1} 个门店到下拉框")
                
        except json.JSONDecodeError as e:
            default_logger.error(f"门店缓存文件格式错误: {e}")
            InfoBar.error(
                title='错误',
                content='门店缓存文件格式错误',
                parent=self
            )
        except Exception as e:
            default_logger.error(f"加载门店列表失败: {e}")
            InfoBar.error(
                title='错误',
                content='加载门店列表失败',
                parent=self
            )
    
    def _handle_store_selected(self, index: int):
        """处理门店选择的中间处理函数"""
        # 使用 QTimer 延迟执行异步操作
        QTimer.singleShot(0, lambda: asyncio.create_task(self.on_store_selected(index)))

    async def on_store_selected(self, index: int):
        """门店选择处理"""
        store_data = self.store_combo.currentData()
        # 同时控制两个按钮的状态
        self.sync_button.setEnabled(bool(store_data))
        
        if store_data:
            try:
                # 保存当前门店ID和平台
                self.current_store_id = store_data['id']
                self.current_platform = store_data['platform']  # 保存当前平台

                if store_data['platform'] == '美团':
                    # 初始化API实例
                    self.api = MeituanAPI(
                        cookies=store_data['cookies'],
                        sign_generator=self.sign_generator,
                        store_name=store_data['name']
                    )
                    self.current_api = self.api  # 保存当前API实例
                elif store_data['platform'] == '饿了么':
                    # 初始化API实例
                    self.api = ElemeAPI(
                        cookies=store_data['cookies'],
                        shop_name = store_data['name']
                    )
                    await self.api.initialize()
                    self.current_api = self.api  # 保存当前API实例
                else:
                    InfoBar.error(
                        title='错误',
                        content='暂不支持该平台',
                        parent=self
                    )
                    return
                # 清空现有数据
                self.category_tree.clear()
                self.table.setRowCount(0)
                
                # 加载分类列表
                self.load_categories()
                
                # 刷新商品表格
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    asyncio.create_task(self.refresh_table())
                else:
                    loop.run_until_complete(self.refresh_table())
                
            except Exception as e:
                default_logger.error(f"初始化API失败: {e}")
                InfoBar.error(
                    title='错误',
                    content='初始化API失败',
                    parent=self
                )

    def load_categories(self):
        """加载分类列表"""
        try:
            with get_session_context() as session:
                # 查询所有分类
                categories = session.query(Category).filter_by(store_id=self.current_store_id).all()
                # 将categories按sequence从大到小排序
                categories = sorted(categories, key=lambda x: x.sequence, reverse=False)
                
                if not categories:
                    return
                
                # 转换为CategoryTree需要的格式
                category_list = []
                for category in categories:
                    category_list.append({
                        'id': category.category_id,
                        'name': category.name,
                        'count': category.productCount
                    })
                
                # 设置分类数据
                self.category_tree.set_categories(category_list)
                
        except Exception as e:
            default_logger.error(f"加载分类列表失败: {e}")
            InfoBar.error(
                title='错误',
                content=f"加载分类列表失败: {e}",
                parent=self
            )


    
    def get_cached_image(self, url: str) -> str:
        """获取缓存的图片路径，如果不存在则下载"""
        try:
            # 使用URL的MD5作为文件名
            import hashlib
            filename = hashlib.md5(url.encode()).hexdigest() + '.jpg'
            cache_path = os.path.join(self.image_cache_dir, filename)
            
            # 如果缓存存在且文件大小大于0，直接返回
            if os.path.exists(cache_path) and os.path.getsize(cache_path) > 0:
                return cache_path
            
            # 下载图片
            if 'img.alicdn.com' in url:
                # 设置请求头
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.289 Safari/537.36'}
                response = requests.get(url,headers=headers,timeout=5)
            else:
                response = requests.get(url, timeout=5)
            if response.status_code == 200:
                with open(cache_path, 'wb') as f:
                    f.write(response.content)
                return cache_path
            
            return None
        except Exception as e:
            default_logger.error(f"下载图片失败: {e}, URL: {url}")
            return None
    
    async def refresh_table(self, product_ids: List[str] = None,isall:bool = False):
        """刷新商品表格
        Args:
            product_ids: 要显示的商品ID列表。如果为None，则显示所有商品
        """
        self.table.setUpdatesEnabled(False)
        self.table.setSortingEnabled(False)
        
        # 初始化任务列表
        ui_tasks = []
        download_tasks = []
        # print('product_ids',product_ids)
        
        try:
            # 显示加载遮罩层
            self.loading_mask.show()
            
            with get_session_context() as session:
                # 子查询：获取每个product_id的最小id记录
                # 目的：对于同一个商品ID，只取最早创建的那条记录
                subquery = (
                    session.query(
                        Product.product_id,  # 选择商品ID字段
                        func.min(Product.id).label('min_id')  # 获取每个商品ID的最小id
                    )
                    .filter(Product.store_id == self.current_store_id)  # 过滤当前门店
                    .group_by(Product.product_id)  # 按商品ID分组
                    .subquery()  # 将查询结果转换为子查询
                )

                # 主查询：获取商品及其折扣信息
                query = (
                    session.query(Product, Discount)  # 查询Product和Discount表
                    .join(
                        subquery,
                        and_(
                            Product.product_id == subquery.c.product_id,  # 匹配商品ID
                            Product.id == subquery.c.min_id  # 匹配最小id
                        )
                    )
                    .outerjoin(
                        Discount,
                        and_(
                            Product.store_id == Discount.store_id,  # 匹配门店ID
                            Product.product_id == Discount.product_id,  # 匹配商品ID
                            Product.sku_id == Discount.sku_id  # 新增 sku_id 匹配
                        )
                    )
                    .filter(Product.store_id == self.current_store_id)  # 过滤当前门店
                )
                
                # 如果指定了商品ID列表，则只显示这些商品
                if product_ids is not None:
                    # 确保所有ID都是字符串类型
                    product_ids = [str(pid) for pid in product_ids]
                    query = query.filter(Product.product_id.in_(product_ids))
                
                # 添加搜索过滤
                search_text = self.search_edit.text().strip()
                if search_text:
                    query = query.filter(Product.name.like(f'%{search_text}%'))
                

                
                # 获取分页数据
                if product_ids is not None:
                    products = query.all()
                    # 将products按sequence从大到小排序
                    if not isall:
                        products = sorted(products, key=lambda x: x[0].sequence if x[0].sequence is not None else 999999, reverse=False)
                    else:
                        # 按照discounts表中sort_index排序
                        products = sorted(products, key=lambda x: x[1].sort_index if x[1] and x[1].sort_index is not None else 999999, reverse=False)
                else:
                    page_size = int(self.paginator.page_size_combo.currentText())
                    offset = (self.paginator.current_page - 1) * page_size
                    products = query.offset(offset).limit(page_size).all()
                    # 将products按sequence从大到小排序
                    products = sorted(products, key=lambda x: x[0].sequence if x[0].sequence is not None else 999999, reverse=False)
                
                # 清空表格并设置行数
                self.table.setRowCount(len(products))
                
                # 用于记录已显示的商品ID
                displayed_products = set()
                
                # 实际显示的行索引
                display_row = 0
                
                # 批量创建和更新UI组件
                for row_idx, (product, discount) in enumerate(products):
                    if product.product_id in displayed_products:
                        continue
                    displayed_products.add(product.product_id)
                    
                    # 使用display_row而不是row_idx来设置表格内容
                    # 立即设置基本信息（这部分很快）
                    self.table.setItem(display_row, 0, QTableWidgetItem(str(product.product_id)))
                    self.table.setItem(display_row, 1, QTableWidgetItem(product.name))
                    
                    # 处理图片
                    if product.picture:
                        self.update_image_cell(display_row, product.picture, product.pictures)
                    else:
                        placeholder = QTableWidgetItem("无图片")
                        placeholder.setTextAlignment(Qt.AlignCenter)
                        self.table.setItem(display_row, 2, placeholder)
                    
                    # 获取当前商品的规格信息
                    specs = session.query(ProductSku).filter_by(
                        store_id=self.current_store_id,
                        product_id=product.product_id
                    ).all()
                    
                    if specs:
                        # 规格显示
                        specs_widget = MultiSpecWidget([{'spec': spec.spec} for spec in specs])
                        self.table.setCellWidget(display_row, 3, specs_widget)

                        # 设置product_id
                        platform = self.store_combo.currentData().get('platform')
                        if platform == "美团":
                            product_id = specs[0].sku_id
                        elif platform == "饿了么":
                            product_id = specs[0].product_id
                        
                        # 原价编辑标签
                        prices_widget = MultiSpecWidget([{
                            'spec': EditableLabel(
                                api=self.api,
                                value=spec.price if spec.price is not None else 0,
                                product_id=product_id,
                                platform=platform,
                                store_id=self.current_store_id,
                                is_number=True,
                                column=4,
                            )
                        } for spec in specs])
                        self.table.setCellWidget(display_row, 4, prices_widget)

                        discounts = session.query(Discount).filter(
                            Discount.store_id == self.current_store_id,
                            Discount.product_id == product.product_id
                        ).all()
                    
                        # 折扣价格
                        discounts_widget = MultiSpecWidget([{
                            'spec': EditableLabel(
                                api=self.api,
                                value=next(
                                    (d.discount_price for d in discounts if d.sku_id == spec.sku_id),
                                    ""
                                ),
                                product_id=spec.sku_id,
                                platform=platform,
                                store_id=self.current_store_id,
                                is_number=True,
                                column=5,
                            )
                        } for spec in specs])
                        # print('折扣价格',discount.discount_price)
                        
                        self.table.setCellWidget(display_row, 5, discounts_widget)
                        
                        # 折扣排序
                        sort_widget = MultiSpecWidget([{
                            'spec': EditableLabel(
                                api=self.api,
                                value=discount.sort_index if discount else 0,
                                product_id=product_id,
                                platform=platform,
                                store_id=self.current_store_id,
                                is_number=True,
                                column=6,
                            )
                        } for spec in specs])
                        self.table.setCellWidget(display_row, 6, sort_widget)
                        
                        # 限购数量
                        limit_widget = MultiSpecWidget([{
                            'spec': EditableLabel(
                                api=self.api,
                                value=discount.order_limit if discount else -1,
                                product_id=discount.itemact_id if discount else 0,
                                platform=platform,
                                store_id=self.current_store_id,
                                is_number=True,
                                column=7,
                            )
                        } for spec in specs])
                        self.table.setCellWidget(display_row, 7, limit_widget)
                    
                    # 取消折扣按钮
                        cancel_buttons = []
                        for spec in specs:
                            if discount and discount.status == 1:
                                btn = CancelDiscountButton(
                                    store_id=self.current_store_id,
                                    product_id=product.product_id,
                                    sku_id=spec.sku_id
                                )
                                cancel_buttons.append({'spec': btn})
                            else:
                                label = QLabel("-")
                                cancel_buttons.append({'spec': label})
                        
                        cancel_widget = MultiSpecWidget(cancel_buttons)
                        self.table.setCellWidget(display_row, 8, cancel_widget)
                        
                        # 库存
                        stocks_widget = MultiSpecWidget([{
                            'spec': EditableLabel(
                                api=self.api,
                                value=spec.stock if spec.stock is not None else 0,
                                product_id=product_id,
                                platform=platform,
                                store_id=self.current_store_id,
                                is_number=True,
                                is_stock=True,
                                column=9,
                            )
                        } for spec in specs])
                        self.table.setCellWidget(display_row, 9, stocks_widget)
                        
                        # 上下架状态
                        if product.status is not None:
                            if platform == "美团":
                                status_btn = StatusButton(
                                    api=self.api,
                                    status=product.status,
                                    product_id=product.product_id,
                                    platform=platform,
                                    sku_id=[spec.sku_id for spec in specs],
                                    store_id=self.current_store_id,
                                    )
                            elif platform == "饿了么":
                                status_btn = StatusButton(
                                    api=self.api,
                                    status=product.status,
                                    product_id=product.product_id,
                                    platform=platform,
                                    store_id=self.current_store_id,
                                    )
                            self.table.setCellWidget(display_row, 10, status_btn)
                        else:
                            status_label = QLabel("-")
                            status_label.setAlignment(Qt.AlignCenter)
                            self.table.setCellWidget(display_row, 10, status_label)
                        
                        # 编辑按钮
                        edit_btn = PushButton('编辑', self)
                        edit_btn.clicked.connect(
                            lambda checked, row=display_row: QTimer.singleShot(
                                0, lambda: asyncio.create_task(self.on_edit_clicked(row))
                            )
                        )
                        self.table.setCellWidget(display_row, 11, edit_btn)
                        
                        
                        
                        # 调整行高
                        total_height = sum(spec_widget.sizeHint().height() for spec_widget in specs_widget.findChildren(SpecInfoWidget))
                        self.table.setRowHeight(display_row, max(total_height + 10, 120))
                        
                    # 增加display_row计数
                    display_row += 1

                # 如果实际显示的行数少于预期，调整表格行数
                if display_row < self.table.rowCount():
                    self.table.setRowCount(display_row)
                
            # 重新启用表格更新
            self.table.setUpdatesEnabled(True)
            self.table.setSortingEnabled(True)
                
        except Exception as e:
            self.table.setUpdatesEnabled(True)
            self.table.setSortingEnabled(True)
            default_logger.error(f"刷新商品表格失败: {e}")
            import traceback
            default_logger.error(f"详细错误: {traceback.format_exc()}")
            InfoBar.error(
                title='错误',
                content=f"刷新商品表格失败: {e}",
                parent=self
            )
        finally:
            # 隐藏加载遮罩层
            self.loading_mask.hide()

    async def on_edit_clicked(self, row: int):
        """处理编辑按钮点击"""
        try:
            # 获取商品信息
            product_id = self.table.item(row, 0).text()
            product_name = self.table.item(row, 1).text()
            platform = self.store_combo.currentData().get('platform')
            store_name = self.store_combo.currentData().get('name')
            
            # 获取cookies
            cookies = self.store_combo.currentData().get('cookies')
            if not cookies:
                raise Exception(f"未找到门店 {store_name} 的cookies")
        
            from src.ui.meituaneditwindow import MeituanEditWindow
            if platform == '美团':
                # 获取wmPoiId
                wm_poi_id = cookies.get('wmPoiId')
                if not wm_poi_id:
                    raise Exception(f"未找到门店 {store_name} 的wmPoiId")
                
                # 打开美团编辑页面
                url = f'https://shangoue.meituan.com/reuse/sc/product/views/core/pages/product/edit?spuId={product_id}&wmPoiId={wm_poi_id}&from=single'
                edit_window = MeituanEditWindow(url, cookies, store_name, platform, self)
                edit_window.show()
                
            elif platform == '饿了么':
                # 打开饿了么编辑页面
                url = f'https://nr.ele.me/eleme-b-newretail/commodity_pc/ebai.html?gray=true&ebQuery=false&nav=agentGroup&ebGroup=commodity#/store/edit?itemId={product_id}'
                edit_window = MeituanEditWindow(url, cookies, store_name, platform, self)
                edit_window.show()
            
            InfoBar.success(
                title='成功',
                content=f"已打开商品 {product_name} 的编辑页面",
                parent=self
            )
            
        except Exception as e:
            default_logger.error(f"打开编辑页面失败: {e}")
            InfoBar.error(
                title='错误',
                content=f"打开编辑页面失败: {e}",
                parent=self
            )
    
    async def wait_for_downloads(self):
        """等待所有下载任务完成"""
        try:
            await asyncio.gather(*self.download_tasks)
        except asyncio.CancelledError:
            pass  # 忽略取消的任务
        except Exception as e:
            default_logger.error(f"下载任务出错: {e}")
        finally:
            self.download_tasks.clear()

    def _handle_category_selected(self, item: QTreeWidgetItem, column: int):
        """处理分类选择的中间处理函数"""
        # 使用 QTimer 延迟执行异步操作
        QTimer.singleShot(0, lambda: asyncio.create_task(self.on_category_selected(item, column)))

    async def on_category_selected(self, item: QTreeWidgetItem, column: int):
        """分类选择事件处理"""
        try:
            # 显示加载遮罩层
            self.loading_mask.show()
            
            # 获取分类ID
            self.current_item = item
            self.current_category_name = item.text(0).split(' (')[0]
            category_id = item.data(0, Qt.UserRole)
            self.current_category_id = category_id  # 保存当前分类ID
            
            # 更新搜索框提示文本
            category_name = item.text(0).split(' (')[0]  # 移除商品数量
            self.search_edit.setPlaceholderText(f'在{category_name}中搜索')

            platform = self.store_combo.currentData().get('platform')
            selected_button = self.status_button_group.checkedButton()
            
            # 如果选择了具体分类（不是"全部商品"）
            if category_id is not None:
                
                
                # 获取分页器中选择的每页数量
                page_size = int(self.paginator.page_size_combo.currentText())
                
                # 通过API获取分类商品列表
                if platform == "美团":
                    # 获取当前选中的状态按钮值
                    
                    state = 0  # 默认全部商品
                    if selected_button:
                        status_value = selected_button.property('value')
                        if status_value == 0:  # 售卖中
                            state = 1
                        elif status_value == 2:  # 已售罄
                            state = 3
                        elif status_value == 1:  # 已下架
                            state = 2
                        else:
                            state = 0
                    
                    # 获取商品数据
                    products_data = await self.api.get_product_list(
                        page_num=self.paginator.current_page,
                        page_size=page_size,
                        tag_id=category_id,
                        state=state
                    )
                    
                    if products_data and 'data' in products_data:
                        all_count = products_data['data'].get('queryCount', {}).get('all', 0)
                        selling_count = products_data['data'].get('queryCount', {}).get('selling', 0)
                        soldout_count = products_data['data'].get('queryCount', {}).get('sellOut', 0)
                        suspended_count = products_data['data'].get('queryCount', {}).get('suspendedSale', 0)
                        # 更新状态按钮显示的数量
                        self.update_status_counts(
                            all_count=all_count,
                            selling_count=selling_count,
                            soldout_count=soldout_count,
                            suspended_count=suspended_count
                        )

                        # 更新分页器状态
                        total_count = products_data['data'].get('totalCount', 0)
                        self.paginator.update_state(total_count)
                        
                        # 获取商品列表
                        products = products_data['data'].get('productList', [])
                        self.products = products
                        print('products的数量',len(products))
                        # 获取商品ID列表，确保所有ID都是字符串类型
                        product_ids = [str(prod['id']) for prod in products]
                        # print('product_ids is :',product_ids)
                        
                        # 更新数据库中的商品顺序
                        with get_session_context() as session:
                            for index, product_id in enumerate(product_ids):
                                # 更新所有匹配的记录
                                session.query(Product).filter_by(
                                    store_id=self.current_store_id,
                                    product_id=str(product_id)
                                ).update(
                                    {"sequence": index},
                                    synchronize_session=False
                                )
                                # 打印日志用于调试
                                products = session.query(Product).filter_by(
                                    store_id=self.current_store_id,
                                    product_id=str(product_id)
                                ).all()
                                # for product in products:
                                    # print('product_id', product_id)
                                    # print(product.name, index)
                            session.commit()
                        
                        # 使用商品ID列表刷新表格
                        await self.refresh_table(product_ids)
                        
                elif platform == "饿了么":
                    selected_button = self.status_button_group.checkedButton()
                    state = None  # 默认全部商品
                    quantity = None
                    if selected_button:
                        status_value = selected_button.property('value')
                        if status_value == 0:  # 售卖中
                            state = [0]
                        elif status_value == 2:  # 已售罄
                            quantity = 0
                        elif status_value == 1:  # 已下架
                            state = [-2,-3]
                    # 获取商品数据
                    print('category_id',category_id)
                    if category_id == '0':
                        products_data = await self.api.product.get_product_list(
                            page_num=self.paginator.current_page,
                            page_size=page_size,
                            hasFrontCategory=False
                        )
                        print('products_data',products_data)
                    else:
                        products_data = await self.api.product.get_product_list_by_category(
                            category_id=category_id,
                            page_num=1,  # 获取所有数据
                            page_size=1000  # 设置一个较大的值以获取所有数据
                            )
                    # 
                    
                    if products_data and 'data' in products_data:
                        # 获取商品列表
                        all_products = products_data['data'].get('data', [])
                        # 计算总数all_count、selling_count、soldout_count、suspended_count
                        total_count = len(all_products)
                        selling_count = len([prod for prod in all_products if prod['status'] == 0])
                        soldout_count = len([prod for prod in all_products if prod['quantity'] == 0])
                        suspended_count = len([prod for prod in all_products if prod['status'] in [-2,-3]])
                        # 更新状态按钮显示的数量
                        
                        self.update_status_counts(
                            all_count=total_count,
                            selling_count=selling_count,
                            soldout_count=soldout_count,
                            suspended_count=suspended_count
                        )


                        # 根据state和quantity过滤商品
                        if state is not None:   
                            all_products = [prod for prod in all_products if prod['status'] in state]
                        if quantity is not None:
                            all_products = [prod for prod in all_products if prod['quantity'] == quantity]
                        self.products = all_products
                        total_count = len(all_products)  # 使用实际的商品总数
                        
                        # 更新分页器状态
                        self.paginator.update_state(total_count)
                        
                        # 获取所有商品ID列表，确保所有ID都是字符串类型
                        product_ids = [str(prod['itemId']) for prod in all_products]
                        
                        # 更新数据库中的商品顺序
                        with get_session_context() as session:
                            for index, product_id in enumerate(product_ids):
                                product = session.query(Product).filter_by(
                                    store_id=self.current_store_id,
                                    product_id=str(product_id)
                                ).first()
                                if product:
                                    product.sequence = index
                            session.commit()
                        
                        # 计算当前页的数据范围
                        page_size = int(self.paginator.page_size_combo.currentText())
                        start_idx = (self.paginator.current_page - 1) * page_size
                        end_idx = min(start_idx + page_size, total_count)
                        
                        # 获取当前页的商品ID列表
                        current_page_product_ids = product_ids[start_idx:end_idx]
                        
                        # 使用商品ID列表刷新表格
                        await self.refresh_table(current_page_product_ids)
            else:
                # 显示所有商品，获取总数并更新分页器
                with get_session_context() as session:
                    total_count = session.query(Product).filter_by(
                        store_id=self.current_store_id
                    ).count()
                    self.paginator.update_state(total_count)
                
                    soldout_count = session.query(Product).filter_by(
                        store_id=self.current_store_id,
                        total_stock=0
                    ).count()

                    selling_count = session.query(Product).filter_by(
                        store_id=self.current_store_id,
                        status=0
                    ).count()

                    suspended_count = session.query(Product).filter(
                        Product.store_id == self.current_store_id,
                        Product.status.in_([1,-2, -3])
                    ).count()

                print('total_count',total_count)
                print('selling_count',selling_count)
                print('soldout_count',soldout_count)
                print('suspended_count',suspended_count)
                
                # 更新状态按钮显示的数量
                self.update_status_counts(
                    all_count=total_count,
                    selling_count=selling_count,
                    soldout_count=soldout_count,
                    suspended_count=suspended_count
                )
                state = None
                stock = None
                if platform == "美团":
                    if selected_button:
                        status_value = selected_button.property('value')
                        if status_value == 0:  # 售卖中
                            state = 0
                        elif status_value == 2:  # 已售罄
                            stock = 0
                        elif status_value == 1:  # 已下架
                            state = 1
                        else:
                            state = 0
                elif platform == "饿了么":
                    if selected_button:
                        status_value = selected_button.property('value')
                        if status_value == 0:  # 售卖中
                            state = 0
                        elif status_value == 2:  # 已售罄
                            stock = 0
                        elif status_value == 1:  # 已下架
                            state = [-2,-3]
                        else:
                            state = 0
                # 获取数据库中对应状态的商品ID列表
                with get_session_context() as session:
                    query = session.query(Product).filter_by(store_id=self.current_store_id)
                    
                    if state is not None:
                        if isinstance(state, list):  # 如果是列表（饿了么的下架状态）
                            query = query.filter(Product.status.in_(state))
                        else:  # 如果是单个值
                            query = query.filter_by(status=state)
                    elif stock is not None:
                        query = query.filter_by(total_stock=stock)
                    
                    
                    if platform == "美团":
                        # 使用子查询获取最新的折扣记录
                        query = (session.query(Product, Discount)
                            .outerjoin(
                                Discount,
                                (Product.store_id == Discount.store_id) &
                                (Product.product_id == Discount.product_id)
                            )
                            .filter(Product.store_id == self.current_store_id)
                            .order_by(
                                # 有折扣的排在前面
                                Discount.sort_index.isnot(None).desc(),
                                # 按照折扣的sort_index排序
                                Discount.sort_index.asc().nullslast(),
                                # 最后按照商品ID排序
                                Product.product_id.asc()
                            ))
                        
                        # 打印生成的SQL语句进行调试
                        default_logger.debug(f"Generated SQL: {query}")
                        
                        # 执行查询并获取结果
                        results = query.all()
                        
                        # 提取商品ID，保持排序顺序
                        product_ids = []
                        seen_ids = set()  # 用于去重
                        for product, _ in results:
                            if product.product_id not in seen_ids:
                                product_ids.append(product.product_id)
                                seen_ids.add(product.product_id)
                    else:
                        products = query.all()
                        product_ids = [product.product_id for product in products]
                   
                    # 根据分页器的当前页和每页数量，获取当前页的商品ID列表
                    page_size = int(self.paginator.page_size_combo.currentText())
                    start_idx = (self.paginator.current_page - 1) * page_size
                    end_idx = min(start_idx + page_size, len(product_ids))
                    product_ids = product_ids[start_idx:end_idx]

                    
                
                # 显示所有商品
                print('product_ids',product_ids)
                await self.refresh_table(product_ids,isall=True)
                
        except Exception as e:
            import traceback
            error_stack = traceback.format_exc()
            default_logger.error(f"处理分类选择失败: {e}\n{error_stack}")
            InfoBar.error(
                title='错误',
                content=f"处理分类选择失败: {e}",
                parent=self
            )
        finally:
            # 隐藏加载遮罩层
            self.loading_mask.hide()
    
    def filter_categories(self, keyword: str):
        """过滤分类"""
        self.category_tree.filter_categories(keyword)
    
    def search_products(self, keyword: str):
        """搜索商品"""
        for row in range(self.table.rowCount()):
            show = True
            if keyword:
                name_item = self.table.item(row, 1)
                if name_item:
                    show = keyword.lower() in name_item.text().lower()
            
            self.table.setRowHidden(row, not show)
    
    async def change_product_status(self, product_id: str, current_status: int):
        """修改商品状态"""
        try:
            with get_session_context() as session:
                # 更新数据库中的状态
                product = session.query(Product).filter_by(
                    store_id=self.current_store_id,
                    product_id=product_id
                ).first()
                
                if product:
                    new_status = 0 if current_status == 1 else 1
                    product.status = new_status
                    
                    # 刷新表格
                    await self.refresh_table()
                    
                    InfoBar.success(
                        title='成功',
                        content=f"商品状态已更新为{'上架' if new_status == 1 else '下架'}",
                        parent=self
                    )
                else:
                    InfoBar.error(
                        title='错误',
                        content="未找到商品",
                        parent=self
                    )
                    
        except Exception as e:
            default_logger.error(f"修改商品状态失败: {e}")
            InfoBar.error(
                title='错误',
                content=f"修改商品状态失败: {e}",
                parent=self
            )

    async def cancel_discount(self, product_id: str):
        """取消商品折扣"""
        try:
            with get_session_context() as session:
                # 删除折扣记录
                session.query(Discount).filter_by(
                    store_id=self.current_store_id,
                    product_id=product_id
                ).delete()
                
                # 刷新表格
                await self.refresh_table()
                
                InfoBar.success(
                    title='成功',
                    content="商品折扣已取消",
                    parent=self
                )
                
        except Exception as e:
            default_logger.error(f"取消商品折扣失败: {e}")
            InfoBar.error(
                title='错误',
                content=f"取消商品折扣失败: {e}",
                parent=self
            )

    def handle_sync_button_click(self):
        """处理同步按钮点击事件"""
        try:
            # 获取所有门店数据
            stores = []
            for i in range(self.store_combo.count()):
                store_data = self.store_combo.itemData(i)
                if store_data and store_data.get('id'):  # 跳过"请选择门店"选项
                    stores.append(store_data)
            
            if not stores:
                InfoBar.error(
                    title='错误',
                    content="没有可用的门店",
                    parent=self
                )
                return
                
            # 显示门店选择对话框
            dialog = StoreSelectorDialog(stores=stores, last_selected=self.last_selected_stores, parent=self)
            if dialog.exec_() != QDialog.Accepted:
                return
                
            # 获取选中的门店
            selected_stores = dialog.get_selected_stores()
            if not selected_stores:
                InfoBar.error(
                    title='错误',
                    content="请选择至少一个门店",
                    parent=self
                )
                return
            
            # 更新上次选择的门店
            self.last_selected_stores = {store['id'] for store in selected_stores}
                
            # 禁用按钮，防止重复点击
            self.sync_button.setEnabled(False)
            
            # 创建并显示进度对话框
            self.progress_dialog = SyncProgressDialog(selected_stores, self)
            self.progress_dialog.show()
            
            # 创建并启动同步线程
            self.sync_threads = []
            total_stores = len(selected_stores)
            
            for i, store_data in enumerate(selected_stores):
                # 创建同步线程
                sync_thread = DataSyncThread(
                    store_id=store_data['id'],
                    cookies=store_data['cookies'],
                    platform=store_data['platform']
                )
            
                # 连接信号
                store_name = store_data['name']
                platform = store_data['platform']
                
                # 使用functools.partial来正确捕获每个店铺的platform值
                sync_thread.progress.connect(
                    lambda msg, s=store_name, p=platform: self.handle_sync_progress(s, p, msg)
                )
                sync_thread.finished.connect(self.handle_sync_completed)
                sync_thread.error.connect(
                    lambda err, s=store_name, p=platform: self.handle_sync_error(f"[{s}] {err}", s, p)
                )
                
                # 保存线程引用
                self.sync_threads.append(sync_thread)
                
                # 启动线程
                sync_thread.start()
            
            # 更新界面显示
            self.sync_button.setText('同步中...')
            
            InfoBar.success(
                title='提示',
                content=f"开始同步 {total_stores} 个门店的数据...",
                parent=self
            )
            
        except Exception as e:
            default_logger.error(f"启动同步失败: {e}")
            InfoBar.error(
                title='错误',
                content=f"启动同步失败: {e}",
                parent=self
            )
            self.sync_button.setEnabled(True)

    def handle_sync_progress(self, store_name: str, platform: str, message: str):
        """处理同步进度"""
        try:
            print('platform1',platform)
            # 更新进度对话框中对应店铺的进度
            if hasattr(self, 'progress_dialog'):
                self.progress_dialog.update_store_progress(store_name, platform, message)
                
                print('platform2',platform)
               
            default_logger.info(f"[{store_name}]-{platform} {message}")
        except Exception as e:
            default_logger.error(f"更新进度显示失败: {e}")

    def handle_sync_completed(self):
        """处理同步完成事件"""
        try:
            # 检查是否所有线程都完成了
            all_completed = all(not thread.isRunning() for thread in self.sync_threads)
            if all_completed:
                # 恢复按钮状态
                self.sync_button.setEnabled(True)
                self.sync_button.setText('同步商品数据')
                
                # 清理同步线程列表
                self.sync_threads.clear()
                
                # 关闭进度对话框
                if hasattr(self, 'progress_dialog'):
                    self.progress_dialog.close()
                    delattr(self, 'progress_dialog')
                
                InfoBar.success(
                    title='成功',
                    content="所有门店数据同步完成",
                    parent=self
                )
                
                # 刷新表格显示
                asyncio.create_task(self.refresh_table())
        except Exception as e:
            default_logger.error(f"处理同步完成事件失败: {e}")
            InfoBar.error(
                title='错误',
                content=f"处理同步完成事件失败: {e}",
                parent=self
            )
            # 确保在出错时也恢复按钮状态
            self.sync_button.setEnabled(True)
            self.sync_button.setText('同步商品数据')

    def handle_sync_error(self, error_message, store_name=None, platform=None):
        """处理同步错误事件"""
        try:
            default_logger.error(f"同步出错: {error_message}")

            # 检查是否是认证过期错误
            is_auth_expired = ('cookies已失效' in error_message or 'Token已过期' in error_message or
                             '认证已过期' in error_message)

            # 检查是否所有线程都完成了
            all_completed = all(not thread.isRunning() for thread in self.sync_threads)
            if all_completed:
                # 恢复按钮状态
                self.sync_button.setEnabled(True)
                self.sync_button.setText('同步商品数据')

                # 清理同步线程列表
                self.sync_threads.clear()

                # 关闭进度对话框
                if hasattr(self, 'progress_dialog'):
                    self.progress_dialog.close()
                    delattr(self, 'progress_dialog')

                # 根据错误类型显示不同的提示
                if is_auth_expired:
                    InfoBar.warning(
                        title='认证过期',
                        content=f"门店认证已过期，已跳过: {error_message}",
                        parent=self,
                        duration=4000,
                        position=InfoBarPosition.TOP
                    )
                else:
                    InfoBar.error(
                        title='错误',
                        content=f"同步失败: {error_message}",
                        parent=self,
                        duration=3000,
                        position=InfoBarPosition.TOP
                    )
        except Exception as e:
            default_logger.error(f"处理同步错误事件失败: {e}")
            InfoBar.error(
                title='错误',
                content=f"处理同步错误事件失败: {e}",
                parent=self
            )
            # 确保在出错时也恢复按钮状态
            self.sync_button.setEnabled(True)
            self.sync_button.setText('同步商品数据')

    async def download_image(self, url: str, cache_path: str) -> bool:
        """异步下载并缓存图片"""
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.289 Safari/537.36'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers if 'img.alicdn.com' in url else None, ssl=False) as response:
                    if response.status == 200:
                        data = await response.read()
                        async with aiofiles.open(cache_path, 'wb') as f:
                            await f.write(data)
                        return True
            return False
        except Exception as e:
            default_logger.error(f"下载图片失败: {e}, URL: {url}")
            return False

    async def download_and_update_image(self, row: int, url: str, cache_path: str, pictures: str):
        """下载并更新图片单元格"""
        try:
            if await self.download_image(url, cache_path):
                # 直接在主线程中更新UI，使用 Qt 的信号机制
                pixmap = QPixmap(cache_path)
                scaled_pixmap = pixmap.scaled(100, 100, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                
                # 创建一个新的QPixmap作为最终图像
                final_pixmap = QPixmap(scaled_pixmap.size())
                final_pixmap.fill(Qt.transparent)  # 设置透明背景
                
                # 创建画家对象
                painter = QPainter(final_pixmap)
                painter.setRenderHint(QPainter.Antialiasing)  # 抗锯齿
                
                # 绘制原始图片
                painter.drawPixmap(0, 0, scaled_pixmap)
                
                # 获取商品状态
                product_status = None
                platform = self.store_combo.currentData().get('platform') if self.store_combo.currentData() else None
                
                with get_session_context() as session:
                    product_id = self.table.item(row, 0).text()
                    product = session.query(Product).filter_by(
                        store_id=self.current_store_id,
                        product_id=product_id
                    ).first()
                    if product:
                        product_status = product.status
                
                # 如果商品已下架，添加遮罩
                if product_status is not None:
                    if platform == "饿了么" and product_status == -3:
                        # 饿了么违规下架
                        # 添加半透明黑色遮罩
                        painter.fillRect(final_pixmap.rect(), QColor(0, 0, 0, 128))
                        
                        # 添加文字
                        painter.setPen(Qt.white)
                        font = QFont()
                        font.setPointSize(10)
                        painter.setFont(font)
                        
                        # 在底部绘制文字
                        text_rect = QRectF(0, final_pixmap.height() - 25, 
                                         final_pixmap.width(), 25)
                        painter.fillRect(text_rect.toRect(), QColor(0, 0, 0, 192))
                        painter.drawText(text_rect, Qt.AlignCenter, "违规下架")
                        
                    elif product_status in [1, -2]:  # 美团下架状态为1，饿了么普通下架状态为-2
                        # 普通下架
                        # 添加半透明黑色遮罩
                        painter.fillRect(final_pixmap.rect(), QColor(0, 0, 0, 128))
                        
                        # 添加文字
                        painter.setPen(Qt.white)
                        font = QFont()
                        font.setPointSize(10)
                        painter.setFont(font)
                        
                        # 在底部绘制文字
                        text_rect = QRectF(0, final_pixmap.height() - 25, 
                                         final_pixmap.width(), 25)
                        painter.fillRect(text_rect.toRect(), QColor(0, 0, 0, 192))
                        painter.drawText(text_rect, Qt.AlignCenter, "已下架")
                
                painter.end()
                
                # 创建标签并设置图片
                label = ImageLabel()
                label.setPixmap(final_pixmap)
                if pictures:
                    pictures_list = json.loads(pictures)
                    label.setToolTip(f"共 {len(pictures_list)} 张图片")
                    
                self.table.setCellWidget(row, 2, label)
                
        except Exception as e:
            default_logger.error(f"更新图片单元格失败: {e}")
            self.table.setItem(row, 2, QTableWidgetItem("图片加载失败"))

    def update_image_cell(self, row: int, image_url: str, pictures: str):
        """用ImageLabel异步显示图片，支持点击大图和下架遮罩，仅本表格放大，正常状态也填满"""
        try:
            label = ImageLabel(event_loop=self.loop)
            label.setFixedSize(120, 120)  # 只在本表格放大
            async def set_image_with_mask():
                await label.set_image(image_url)
                # 获取商品状态
                product_status = None
                platform = self.store_combo.currentData().get('platform') if self.store_combo.currentData() else None
                with get_session_context() as session:
                    product_id = self.table.item(row, 0).text()
                    product = session.query(Product).filter_by(
                        store_id=self.current_store_id,
                        product_id=product_id
                    ).first()
                    if product:
                        product_status = product.status
                # 判断是否需要遮罩
                need_mask = False
                mask_text = ""
                if platform == "饿了么" and product_status == -3:
                    need_mask = True
                    mask_text = "违规下架"
                elif (platform == "美团" and product_status == 1) or (platform == "饿了么" and product_status == -2):
                    need_mask = True
                    mask_text = "已下架"
                if label.original_pixmap and not label.original_pixmap.isNull():
                    pixmap = label.original_pixmap
                    scaled_pixmap = pixmap.scaled(120, 120, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                    if need_mask:
                        final_pixmap = QPixmap(scaled_pixmap.size())
                        final_pixmap.fill(Qt.transparent)
                        painter = QPainter(final_pixmap)
                        painter.setRenderHint(QPainter.Antialiasing)
                        painter.drawPixmap(0, 0, scaled_pixmap)
                        painter.fillRect(final_pixmap.rect(), QColor(0, 0, 0, 128))
                        painter.setPen(Qt.white)
                        font = QFont()
                        font.setPointSize(10)
                        painter.setFont(font)
                        text_rect = QRectF(0, final_pixmap.height() - 20, final_pixmap.width(), 20)
                        painter.fillRect(text_rect.toRect(), QColor(0, 0, 0, 192))
                        painter.drawText(text_rect, Qt.AlignCenter, mask_text)
                        painter.end()
                        label.setPixmap(final_pixmap)
                        label.original_pixmap = pixmap  # 保证点击大图仍为原图
                    else:
                        label.setPixmap(scaled_pixmap)
                        label.original_pixmap = pixmap
            asyncio.create_task(set_image_with_mask())
            if pictures:
                try:
                    pictures_list = json.loads(pictures)
                    label.setToolTip(f"共 {len(pictures_list)} 张图片")
                except Exception:
                    pass
            self.table.setCellWidget(row, 2, label)
        except Exception as e:
            default_logger.error(f"ImageLabel加载图片失败: {e}")
            self.table.setItem(row, 2, QTableWidgetItem("图片加载失败"))

    def closeEvent(self, event):
        """窗口关闭事件处理"""
        # 取消所有未完成的下载任务
        for task in self.download_tasks:
            if not task.done():
                task.cancel()
        self.download_tasks.clear()
        super().closeEvent(event)

    def on_page_changed(self):
        """页码改变时的处理函数"""
        # 获取当前选中的分类项
        current_item = self.current_item
        if current_item:
            # 使用QTimer延迟执行异步操作
            QTimer.singleShot(0, lambda: asyncio.create_task(self.on_category_selected(current_item, 0)))

    def on_page_size_changed(self):
        """每页显示数量改变时的处理函数"""
        # 获取当前选中的分类项
        try:
            current_item = self.current_item
            if current_item:
                # 使用QTimer延迟执行异步操作
                QTimer.singleShot(0, lambda: asyncio.create_task(self.on_category_selected(current_item, 0)))
        except Exception as e:
            default_logger.error(f"更新页面大小失败: {e}")

    async def refresh_single_product(self, product_id: str, sku_id: str = None):
        """刷新单个商品的信息"""
        try:
            platform = self.store_combo.currentData().get('platform')
            
            # 根据平台调用不同的API获取最新数据
            if platform == '饿了么':
                # 调用饿了么搜索接口
                result = await self.api.search_items(product_id)
                if result and 'data' in result:
                    product_data = result['data']
                    # 获取折扣信息
                    discount_data = await self.api.discount.get_discount_detail(product_id)
            else:
                # 在products表中查询商品名称
                with get_session_context() as session:
                    product = session.query(Product).filter_by(
                        store_id=self.current_store_id,
                        product_id=product_id
                    ).first()
                    product_name = product.name
                    sku_id = product.sku_id
                    
                # 调用美团搜索接口
                result = await self.api.get_product_list(
                    page_num=1,
                    page_size=10,
                    search_word=product_name
                )
                if result and 'data' in result:
                    product_data = result['data']
                    # 获取折扣信息
                    discount_data = await self.api.get_discount_list(
                        page_no=1,
                        page_size=10,
                        search_params={
                            'skuIdList': [sku_id]
                        }
                    )

                # 更新数据库
                with get_session_context() as session:
                    # 更新商品信息
                    if product_data:
                        product = session.query(Product).filter_by(
                            store_id=self.current_store_id,
                            product_id=product_id
                        ).first()

                        if product:
                            # 更新商品信息
                            for productList in product_data['productList']:
                                product.name = productList['name']
                                product.picture = productList['picture']
                                product.pictures = json.dumps(productList['pictures'])
                                product.status = productList['sellStatus']
                                for i in productList['wmProductSkus']:
                                    if i['id'] == sku_id:
                                        product.price = i['price']
                                        product.total_stock = i['stock']

                    print('sku_id',sku_id)
                    print('product_id',product_id)
                    discount = session.query(Discount).filter_by(
                        store_id=self.current_store_id,
                        product_id=product_id,
                        sku_id=sku_id if sku_id else product_id
                    ).first()
                    print('折扣',discount)
                    # 更新折扣信息
                    if discount_data['data']['respPage']['pageContent']:
                        if discount:
                            # 更新折扣信息
                            discount_info = discount_data['data']['respPage']['pageContent'][0]
                            discount.discount_price = discount_info['actPrice']
                            discount.order_limit = discount_info['orderLimit']
                            discount.sort_index = discount_info['sortNumber']
                            discount.verify_status = discount_info.get('actStatus')
                            discount.verify_msg = discount_info.get('verifyMsg', '')
                    else:
                        if discount:
                            # 删除折扣信息
                            session.delete(discount)

                    session.commit()

            # 直接更新表格中的对应行
            for row in range(self.table.rowCount()):
                id_item = self.table.item(row, 0)
                if id_item and id_item.text() == product_id:
                    # 从数据库获取最新数据并更新表格行
                    with get_session_context() as session:
                        product = session.query(Product).filter_by(
                            store_id=self.current_store_id,
                            product_id=product_id
                        ).first()
                        discount = session.query(Discount).filter_by(
                            store_id=self.current_store_id,
                            product_id=product_id,
                            sku_id=sku_id if sku_id else product_id
                        ).first()
                        
                        # 更新表格行的显示
                        self.update_table_row(row, product, discount)
                    break

            InfoBar.success(
                title='成功',
                content="商品信息已更新",
                parent=self
            )

        except Exception as e:
            default_logger.error(f"刷新商品信息失败: {e}")
            InfoBar.error(
                title='错误',
                content=f"刷新商品信息失败: {e}",
                parent=self
            )

    def update_table_row(self, row: int, product: Product, discount: Discount = None):
        """更新表格中的单行数据"""
        # 更新商品基本信息
        self.table.item(row, 1).setText(product.name)
        self.table.item(row, 2).setText(str(product.price))
        self.table.item(row, 3).setText(str(product.total_stock))
        
        

    async def _sync_store_data(self):
        """同步门店数据"""
        try:
            from src.api.store_manager import StoreManager
            from src.services.product_sync import ProductSyncService

            # 显示开始同步提示
            InfoBar.info(
                title='提示',
                content="开始同步门店数据...",
                parent=self
            )

            # 获取同步前的门店列表（用于后续验证）
            old_stores = []
            for i in range(self.store_combo.count()):
                store_data = self.store_combo.itemData(i)
                if store_data and store_data.get('id'):
                    old_stores.append(store_data)

            # 使用StoreManager刷新缓存
            store_manager = StoreManager()
            store_manager.refresh_cache()

            # 重新加载门店列表
            self.load_stores()

            # 获取更新后的门店列表
            new_stores = []
            for i in range(self.store_combo.count()):
                store_data = self.store_combo.itemData(i)
                if store_data and store_data.get('id'):
                    new_stores.append(store_data)

            # 清除所有门店的API实例缓存，强制使用新的cookie
            InfoBar.info(
                title='提示',
                content="正在更新API实例...",
                parent=self
            )

            for store in new_stores:
                ProductSyncService.refresh_store_api_instance(store)

            # 验证新cookie是否有效
            InfoBar.info(
                title='提示',
                content="正在验证新的认证信息...",
                parent=self
            )

            validation_results = []
            for store in new_stores:
                try:
                    result = await ProductSyncService.validate_store_cookies(store)
                    validation_results.append(result)
                except Exception as e:
                    validation_results.append({
                        'success': False,
                        'message': f"验证{store.get('name', '未知门店')}时发生异常: {str(e)}",
                        'store_name': store.get('name', '未知门店'),
                        'platform': store.get('platform', '未知平台')
                    })

            # 统计验证结果
            success_stores = [r for r in validation_results if r['success']]
            failed_stores = [r for r in validation_results if not r['success']]

            # 显示验证结果
            if success_stores and not failed_stores:
                # 全部验证成功
                InfoBar.success(
                    title='同步完成',
                    content=f"门店数据同步完成，所有 {len(success_stores)} 个门店的认证信息均有效",
                    parent=self,
                    duration=5000
                )
            elif success_stores and failed_stores:
                # 部分验证成功
                success_names = [r['store_name'] for r in success_stores]
                failed_names = [r['store_name'] for r in failed_stores]

                InfoBar.warning(
                    title='同步完成（部分门店认证失效）',
                    content=f"同步完成。有效门店({len(success_stores)}个): {', '.join(success_names)}；失效门店({len(failed_stores)}个): {', '.join(failed_names)}",
                    parent=self,
                    duration=8000
                )

                # 显示失效门店的详细信息
                for result in failed_stores:
                    InfoBar.error(
                        title=f'{result["platform"]}认证失效',
                        content=result['message'],
                        parent=self,
                        duration=6000
                    )
            else:
                # 全部验证失败
                failed_names = [r['store_name'] for r in failed_stores]
                InfoBar.error(
                    title='同步完成（所有门店认证失效）',
                    content=f"门店数据已同步，但所有门店的认证信息均已失效: {', '.join(failed_names)}",
                    parent=self,
                    duration=8000
                )

        except Exception as e:
            default_logger.error(f"同步门店数据失败: {e}")
            InfoBar.error(
                title='错误',
                content=f"同步门店数据失败: {e}",
                parent=self
            )

    def _handle_sync_store_click(self):
        """处理同步门店按钮点击事件"""
        # 使用QTimer延迟执行异步操作
        QTimer.singleShot(0, lambda: asyncio.create_task(self._sync_store_data()))

    async def _handle_status_filter(self):
        """
        处理状态筛选按钮点击事件
        """
        await self.on_category_selected(self.current_item, 0)

    def handle_selection_changed(self):
        """处理表格选择变化"""
        selected_items = self.table.selectedItems()
        selected_row_numbers = sorted(set(item.row() for item in selected_items))
        selected_rows = len(selected_row_numbers)
        
        # 添加调试日志
        default_logger.info(f"选中项数量: {len(selected_items)}")
        default_logger.info(f"表格列数: {self.table.columnCount()}")
        default_logger.info(f"实际选中的行数: {selected_rows}")
        default_logger.info(f"选中的行号: {selected_row_numbers}")
        
        # 只要有选中的行就显示批量操作按钮
        show_batch_buttons = selected_rows > 0
        default_logger.info(f"是否显示批量按钮: {show_batch_buttons}")
        
        self.batch_online_button.setVisible(show_batch_buttons)
        self.batch_offline_button.setVisible(show_batch_buttons)
        self.batch_category_button.setVisible(show_batch_buttons)
        self.cancel_select_button.setVisible(show_batch_buttons)
        
        if show_batch_buttons:
            self.batch_online_button.setText(f'批量上架({selected_rows})')
            self.batch_offline_button.setText(f'批量下架({selected_rows})')
            self.batch_category_button.setText(f'批量修改分类({selected_rows})')

    def cancel_selection(self):
        """取消所有选择"""
        self.table.clearSelection()

    def _handle_batch_status_change(self, new_status: int):
        """处理批量状态更改的中间处理函数"""
        # 使用 QTimer 延迟执行异步操作
        QTimer.singleShot(0, lambda: asyncio.create_task(self.handle_batch_status_change(new_status)))

    async def handle_batch_status_change(self, new_status: int):
        """处理批量状态更改"""
        try:
            # 获取选中的商品ID列表
            selected_rows = set()
            selected_items = self.table.selectedItems()
            for item in selected_items:
                selected_rows.add(item.row())
        
            # 获取当前表格中所有商品的ID（不仅是选中的）
            current_product_ids = []
            for row in range(self.table.rowCount()):
                product_id = self.table.item(row, 0).text()
                current_product_ids.append(product_id)
            
            # 获取当前表格中选中的商品信息
            selected_product_info = []
            for row in selected_rows:
                product_id = self.table.item(row, 0).text()
                product_name = self.table.item(row, 1).text()
                selected_product_info.append({
                    'product_id': product_id,
                    'name': product_name
                })
            
            if not selected_product_info:
                return
        
            # 确认对话框
            status_text = "上架" if new_status == 1 else "下架"
            reply = MessageBox(
                "确认操作",
                f"确定要将选中的 {len(selected_product_info)} 个商品在所有门店中{status_text}吗？",
                self
            )
            if not reply.exec():
                return
        
            # 显示加载遮罩
            self.loading_mask.show()
            self.loading_mask.set_message("正在处理批量操作...")
        
            # 获取所有门店信息
            stores = []
            for i in range(self.store_combo.count()):
                store_data = self.store_combo.itemData(i)
                if store_data:
                    stores.append(store_data)
            
            async def process_store(store_data):
                """处理单个门店的异步函数"""
                try:
                    store_id = store_data['id']
                    print(f'开始{status_text}{store_data["name"]}{store_data["platform"]}的商品')
                    
                    # 使用单个会话处理所有查询
                    with get_session_context() as session:
                        # 获取该门店对应的商品ID
                        store_products = []
                        for product_info in selected_product_info:
                            product = session.query(Product).filter_by(
                                store_id=store_id,
                                name=product_info['name']
                            ).first()
                            
                            if product:
                                store_products.append({
                                    'product_id': product.product_id,
                                    'name': product.name
                                })
                        
                        if not store_products:
                            print(f"{store_data['name']}没有找到对应的商品，跳过")
                            return {
                                'store_name': store_data['name'],
                                'success': True,
                                'skipped': True,
                                'error': None
                            }
                        
                        store_product_ids = [p['product_id'] for p in store_products]
                        
                    if store_data['platform'] == '美团':
                        current_api = MeituanAPI(
                            cookies=store_data['cookies'],
                            sign_generator=self.sign_generator,
                            store_name=store_data['name']
                        )
                        
                        # 获取SKU IDs
                        sku_ids = []
                        for product_id in store_product_ids:
                            skus = session.query(ProductSku).filter_by(
                                store_id=store_id,
                                product_id=product_id
                            ).all()
                            sku_ids.extend([sku.sku_id for sku in skus])
                        
                        if new_status == 1:
                            sell_status = 0
                        else:
                            sell_status = 1
                            
                        result = await current_api.update_sell_status(store_product_ids, sku_ids, sell_status)
                        
                        if result.get('msg') == 'success':
                            print(f'美团门店{store_data["name"]}批量操作修改成功')
                            for product_id in store_product_ids:
                                session.query(Product).filter_by(
                                    store_id=store_id,
                                    product_id=product_id
                                ).update({"status": sell_status})
                            session.commit()
                            return {
                                'store_name': store_data['name'],
                                'success': True,
                                'error': None
                            }
                        else:
                            return {
                                'store_name': store_data['name'],
                                'success': False,
                                'error': '美团API返回失败'
                            }
                            
                    elif store_data['platform'] == '饿了么':
                        current_api = ElemeAPI(
                            cookies=store_data['cookies'],
                            shop_name=store_data['name']
                        )
                        await current_api.initialize()
                    
                        try:
                            if new_status == 1:
                                status = 0
                                he_status = 0
                            else:
                                status = -2
                                he_status = 1
                            
                            result = await current_api.product.batch_update_products(store_product_ids, status)
                            
                            if result:
                                success_flag = True
                                for product_id, success in result.items():
                                    if success:
                                        session.query(Product).filter_by(
                                            store_id=store_id,
                                            product_id=product_id
                                        ).update({"status": he_status})
                                    else:
                                        success_flag = False
                                session.commit()
                                
                                return {
                                    'store_name': store_data['name'],
                                    'success': success_flag,
                                    'error': None if success_flag else '部分商品操作失败'
                                }
                            
                            return {
                                'store_name': store_data['name'],
                                'success': False,
                                'error': '饿了么API返回为空'
                            }
                            
                        finally:
                            await current_api.close()
                    
                except AuthenticationExpiredError as e:
                    # 认证过期，标记为跳过
                    default_logger.warning(f"门店 {store_data['name']} 认证过期: {e.message}")
                    return {
                        'store_name': store_data['name'],
                        'success': True,
                        'skipped': True,
                        'error': e.message
                    }
                except Exception as e:
                    default_logger.error(f"处理门店 {store_data['name']} 失败: {e}")
                    return {
                        'store_name': store_data['name'],
                        'success': False,
                        'error': str(e)
                    }

            # 并行执行所有门店的处理
            tasks = [process_store(store_data) for store_data in stores if store_data]
            results = await asyncio.gather(*tasks)

            # 统计结果
            total_success = sum(1 for r in results if r['success'] and not r.get('skipped', False))
            total_failed = sum(1 for r in results if not r['success'])
            total_skipped = sum(1 for r in results if r.get('skipped', False))
            failed_stores = [f"{r['store_name']}({r['error']})" for r in results if not r['success']]
            skipped_stores = [f"{r['store_name']}({r['error']})" for r in results if r.get('skipped', False)]

            # 刷新当前表格显示
            await self.refresh_table(current_product_ids)

            # 显示总结果
            message = (
                f"批量操作完成\n"
                f"已处理 {len(stores)} 个门店\n"
                f"成功: {total_success}\n"
                f"失败: {total_failed}\n"
                f"跳过: {total_skipped}"
            )

            if failed_stores:
                message += f"\n失败的门店: {', '.join(failed_stores)}"

            if skipped_stores:
                message += f"\n跳过的门店(认证过期): {', '.join(skipped_stores)}"
            
            InfoBar.success(
                title="完成",
                content=message,
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=3000,
                parent=self
            )
            
        except Exception as e:
            default_logger.error(f"批量操作失败: {e}")
            InfoBar.error(
                title="错误",
                content=f"批量操作失败: {e}",
                parent=self
            )
        finally:
            self.loading_mask.hide()

    async def handle_batch_category_change(self):
        """处理批量修改分类"""
        try:
            # 获取选中的行
            selected_rows = sorted(set(item.row() for item in self.table.selectedItems()))
            if not selected_rows:
                return
                
            # 收集商品名称和ID
            selected_product_info = []
            for row in selected_rows:
                product_id = self.table.item(row, 0).text()
                product_name = self.table.item(row, 1).text()
                selected_product_info.append({
                    'product_id': product_id,
                    'name': product_name
                })
            
            # 从数据库获取当前门店的分类列表
            with get_session_context() as session:
                categories = session.query(Category).filter_by(
                    store_id=self.current_store_id
                ).order_by(Category.sequence).all()
                
                # 转换为对话框需要的格式
                category_list = [
                    {"text": category.name, "data": category.category_id}
                    for category in categories
                ]
            
            # 创建并显示对话框
            dialog = BatchCategoryEditDialog(
                selected_count=len(selected_product_info),
                categories=category_list,
                parent=self
            )
            
            # 如果用户确认修改
            if dialog.exec() == QDialog.Accepted:
                result = dialog.get_result()
                mode = result["mode"]
                selected_category_ids = result["category_ids"]
                
                # 获取所有门店信息
                stores = []
                for i in range(self.store_combo.count()):
                    store_data = self.store_combo.itemData(i)
                    if store_data:
                        stores.append(store_data)
                
                # 显示确认对话框
                reply = MessageBox(
                    "确认操作",
                    f"确定要将选中的 {len(selected_product_info)} 个商品在所有门店中修改分类吗？",
                    self
                )
                if not reply.exec():
                    return
                
                # 显示加载遮罩
                self.loading_mask.show()
                self.loading_mask.set_message("正在处理批量操作...")
                
                async def process_store(store_data):
                    """处理单个门店的异步函数"""
                    try:
                        store_id = store_data['id']
                        print(f'开始修改{store_data["name"]}{store_data["platform"]}的商品分类')
                        
                        # 使用单个会话处理所有查询
                        with get_session_context() as session:
                            # 获取该门店对应的商品ID
                            store_products = []
                            for product_info in selected_product_info:
                                product = session.query(Product).filter_by(
                                    store_id=store_id,
                                    name=product_info['name']
                                ).first()
                                
                                if product:
                                    store_products.append({
                                        'product_id': product.product_id,
                                        'name': product.name
                                    })
                            
                            if not store_products:
                                print(f"{store_data['name']}没有找到对应的商品，跳过")
                                return {
                                    'store_name': store_data['name'],
                                    'success': True,
                                    'skipped': True,
                                    'error': None
                                }
                            
                            store_product_ids = [p['product_id'] for p in store_products]
                            
                            # 获取该门店对应的分类ID
                            store_categories = session.query(Category).filter_by(
                                store_id=store_id
                            ).all()
                            
                            # 创建分类名称到ID的映射
                            category_name_to_id = {
                                cat.name: cat.category_id
                                for cat in store_categories
                            }
                            
                            # 获取当前门店对应的分类ID
                            store_category_ids = []
                            with get_session_context() as session:
                                for cat_id in selected_category_ids:
                                    # 找到原始分类的名称
                                    orig_category = session.query(Category).filter_by(
                                        store_id=self.current_store_id,
                                        category_id=cat_id
                                    ).first()
                                    
                                    if orig_category and orig_category.name in category_name_to_id:
                                        # 使用目标门店中相同名称分类的ID
                                        store_category_ids.append(category_name_to_id[orig_category.name])
                        
                        if store_data['platform'] == '美团':
                            current_api = MeituanAPI(
                                cookies=store_data['cookies'],
                                sign_generator=self.sign_generator,
                                store_name=store_data['name']
                            )
                            
                            # 获取SKU IDs
                            with get_session_context() as session:
                                sku_ids = []
                                for product_id in store_product_ids:
                                    skus = session.query(ProductSku).filter_by(
                                        store_id=store_id,
                                        product_id=product_id
                                    ).all()
                                    sku_ids.extend([sku.sku_id for sku in skus])
                            
                            # 根据不同模式处理
                            if mode == "add":
                                result = await current_api.batch_update_product_category(sku_ids, store_category_ids, op_type=1)
                            elif mode == "replace":
                                result = await current_api.batch_update_product_category(sku_ids, store_category_ids, op_type=2)
                            elif mode == "delete":
                                result = await current_api.batch_update_product_category(sku_ids, store_category_ids, op_type=3)
                            
                            if result.get('code') == 0:
                                # 更新数据库
                                with get_session_context() as session:
                                    for product_id in store_product_ids:
                                        product = session.query(Product).filter_by(
                                            store_id=store_id,
                                            product_id=product_id
                                        ).first()
                                        
                                        if product:
                                            if mode == "add" or mode == "replace":
                                                if store_category_ids:
                                                    product.category_id = store_category_ids[0]
                                            elif mode == "delete":
                                                if product.category_id in store_category_ids:
                                                    product.category_id = None
                                    session.commit()
                                
                                return {
                                    'store_name': store_data['name'],
                                    'success': True,
                                    'error': None
                                }
                            else:
                                return {
                                    'store_name': store_data['name'],
                                    'success': False,
                                    'error': result.get('msg', '美团API返回失败')
                                }
                                
                        elif store_data['platform'] == '饿了么':
                            current_api = ElemeAPI(
                                cookies=store_data['cookies'],
                                shop_name=store_data['name']
                            )
                            await current_api.initialize()
                            
                            try:
                                if mode == "add":
                                    # 获取当前分类
                                    current_categories = []
                                    with get_session_context() as session:
                                        for product_id in store_product_ids:
                                            product = session.query(Product).filter_by(
                                                store_id=store_id,
                                                product_id=product_id
                                            ).first()
                                            if product and product.category_id:
                                                current_categories=product.category_id.split(',')
                                    
                                    # 合并分类ID（去重）
                                    all_category_ids = list(set(current_categories + store_category_ids))
                                    result = await current_api.product.batch_update_category(store_product_ids, all_category_ids)
                                    
                                elif mode == "replace":
                                    result = await current_api.product.batch_update_category(store_product_ids, store_category_ids)
                                    
                                elif mode == "delete":
                                    # 获取当前分类
                                    current_categories = []
                                    with get_session_context() as session:
                                        for product_id in store_product_ids:
                                            product = session.query(Product).filter_by(
                                                store_id=store_id,
                                                product_id=product_id
                                            ).first()
                                            if product and product.category_id:
                                                current_categories=product.category_id.split(',')
                                    
                                    # 移除指定分类
                                    remaining_categories = [c for c in current_categories if c not in store_category_ids]
                                    result = await current_api.product.batch_update_category(store_product_ids, remaining_categories)
                                
                                if result:
                                    # 更新数据库
                                    with get_session_context() as session:
                                        for product_id in store_product_ids:
                                            product = session.query(Product).filter_by(
                                                store_id=store_id,
                                                product_id=product_id
                                            ).first()
                                            
                                            if product:
                                                if mode == "add":
                                                    if all_category_ids:
                                                        product.category_id = ','.join(all_category_ids)
                                                elif mode == "replace":
                                                    if store_category_ids:
                                                        product.category_id = ','.join(store_category_ids)
                                                elif mode == "delete":
                                                    if product.category_id in store_category_ids:
                                                        product.category_id = ','.join(remaining_categories)
                                        session.commit()
                                    
                                    return {
                                        'store_name': store_data['name'],
                                        'success': True,
                                        'error': None
                                    }
                                else:
                                    return {
                                        'store_name': store_data['name'],
                                        'success': False,
                                        'error': '饿了么API返回为空'
                                    }
                                    
                            finally:
                                await current_api.close()
                        
                    except AuthenticationExpiredError as e:
                        # 认证过期，标记为跳过
                        default_logger.warning(f"门店 {store_data['name']} 认证过期: {e.message}")
                        return {
                            'store_name': store_data['name'],
                            'success': True,
                            'skipped': True,
                            'error': e.message
                        }
                    except Exception as e:
                        default_logger.error(f"处理门店 {store_data['name']} 失败: {e}")
                        return {
                            'store_name': store_data['name'],
                            'success': False,
                            'error': str(e)
                        }
                
                # 并行执行所有门店的处理
                tasks = [process_store(store_data) for store_data in stores if store_data]
                results = await asyncio.gather(*tasks)
                
                # 统计结果
                total_success = sum(1 for r in results if r['success'] and not r.get('skipped', False))
                total_failed = sum(1 for r in results if not r['success'])
                total_skipped = sum(1 for r in results if r.get('skipped', False))
                failed_stores = [f"{r['store_name']}({r['error']})" for r in results if not r['success']]
                skipped_stores = [f"{r['store_name']}({r['error']})" for r in results if r.get('skipped', False)]

                # 刷新当前表格显示
                selected_items = self.category_tree.tree.selectedItems()

                if selected_items and len(selected_items) > 0:
                    await self.on_category_selected(selected_items[0], 0)

                # 刷新分类树
                self.load_categories()

                # 显示总结果
                message = (
                    f"批量修改分类完成\n"
                    f"已处理 {len(stores)} 个门店\n"
                    f"成功: {total_success}\n"
                    f"失败: {total_failed}\n"
                    f"跳过: {total_skipped}"
                )

                if failed_stores:
                    message += f"\n失败的门店: {', '.join(failed_stores)}"

                if skipped_stores:
                    message += f"\n跳过的门店(认证过期): {', '.join(skipped_stores)}"
                
                InfoBar.success(
                    title="完成",
                    content=message,
                    orient=Qt.Horizontal,
                    isClosable=True,
                    position=InfoBarPosition.TOP,
                    duration=3000,
                    parent=self
                )
                
        except Exception as e:
            default_logger.error(f"批量修改分类失败: {e}")
            InfoBar.error(
                title="错误",
                content=f"批量修改分类失败: {e}",
                parent=self
            )
        finally:
            self.loading_mask.hide()

   
    async def save_category_sequence(self):
        """保存所有门店的分类排序"""
        try:
            # 获取当前界面上的分类排序
            current_categories = []
            root = self.category_tree.tree.topLevelItem(0)
            for i in range(root.childCount()):
                item = root.child(i)
                category_name = item.text(0)
                if category_name:
                    name_part = category_name.split(' (')[0]
                    if '.' in name_part:
                        current_categories.append(name_part.split('.', 1)[1].strip())
                    else:
                        current_categories.append(name_part.strip())
            
            if not current_categories:
                InfoBar.warning(
                    title="警告",
                    content="没有可排序的分类",
                    parent=self,
                    duration=2000,
                )
                return

            # 获取所有门店列表
            stores = []
            for i in range(self.store_combo.count()):
                store_data = self.store_combo.itemData(i)
                if store_data:
                    stores.append(store_data)

            # 弹出门店选择对话框
            dialog = StoreSelectorDialog(stores, parent=self)
            if dialog.exec_() != QDialog.Accepted:
                return
            
            # 获取用户选择的门店
            selected_stores = dialog.get_selected_stores()
            if not selected_stores:
                InfoBar.warning(
                    title="警告",
                    content="请选择至少一个门店",
                    parent=self,
                    duration=2000,
                )
                return

            # 创建并显示进度对话框
            progress_dialog = SyncProgressDialog(selected_stores, self)
            progress_dialog.show()

            # 按平台分类选中的门店
            meituan_stores = []
            eleme_stores = []
            for store in selected_stores:
                if store['platform'] == "美团":
                    meituan_stores.append(store)
                elif store['platform'] == "饿了么":
                    eleme_stores.append(store)

            success_count = 0
            failed_stores = []

            # 创建处理单个门店的异步函数
            async def process_eleme_store(store_data):
                try:
                    store_name = store_data['name']
                    platform = store_data['platform']
                    
                    # 更新进度对话框状态
                    progress_dialog.update_store_progress(store_name, platform, "正在初始化...")
                    
                    # 创建API实例
                    api = ElemeAPI(store_data['cookies'], store_name)
                    if not await api.initialize():
                        progress_dialog.update_store_progress(store_name, platform, "初始化失败")
                        raise Exception("饿了么API初始化失败")

                    # 获取门店当前的分类列表
                    progress_dialog.update_store_progress(store_name, platform, "获取分类列表...")
                    categories = await api.category.get_category_list()
                    
                    # 计算总步骤数用于进度显示
                    total_steps = len(current_categories)
                    current_step = 0
                    
                    # 遍历目标顺序，逐个处理分类排序
                    for target_idx, target_category_name in enumerate(current_categories):
                        try:
                            current_step += 1
                            progress = int((current_step / total_steps) * 100)
                            progress_dialog.update_store_progress(
                                store_name, 
                                platform, 
                                f"{progress}% - 正在处理分类: {target_category_name}"
                            )
                            
                            current_idx = next((idx for idx, cat in enumerate(categories) 
                                            if cat['customCategoryName'] == target_category_name), None)
                            
                            if current_idx is None:
                                default_logger.warning(f"分类 {target_category_name} 在门店 {store_name} 中不存在，跳过")
                                continue
                                
                            if current_idx != target_idx-1:
                                updated_categories = rank_move(categories, current_idx, target_idx-1)
                                update_data = [{
                                    'customCategoryId': cat['customCategoryId'],
                                    'rank': cat['rank']
                                } for cat in updated_categories]
                                
                                result = await api.category.update_sequence(update_data)
                                
                                if result and result.get('data', {}).get('errMessage') == 'success':
                                    categories = updated_categories
                                    await asyncio.sleep(0.5)
                                else:
                                    raise Exception(f"更新失败: {result.get('data', {}).get('errMessage')}")
                            
                        except Exception as e:
                            progress_dialog.update_store_progress(store_name, platform, f"处理分类 {target_category_name} 失败")
                            default_logger.error(f"移动分类 {target_category_name} 失败: {str(e)}")
                            return False, f"{store_name}-{platform}"
                    
                    # 更新最终进度
                    progress_dialog.update_store_progress(store_name, platform, "100% - 排序完成")
                    return True, store_name
                    
                except Exception as e:
                    error_msg = f"更新分类排序失败: {str(e)}"
                    progress_dialog.update_store_progress(store_name, platform, error_msg)
                    default_logger.error(f"更新门店 {store_name}-{platform} 的分类排序时发生错误: {str(e)}")
                    return False, f"{store_name}-{platform}"

            async def process_meituan_store(store_data):
                try:
                    store_name = store_data['name']
                    platform = store_data['platform']
                    
                    # 更新进度对话框状态
                    progress_dialog.update_store_progress(store_name, platform, "正在初始化...")
                    
                    # 创建API实例
                    api = MeituanAPI(store_data['cookies'], MtgsigGenerator())
                    
                    # 获取门店当前的分类列表
                    progress_dialog.update_store_progress(store_name, platform, "获取分类列表...")
                    categories_result = await api.get_category_list()
                    if not categories_result or categories_result.get('data') is None:
                        progress_dialog.update_store_progress(store_name, platform, "获取分类列表失败")
                        raise Exception(f"获取分类列表失败: {categories_result}")
                    
                    store_categories = categories_result['data']['tagList']
                    
                    # 计算总步骤数用于进度显示
                    total_steps = len(current_categories)
                    current_step = 0
                    
                    new_sequence = []
                    for category_name in current_categories:
                        current_step += 1
                        progress = int((current_step / total_steps) * 100)
                        progress_dialog.update_store_progress(
                            store_name, 
                            platform, 
                            f"{progress}% - 正在处理分类: {category_name}"
                        )
                        
                        for cat in store_categories:
                            if str(cat['name']) == str(category_name):
                                if cat['name'] == '未分类':
                                    continue
                                new_sequence.append(cat['id'])
                                break
                    
                    # 处理未在目标序列中的分类
                    progress_dialog.update_store_progress(store_name, platform, "处理剩余分类...")
                    for cat in store_categories:
                        if str(cat['name']) not in map(str, current_categories):
                            new_sequence.append(cat['id'])
                    
                    # 更新排序
                    progress_dialog.update_store_progress(store_name, platform, "正在保存排序...")
                    result = await api.update_category_sequence(new_sequence)
                    if result and result.get('msg') == None:
                        progress_dialog.update_store_progress(store_name, platform, "100% - 排序完成")
                        return True, store_data['name']
                    else:
                        error_msg = f"更新失败: {result.get('msg')}"
                        progress_dialog.update_store_progress(store_name, platform, error_msg)
                        return False, f"{store_data['name']}-美团"
                    
                except Exception as e:
                    error_msg = f"更新分类排序失败: {str(e)}"
                    progress_dialog.update_store_progress(store_name, platform, error_msg)
                    default_logger.error(f"更新门店 {store_data['name']} 的分类排序时发生错误: {str(e)}")
                    return False, f"{store_data['name']}-美团"

            # 并发处理所有门店
            tasks = []
            for store in eleme_stores:
                tasks.append(process_eleme_store(store))
            for store in meituan_stores:
                tasks.append(process_meituan_store(store))
                
            results = await asyncio.gather(*tasks)
            
            # 处理结果
            for success, store_name in results:
                if success:
                    success_count += 1
                else:
                    failed_stores.append(store_name)
            
            # 显示结果
            if failed_stores:
                message = f"分类排序完成。\n成功：{success_count}个门店\n失败：{len(failed_stores)}个门店\n失败门店：{', '.join(failed_stores)}"
                InfoBar.error(
                    title="部分失败",
                    content=message,
                    parent=self,
                    duration=3000,
                )
            else:
                InfoBar.success(
                    title="成功",
                    content=f"所有门店({success_count}个)分类排序已完成",
                    parent=self,
                    duration=2000,
                )
                
            # 关闭进度对话框
            progress_dialog.close()
            
        except Exception as e:
            default_logger.error(f"保存分类排序时发生错误: {str(e)}")
            InfoBar.error(
                title="错误",
                content=f"保存分类排序时发生错误: {str(e)}",
                parent=self,
                duration=3000,
            )

    def handle_save_sequence(self):
        """处理保存排序按钮点击"""
        # 禁用保存按钮
        self.category_tree.save_btn.setEnabled(False)
        # 修改按钮文本来表示加载状态
        self.category_tree.save_btn.setText("保存中...")

        async def save():
            try:
                await self.save_category_sequence()
            except Exception as e:
                default_logger.error(f"保存排序时发生错误: {str(e)}")
            finally:
                # 重新启用保存按钮
                self.category_tree.save_btn.setText("保存")
                self.category_tree.save_btn.setEnabled(True)
        
        # 使用QTimer延迟执行异步操作
        QTimer.singleShot(0, lambda: asyncio.create_task(save()))

    def enter_sequence_edit_mode(self):
        """进入排序编辑模式"""
        if not self.table.rowCount():
            InfoBar.warning(
                title='提示',
                content='当前没有商品数据',
                parent=self,
                position=InfoBarPosition.TOP,
                duration=2000
            )
            return

        self.is_sequence_editing = True
        
        # 保存当前表格数据
        self.original_table_data = self.get_current_table_data()
        
        # 显示保存和取消按钮
        self.save_sequence_btn.show()
        self.cancel_sequence_btn.show()
        
        # 隐藏编辑排序按钮
        self.edit_sequence_btn.hide()
        
        # 禁用其他功能按钮
        self.disable_other_buttons(True)
        
        # 修改表格显示
        self.setup_sequence_edit_table()
    
    def get_current_table_data(self):
        """获取当前表格数据"""
        data = []
        for row in range(self.table.rowCount()):
            row_data = []
            for col in range(self.table.columnCount()):
                item = self.table.item(row, col)
                if item:
                    row_data.append(item.text())
                else:
                    row_data.append("")
            data.append(row_data)
        return data
    
    def setup_sequence_edit_table(self):
        """设置排序编辑模式的表格显示"""
        try:
            # 保存当前列的可见性状态
            self.column_visibility = [not self.table.isColumnHidden(i) 
                                    for i in range(self.table.columnCount())]
            
            # 隐藏除了前三列之外的所有列
            for col in range(self.table.columnCount()):
                if col > 2:  # 只保留商品ID、商品名称、图片这三列
                    self.table.setColumnHidden(col, True)
            
            # 添加排序列
            sequence_col = self.table.columnCount()
            self.table.insertColumn(sequence_col)
            self.table.setHorizontalHeaderItem(sequence_col, QTableWidgetItem("排序"))
            
            # 设置排序列的宽度为80像素
            self.table.setColumnWidth(sequence_col, 80)
            
            # 设置排序列的值
            for row in range(self.table.rowCount()):
                sequence_item = QTableWidgetItem(str(row + 1))
                sequence_item.setFlags(sequence_item.flags() | Qt.ItemIsEditable)
                sequence_item.setTextAlignment(Qt.AlignCenter)  # 居中对齐
                self.table.setItem(row, sequence_col, sequence_item)
            
            # 设置表格为单元格选择模式
            self.table.setSelectionBehavior(QAbstractItemView.SelectItems)
            
            # 设置表格的编辑触发方式为双击编辑
            self.table.setEditTriggers(QAbstractItemView.DoubleClicked | 
                                     QAbstractItemView.EditKeyPressed)
            # 设置列宽可以自由拖动
            self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Interactive)

            
            # 连接单元格编辑完成信号
            self.table.cellChanged.connect(self.handle_sequence_changed)
            
        except Exception as e:
            InfoBar.error(
                title='错误',
                content=f'设置排序表格失败: {str(e)}',
                parent=self,
                position=InfoBarPosition.TOP,
                duration=2000
            )
    
    def disable_other_buttons(self, disabled: bool):
        """禁用/启用其他功能按钮"""
        # 这里添加需要禁用的其他按钮和控件
        controls_to_disable = [
            self.sync_button,
            self.search_edit,
            self.store_combo,
            self.category_tree
        ]
        
        # 禁用/启用普通控件
        for control in controls_to_disable:
            if hasattr(control, 'setEnabled'):
                control.setEnabled(not disabled)
        
        # 特殊处理状态按钮组
        if hasattr(self, 'status_button_group'):
            for button in self.status_button_group.buttons():
                if button:
                    button.setEnabled(not disabled)
    
    def handle_sequence_changed(self, row: int, column: int):
        """处理排序号变更"""
        if not self.is_sequence_editing:
            return
            
        sequence_col = self.table.columnCount() - 1
        if column != sequence_col:
            return
            
        try:
            # 获取新的排序号
            new_sequence = int(self.table.item(row, column).text())
            if new_sequence < 1:
                raise ValueError("排序号必须大于0")
                
            # 更新表格排序
            self.update_table_sequence(row, new_sequence)
            
        except ValueError as e:
            # 还原为原始排序号
            self.table.item(row, column).setText(str(row + 1))
            InfoBar.error(
                title='错误',
                content=str(e),
                parent=self,
                position=InfoBarPosition.TOP,
                duration=2000
            )
    
    def update_table_sequence(self, changed_row: int, new_sequence: int):
        """更新表格排序"""
        try:
            sequence_col = self.table.columnCount() - 1
            total_rows = self.table.rowCount()
            
            # 确保新序号在有效范围内
            new_sequence = max(1, min(new_sequence, total_rows))
            
            # 获取当前行的所有数据和图片信息
            row_data = []
            image_url = None
            pictures = None
            
            for col in range(self.table.columnCount()):
                if col == 2:  # 图片列
                    # 获取图片URL和pictures信息
                    item = self.table.item(changed_row, 0)  # 获取商品ID
                    if item:
                        product_id = item.text()
                        with get_session_context() as session:
                            product = session.query(Product).filter_by(
                                store_id=self.current_store_id,
                                product_id=product_id
                            ).first()
                            if product:
                                image_url = product.picture
                                pictures = product.pictures
                    row_data.append(None)
                else:
                    item = self.table.item(changed_row, col)
                    if item:
                        row_data.append(item.text())
                    else:
                        row_data.append("")
            
            # 暂时断开信号连接
            try:
                self.table.cellChanged.disconnect(self.handle_sequence_changed)
            except:
                pass
            
            # 删除原行
            self.table.removeRow(changed_row)
            
            # 在新位置插入行
            self.table.insertRow(new_sequence - 1)
            
            # 恢复行数据和图片
            for col, data in enumerate(row_data):
                if col == 2:  # 图片列
                    if image_url:
                        # 重新创建图片部件
                        image_path = self.get_cached_image(image_url)
                        if image_path:
                            self.update_image_cell(new_sequence - 1, image_path, pictures)
                else:
                    if data is not None:
                        self.table.setItem(new_sequence - 1, col, QTableWidgetItem(str(data)))
            
            # 重新生成序号
            for row in range(total_rows):
                self.table.setItem(row, sequence_col, QTableWidgetItem(str(row + 1)))
            
            # 重新连接信号
            self.table.cellChanged.connect(self.handle_sequence_changed)
            
        except Exception as e:
            InfoBar.error(
                title='错误',
                content=f'更新排序失败: {str(e)}',
                parent=self,
                position=InfoBarPosition.TOP,
                duration=2000
            )
    
    def cancel_sequence_edit(self):
        """取消排序编辑"""
        try:
            self.is_sequence_editing = False
            
            # 断开单元格编辑信号
            try:
                self.table.cellChanged.disconnect(self.handle_sequence_changed)
            except:
                pass
            
            # 恢复原始数据
            self.restore_original_table_data()
            
            # 恢复按钮状态
            self.save_sequence_btn.hide()
            self.cancel_sequence_btn.hide()
            self.edit_sequence_btn.show()
            
            # 启用其他按钮
            self.disable_other_buttons(False)
            
        except Exception as e:
            InfoBar.error(
                title='错误',
                content=f'取消排序编辑失败: {str(e)}',
                parent=self,
                position=InfoBarPosition.TOP,
                duration=2000
            )
    
    def restore_original_table_data(self):
        """恢复原始表格数据"""
        try:
            # 移除排序列
            self.table.removeColumn(self.table.columnCount() - 1)
            
            # 恢复列的可见性状态
            for col, visible in enumerate(self.column_visibility):
                self.table.setColumnHidden(col, not visible)
            
            # 清空表格
            self.table.setRowCount(0)
            
            # 重新加载当前分类的数据
            # if self.current_category_id:
            #     asyncio.create_task(self.refresh_table(category_id=self.current_category_id))
            # else:
            #     asyncio.create_task(self.refresh_table())

            asyncio.create_task(self.on_category_selected(self.current_item, 0))
                    
        except Exception as e:
            InfoBar.error(
                title='错误',
                content=f'恢复原始数据失败: {str(e)}',
                parent=self,
                position=InfoBarPosition.TOP,
                duration=2000
            )
    
    async def save_sequence(self):
        """保存排序"""
        try:
            # 获取当前表格中的商品顺序（作为基准）
            sequence_col = self.table.columnCount() - 1
            new_products = []
            for row in range(self.table.rowCount()):
                product_id = self.table.item(row, 0).text()
                product_name = self.table.item(row, 1).text()
                sequence = int(self.table.item(row, sequence_col).text())
                new_products.append({
                    'id': product_id,
                    'name': product_name,
                    'sequence': sequence
                })

            # 显示门店选择对话框
            stores = []
            for i in range(self.store_combo.count()):
                store_data = self.store_combo.itemData(i)
                if store_data:
                    stores.append(store_data)

            dialog = StoreSelectorDialog(stores, self.last_selected_stores, self)
            if dialog.exec() != QDialog.Accepted:
                return

            # 获取选中的门店
            selected_stores = dialog.get_selected_stores()
            if not selected_stores:
                InfoBar.warning(
                    title='提示',
                    content='请选择至少一个门店',
                    parent=self,
                    position=InfoBarPosition.TOP,
                    duration=2000
                )
                return

            # 保存选中的门店ID
            self.last_selected_stores = {store['id'] for store in selected_stores}

            # 显示加载遮罩
            self.loading_mask.show()
            self.loading_mask.set_message("正在保存排序...")

            # 处理每个门店的排序
            results = []
            for store in selected_stores:
                try:
                    # 获取门店对应的分类ID
                    with get_session_context() as session:
                        category = session.query(Category).filter_by(
                            store_id=store['id'],
                            name=self.current_category_name
                        ).first()

                        if not category:
                            results.append({
                                'store_name': store['name'],
                                'success': False,
                                'error': '未找到对应分类'
                            })
                            continue

                        # 获取该门店该分类下的所有商品,应该是从平台后端获取
                        if store['platform'] == '美团':
                            api = MeituanAPI(
                                cookies=store['cookies'],
                                sign_generator=self.sign_generator,
                                store_name=store['name']
                            )
                            
                            result = await api.get_product_list(
                                tag_id=category.category_id,
                                page_num=1,
                                page_size=100
                            )
                            all_products = result.get('data', {}).get('productList', [])

                            # 构建老的商品列表
                            old_products = [{
                                'id': p['id'],
                                'name': p['name'],
                                'sequence': i+1
                            } for i, p in enumerate(all_products)]
                        elif store['platform'] == '饿了么':
                            api = ElemeAPI(
                                cookies=store['cookies'],
                                shop_name=store['name']
                            )
                            await api.initialize()
                            result = await api.product.get_product_list_by_category(
                                category_id=category.category_id
                            )
                            all_products = result.get('data', {}).get('data', [])
                            # 构建老的商品列表
                            old_products = [{
                                'id': p['itemId'],
                                'name': p['title'],
                                'sequence': p['rank']
                            } for p in all_products]
                        

                        # 构建 store_products 列表
                        store_products = []
                        store_products_for_api = []
                        if store['platform'] == '饿了么':
                            


                            sequence_counter = 1
                            seen_names = set()

                            # 1. 首先处理基准中的商品
                            for base_product in new_products:
                                # 跳过已处理的重复名称
                                if base_product['name'] in seen_names:
                                    continue

                                # 在当前门店中查找对应名称的商品
                                matching_product = next(
                                    (p for p in all_products if p['title'] == base_product['name']),
                                    None
                                )
                                if matching_product:
                                    seen_names.add(base_product['name'])  # 记录已处理名称
                                    store_products.append({
                                        'spuId': matching_product['itemId'],
                                        'sequence': sequence_counter
                                    })
                                    sequence_counter += 1

                            # 2. 处理不在基准中的商品
                            remaining_products = [
                                p for p in all_products
                                if p['title'] not in [bp['name'] for bp in new_products]
                            ]

                            # 按照名称排序剩余的商品
                            remaining_products.sort(key=lambda p: p['title'])

                            # 添加剩余的商品到 store_products，并进行去重
                            for product in remaining_products:
                                if product['title'] not in seen_names:
                                    seen_names.add(product['title'])
                                    store_products.append({
                                        'spuId': product['itemId'],
                                        'sequence': sequence_counter
                                    })
                                    sequence_counter += 1
                            
                            # 调用 find_rank_differences 方法获取需要更新的商品列表
                            rank_differences = self.find_rank_differences(old_products, [
                                {
                                    'id': p['spuId'],
                                    'name': next((item['name'] for item in new_products if item['id'] == p['spuId']), None),
                                    'sequence': len(store_products) - p['sequence']+1
                                } for p in store_products
                            ])

                            # rank_differences = generate_sort_params_by_list(store_products, old_products)
                            # 将 store_products 转换为 API 需要的格式
                            store_products_for_api = [{
                                'id': p['id'],
                                'rank': p['rank']
                            } for p in rank_differences]

                            if not store_products_for_api:
                                results.append({
                                    'store_name': store['name'],
                                    'success': True,
                                    'error': '排序未改变'
                                })
                                continue
                        else:  # 美团平台

                            

                            # 构建完整的商品排序列表
                            sequence_counter = 1
                            seen_names = set()

                            # 1. 首先处理基准中的商品
                            for base_product in new_products:
                                # 跳过已处理的重复名称
                                if base_product['name'] in seen_names:
                                    continue

                                # 在当前门店中查找对应名称的商品
                                matching_product = next(
                                    (p for p in all_products if p['name'] == base_product['name']),
                                    None
                                )
                                if matching_product:
                                    seen_names.add(base_product['name'])  # 记录已处理名称
                                    store_products.append({
                                        'spuId': matching_product['id'],
                                        'sequence': sequence_counter
                                    })
                                    sequence_counter += 1

                            # 2. 处理不在基准中的商品
                            remaining_products = [
                                p for p in all_products
                                if p['name'] not in [bp['name'] for bp in new_products]
                            ]

                            # 按照名称排序剩余的商品
                            remaining_products.sort(key=lambda p: p['name'])

                            # 添加剩余的商品到 store_products，并进行去重
                            for product in remaining_products:
                                if product['name'] not in seen_names:
                                    seen_names.add(product['name'])
                                    store_products.append({
                                        'spuId': product['id'],
                                        'sequence': sequence_counter
                                    })
                                    sequence_counter += 1

                            store_products_for_api = store_products

                        if not store_products_for_api:
                            results.append({
                                'store_name': store['name'],
                                'success': False,
                                'error': '未找到对应商品'
                            })
                            continue

                        # 调用对应平台的API
                        if store['platform'] == '美团':
                            
                            result = await api.update_sequence(
                                tag_id=int(category.category_id),
                                spu_list=store_products_for_api
                            )
                            if result and result.get('code') == 0:
                                # 更新数据库中的排序
                                for product in store_products_for_api:
                                    session.query(Product).filter_by(
                                        store_id=store['id'],
                                        product_id=str(product['spuId'])
                                    ).update(
                                        {"sequence": product['sequence']},
                                        synchronize_session=False
                                    )
                                session.commit()

                                results.append({
                                    'store_name': store['name'],
                                    'success': True,
                                    'error': None
                                })
                            else:
                                results.append({
                                    'store_name': store['name'],
                                    'success': False,
                                    'error': result.get('msg', '美团API返回失败')
                                })

                        elif store['platform'] == '饿了么':
                            
                            try:
                                result = await api.product.update_sequence(
                                    category_id=category.category_id,
                                    product_sequence=[{
                                        'id': p['id'],
                                        'rank': p['rank']
                                    } for p in store_products_for_api]
                                )
                                if result and result.get('data', {}).get('errCode') == '10000':
                                    # 更新数据库中的排序
                                    for product in store_products_for_api:
                                        session.query(Product).filter_by(
                                            store_id=store['id'],
                                            product_id=str(product['id'])
                                        ).update(
                                            {"sequence": product['rank']},
                                            synchronize_session=False
                                        )
                                    session.commit()

                                    results.append({
                                        'store_name': store['name'],
                                        'success': True,
                                        'error': None
                                    })
                                else:
                                    results.append({
                                        'store_name': store['name'],
                                        'success': False,
                                        'error': result.get('data', {}).get('errMessage', '饿了么API返回失败')
                                    })
                            except Exception as e:
                                results.append({
                                    'store_name': store['name'],
                                    'success': False,
                                    'error': str(e)
                                })
                            finally:
                                if api:
                                    await api.close()
                                

                except AuthenticationExpiredError as e:
                    # 认证过期，标记为跳过
                    default_logger.warning(f"门店 {store['name']} 认证过期: {e.message}")
                    results.append({
                        'store_name': store['name'],
                        'success': True,
                        'skipped': True,
                        'error': e.message
                    })
                except Exception as e:
                    results.append({
                        'store_name': store['name'],
                        'success': False,
                        'error': str(e)
                    })

            # 统计结果
            total_success = sum(1 for r in results if r['success'] and not r.get('skipped', False))
            total_failed = sum(1 for r in results if not r['success'])
            total_skipped = sum(1 for r in results if r.get('skipped', False))
            failed_stores = [f"{r['store_name']}({r['error']})" for r in results if not r['success']]
            skipped_stores = [f"{r['store_name']}({r['error']})" for r in results if r.get('skipped', False)]

            # 显示结果
            message = (
                f"排序保存完成\n"
                f"成功: {total_success}\n"
                f"失败: {total_failed}\n"
                f"跳过: {total_skipped}"
            )

            if failed_stores:
                message += f"\n失败的门店: {', '.join(failed_stores)}"

            if skipped_stores:
                message += f"\n跳过的门店(认证过期): {', '.join(skipped_stores)}"

            InfoBar.success(
                title='完成',
                content=message,
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=3000,
                parent=self
            )

            # 如果全部成功，退出排序模式
            if total_failed == 0:
                self.cancel_sequence_edit()

        except Exception as e:
            InfoBar.error(
                title='错误',
                content=f'保存排序失败: {str(e)}',
                parent=self,
                position=InfoBarPosition.TOP,
                duration=2000
            )
        finally:
            self.loading_mask.hide()


    def generate_sort_params(items: List[Dict], current_pos: int, target_pos: int) -> List[Dict]:
        """
        生成排序参数
        
        Args:
            items: 原始列表
            current_pos: 当前位置
            target_pos: 目标位置
            
        Returns:
            用于API请求的排序参数列表，格式为[{'id': item_id, 'rank': new_position}, ...]
        """
        # 确保目标位置不超出范围
        max_pos = len(items) - 1
        target_pos = min(target_pos, max_pos)
        
        # 创建新的排序列表
        new_items = deepcopy(items)
        item_to_move = new_items.pop(current_pos)
        new_items.insert(target_pos, item_to_move)
        
        # 生成排序参数
        return [
            {'id': item['id'], 'rank': idx}
            for idx, item in enumerate(new_items)
        ]
        
    def find_rank_differences(self,list_e, list_t):
        """
        比较两个对象数组 list_e 和 list_t 中对象的 id 和 sequence 属性，
        找出 id 相同但 sequence 不同的对象，并将这些对象的信息存储在一个新的列表中返回。

        Args:
            list_e (list): 一个对象列表，每个对象都有 'id' 和 'sequence' 属性。
            list_t (list): 另一个对象列表，每个对象也有 'id' 和 'sequence' 属性。

        Returns:
            list: 一个列表，包含所有在 list_e 和 list_t 中 id 相同但 rank 不同的对象的信息，
                每个对象以字典形式表示：{'id': id, 'rank': rank}。
        """

        # 创建一个字典，以 list_e 中每个对象的 id 作为键，对应的 sequence 作为值
        dict_a = {item['id']: item['sequence'] for item in list_e}

        # 创建一个空列表，用于存储 id 相同但 rank 不同的对象的信息
        result_list = []

        # 遍历 list_t，比较每个对象的 rank 值与 dict_a 中对应 id 的 rank 值
        for item_t in list_t:
            item_id = item_t['id']
            item_rank = item_t['sequence']

            # 如果 list_e 中存在相同的 id 且 rank 值不同，则将该对象的信息添加到 result_list 中
            if item_id in dict_a and dict_a[item_id] != item_rank:
                result_list.append({'id': item_id, 'rank': item_rank})

        return result_list  
    
    

    async def backup_discount_prices(self, selected_products, store_data):
        """备份选中商品的折扣价格"""
        try:
            backup_data = []
            
            for product in selected_products:
                product_data = {
                    'store_name': store_data['name'],
                    'platform': store_data['platform'],
                    'product_id': product.get('id') or product.get('itemId'),
                    'product_name': product.get('name') or product.get('title'),
                    'spec': product.get('spec'),
                    'original_price': product.get('price',None),
                    'discount_price': product.get('activity_price',None),
                    'backup_time': time.strftime('%Y-%m-%d %H:%M:%S')
                }
                
                
                
                backup_data.append(product_data)
            
            # 创建备份文件
            backup_dir = os.path.join(self.root_dir, 'backup', 'discount_prices')
            os.makedirs(backup_dir, exist_ok=True)
            
            backup_file = os.path.join(
                backup_dir, 
                f"discount_backup_{store_data['platform']}_{store_data['name']}_{time.strftime('%Y%m%d_%H%M%S')}.json"
            )
            
            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, ensure_ascii=False, indent=4)
            
            return backup_file
            
        except Exception as e:
            default_logger.error(f"备份折扣价格失败: {e}")
            raise

    def handle_backup_discount(self):
        """处理备份折扣价格按钮点击"""
        try:
            # 获取当前选中的商品
            selected_rows = sorted(set(item.row() for item in self.table.selectedItems()))
            
            if not selected_rows:
                QMessageBox.warning(self, '警告', '请选择要备份折扣价格的商品')
                return
            
            # 获取当前门店信息
            current_store = self.store_combo.currentData()
            if not current_store:
                QMessageBox.warning(self, '警告', '请先选择门店')
                return
            
            # 获取选中商品的数据
            selected_products = []
            for row in selected_rows:
                product_data = {}
                product_data['id'] = self.table.item(row, 0).text()
                product_data['name'] = self.table.item(row, 1).text()
                
                # 获取规格信息
                specs_widget = self.table.cellWidget(row, 3)
                if specs_widget:
                    spec_texts = []
                    layout = specs_widget.layout  #  移除括号
                    if layout:
                        for i in range(layout.count()):
                            widget = layout.itemAt(i).widget()
                            if isinstance(widget, QLabel):
                                spec_texts.append(widget.text())
                    product_data['spec'] = ', '.join(spec_texts)  # 将多个规格信息合并为一个字符串
                else:
                    product_data['spec'] = ''

                # 获取原价信息
                specs_widget = self.table.cellWidget(row, 4)
                if specs_widget:
                    spec_texts = []
                    layout = specs_widget.layout  #  移除括号
                    if layout:
                        for i in range(layout.count()):
                            widget = layout.itemAt(i).widget()
                            
                            spec_texts.append(widget.edit.text())
                    product_data['price'] = ','.join(spec_texts)  # 将多个规格信息合并为一个字符串
                else:
                    product_data['price'] = ''
                
                # 获取折扣价信息
                specs_widget = self.table.cellWidget(row, 5)
                if specs_widget:
                    spec_texts = []
                    layout = specs_widget.layout  #  移除括号
                    if layout:
                        for i in range(layout.count()):
                            widget = layout.itemAt(i).widget()
                            
                            spec_texts.append(widget.edit.text())
                    product_data['activity_price'] = ','.join(spec_texts)  # 将多个规格信息合并为一个字符串
                else:
                    product_data['activity_price'] = ''
                
                if product_data:
                    selected_products.append(product_data)
            
            async def backup():
                try:
                    backup_file = await self.backup_discount_prices(selected_products, current_store)
                    QMessageBox.information(
                        self, 
                        '提示', 
                        f'折扣价格备份成功\n备份文件：{backup_file}'
                    )
                except Exception as e:
                    QMessageBox.critical(
                        self, 
                        '错误',
                        f'备份折扣价格失败: {str(e)}'
                    )
            
            asyncio.create_task(backup())
            
        except Exception as e:
            default_logger.error(f"处理备份折扣价格失败: {e}")
            QMessageBox.critical(self, '错误', f'备份失败: {str(e)}')

    def handle_restore_discount(self):
        """处理恢复折扣价格按钮点击"""
        try:
            # 选择备份文件
            backup_dir = os.path.join(self.root_dir, 'backup', 'discount_prices')
            if not os.path.exists(backup_dir):
                QMessageBox.warning(self, '警告', '未找到折扣价格备份文件')
                return
            
            file_dialog = QFileDialog()
            file_dialog.setFileMode(QFileDialog.ExistingFile)
            file_dialog.setNameFilter("折扣价格备份文件 (discount_backup_*.json)")
            file_dialog.setDirectory(backup_dir)
            
            # 获取当前门店信息
            current_store = self.store_combo.currentData()
            if current_store:
                # 构建当前门店的文件名过滤规则
                store_name = current_store['name']
                platform = current_store['platform']
                file_dialog.setNameFilter(f"折扣价格备份文件 (discount_backup_{platform}_{store_name}_*.json)")
            
            if file_dialog.exec_():
                selected_files = file_dialog.selectedFiles()
                if selected_files:
                    self.discount_backup_file = selected_files[0]
                    self.show_restore_dialog()
        
        except Exception as e:
            default_logger.error(f"处理恢复折扣价格失败: {e}")
            QMessageBox.critical(self, '错误', f'恢复失败: {str(e)}')

    def show_restore_dialog(self):
        """显示恢复确认对话框"""
        try:
            with open(self.discount_backup_file, 'r', encoding='utf-8') as f:
                backup_data = json.load(f)
            
            dialog = QDialog(self)
            dialog.setWindowTitle('恢复折扣价格')
            dialog.setMinimumWidth(1200)
            
            layout = QVBoxLayout()
            
            # 添加信息标签
            info_label = QLabel(f'备份文件：{os.path.basename(self.discount_backup_file)}')
            layout.addWidget(info_label)
            
            # 创建表格显示备份数据
            table = QTableWidget()
            table.setColumnCount(7)
            table.setHorizontalHeaderLabels([ '门店','平台', '商品名称','商品id','规格', '原价', '折扣价'])
            
            for item in backup_data:
                specs_list= item['spec'].split(',')
                original_price_list= item['original_price'].split(',')
                discount_price_list= item['discount_price'].split(',')
                for i in range(len(specs_list)):
                    row = table.rowCount()
                    table.insertRow(row)
                    table.setItem(row, 0, QTableWidgetItem(item['store_name']))
                    table.setItem(row, 1, QTableWidgetItem(item['platform']))
                    table.setItem(row, 2, QTableWidgetItem(item['product_name']))
                    table.setItem(row, 3, QTableWidgetItem(item['product_id']))
                    table.setItem(row, 4, QTableWidgetItem(specs_list[i].strip()))
                    table.setItem(row, 5, QTableWidgetItem(original_price_list[i].strip()))
                    table.setItem(row, 6, QTableWidgetItem(discount_price_list[i].strip()))
            
            table.resizeColumnsToContents()
            layout.addWidget(table)
            
            # 添加进度条
            progress_bar = QProgressBar()
            progress_bar.setVisible(False)
            layout.addWidget(progress_bar)
            
            # 添加按钮
            button_layout = QHBoxLayout()
            restore_btn = QPushButton('开始恢复')
            cancel_btn = QPushButton('取消')
            
            button_layout.addWidget(restore_btn)
            button_layout.addWidget(cancel_btn)
            layout.addLayout(button_layout)
            
            dialog.setLayout(layout)
            
            # 连接信号
            cancel_btn.clicked.connect(dialog.reject)
            restore_btn.clicked.connect(lambda: self.start_restore(dialog, progress_bar, table))
            
            dialog.exec_()
            
        except Exception as e:
            default_logger.error(f"显示恢复对话框失败: {e}")
            QMessageBox.critical(self, '错误', f'显示恢复对话框失败: {str(e)}')

    def start_restore(self, dialog, progress_bar, table):
        """开始恢复折扣价格"""
        try:
            self.is_restoring_discount = True
            self.restore_progress = 0
            self.restore_total = table.rowCount()
            self.restore_results = []
            
            progress_bar.setVisible(True)
            progress_bar.setMaximum(self.restore_total)
            progress_bar.setValue(0)
            
            async def restore():
                try:
                    for row in range(table.rowCount()):
                        try:
                            if table.item(row, 1).text() == '美团':
                                store_id=self.api.cookies.get('wmPoiId')
                                # item_act_id在数据表中discounts表查询
                                with get_session_context() as session:
                                    discounts = session.query(Discount).filter_by(
                                        store_id=store_id,
                                        product_id=table.item(row, 3).text()
                                    ).all()
                                    for discount in discounts:
                                        # 通过discount.sku_id在prodcuts_skus表查询spec
                                        sku = session.query(ProductSku).filter_by(
                                            sku_id=discount.sku_id
                                        ).first()
                                        spec = sku.spec
                                        if spec == table.item(row, 4).text():
                                            item_act_id = discount.itemact_id
                                            break
                                    result = await self.api.update_discount(
                                        item_act_id=item_act_id,
                                        act_price=table.item(row, 6).text(),
                                        store_id=store_id
                                    )
                                    success = result and result.get('code') == 0
                                
                            elif table.item(row, 1).text() == '饿了么':
                                
                                try:
                                    # 通过product_id在数据表中discounts表查询sku_id
                                    with get_session_context() as session:
                                        sku_id = session.query(Discount).filter_by(
                                            product_id=table.item(row, 3).text()
                                        ).first().sku_id
                                    result = await self.api.discount.update_discount(
                                        sku_id=sku_id,
                                        special_price=table.item(row, 6).text()
                                    )
                                    success = result
                                except Exception as e:
                                    default_logger.error(f"恢复折扣价格失败: {e}")
                                    success = False
                            
                            self.restore_results.append({
                                'product_name': table.item(row, 2).text(),
                                'store_name': table.item(row, 0).text(),
                                'platform': table.item(row, 1).text(),
                                'success': success,
                                'message': '恢复成功' if success else '恢复失败'
                            })
                            
                        except Exception as e:
                            self.restore_results.append({
                                'product_name': table.item(row, 2).text(),
                                'store_name': table.item(row, 0).text(),
                                'platform': table.item(row, 1).text(),
                                'success': False,
                                'message': str(e)
                            })
                        
                        self.restore_progress += 1
                        progress_bar.setValue(self.restore_progress)
                    
                    self.show_restore_results(table)
                    # dialog.accept()
                    
                except Exception as e:
                    default_logger.error(f"恢复折扣价格失败: {e}")
                    QMessageBox.critical(self, '错误', f'恢复失败: {str(e)}')
                    # dialog.reject()
                
                finally:
                    self.is_restoring_discount = False
            
            asyncio.create_task(restore())
            
        except Exception as e:
            default_logger.error(f"开始恢复折扣价格失败: {e}")
            QMessageBox.critical(self, '错误', f'开始恢复失败: {str(e)}')
            # dialog.reject()

    def show_restore_results(self,table):
        """显示恢复结果"""
        try:
            # 添加一列显示恢复结果
            table.setColumnCount(8)
            table.setHorizontalHeaderLabels([ '门店','平台', '商品名称','商品id','规格', '原价', '折扣价','恢复结果'])
            
            success_count = 0
            fail_count = 0
            
            for row, result in enumerate(self.restore_results):
                
                table.setItem(row, 7, QTableWidgetItem('成功' if result['success'] else '失败'))
                table.item(row, 7).setToolTip(result['message'])
                if result['success']:
                    success_count += 1
                else:
                    fail_count += 1
                    # 设置失败行的背景色为浅红色
                    table.item(row, 7).setBackground(QColor(255, 200, 200))
            

            
        except Exception as e:
            default_logger.error(f"显示恢复结果失败: {e}")
            QMessageBox.critical(self, '错误', f'显示恢复结果失败: {str(e)}')

    def handle_backup_products(self):
        """处理备份商品信息"""
        # 获取所有门店数据
        stores = []
        for i in range(self.store_combo.count()):
            store_data = self.store_combo.itemData(i)
            if store_data:
                store = {
                    'id': store_data['id'],
                    'name': store_data['name'],
                    'platform': store_data['platform'],
                    'cookies': store_data.get('cookies'),  # 美团平台需要
                    'client': store_data.get('client')     # 饿了么平台需要
                }
                stores.append(store)
            
        # 显示下载对话框
        dialog = ProductExcelDownloadDialog(stores, self,self.sign_generator)
        dialog.exec_()
