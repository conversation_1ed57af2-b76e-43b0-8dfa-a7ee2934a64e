import sys
import os
from PyQt5.QtWidgets import QApplication
from qasync import QEventLoop
import asyncio

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

from src.ui.main_window import MainWindow
from src.models.store_data import init_db
from src.utils.logger import default_logger

# 添加Lib/site-packages到Python路径以支持CEF Python
lib_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'Lib', 'site-packages')
if os.path.exists(lib_path):
    sys.path.insert(0, lib_path)

# CEF Python imports
try:
    from cefpython3 import cefpython as cef
    CEF_AVAILABLE = True
except ImportError:
    CEF_AVAILABLE = False
    default_logger.warning("CEF Python未安装，将使用PyQt5 WebEngine")

def get_resource_path(relative_path):
    """获取资源文件的绝对路径"""
    if hasattr(sys, '_MEIPASS'):
        # PyInstaller 创建临时文件夹，将路径存储在 _MEIPASS 中
        base_path = sys._MEIPASS
    else:
        base_path = os.path.abspath(".")
    return os.path.join(base_path, relative_path)

def main():
    # 设置 QtWebEngine 环境变量（保留兼容性）
    os.environ['QTWEBENGINE_DISABLE_SANDBOX'] = '1'
    os.environ['QTWEBENGINE_CHROMIUM_FLAGS'] = '--disable-gpu'

    # 确保必要的目录存在
    for dir_name in ['data', 'cache', 'logs']:
        dir_path = get_resource_path(dir_name)
        os.makedirs(dir_path, exist_ok=True)

    # 初始化数据库
    try:
        init_db()
        default_logger.info("数据库初始化成功")
    except Exception as e:
        default_logger.error(f"数据库初始化失败: {e}")
        return

    # 启动应用
    app = QApplication(sys.argv)

    # 初始化CEF（如果可用）
    if CEF_AVAILABLE:
        try:
            # CEF设置 - 修复渲染问题的关键设置
            settings = {
                "debug": False,
                "log_severity": cef.LOGSEVERITY_ERROR,  # 减少日志输出
                "log_file": "",
                "multi_threaded_message_loop": False,
                # 关键修复：禁用GPU加速以解决渲染冲突
                "command_line_args_disabled": False,
            }

            # 在打包环境中设置CEF资源路径
            if hasattr(sys, '_MEIPASS'):
                # PyInstaller环境
                cef_path = os.path.join(sys._MEIPASS, 'cefpython3')
                if os.path.exists(cef_path):
                    settings["resources_dir_path"] = cef_path
                    settings["locales_dir_path"] = os.path.join(cef_path, 'locales')
                    # 设置浏览器子进程路径
                    subprocess_path = os.path.join(cef_path, 'subprocess.exe')
                    if os.path.exists(subprocess_path):
                        settings["browser_subprocess_path"] = subprocess_path

                    # 设置CEF的工作目录
                    import os
                    original_cwd = os.getcwd()
                    os.chdir(cef_path)

                    default_logger.info(f"设置CEF资源路径: {cef_path}")
                    default_logger.info(f"设置CEF locales路径: {os.path.join(cef_path, 'locales')}")
                    default_logger.info(f"设置CEF subprocess路径: {subprocess_path}")
                    default_logger.info(f"切换工作目录到: {cef_path}")
                else:
                    default_logger.warning(f"CEF资源路径不存在: {cef_path}")
            else:
                # 开发环境
                try:
                    import cefpython3
                    cef_dir = os.path.dirname(cefpython3.__file__)
                    settings["resources_dir_path"] = cef_dir
                    settings["locales_dir_path"] = os.path.join(cef_dir, 'locales')
                    subprocess_path = os.path.join(cef_dir, 'subprocess.exe')
                    if os.path.exists(subprocess_path):
                        settings["browser_subprocess_path"] = subprocess_path
                except ImportError:
                    pass

            # 添加命令行参数来修复渲染问题
            command_line_args = [
                "--disable-gpu",                    # 禁用GPU加速
                "--disable-gpu-compositing",       # 禁用GPU合成
                "--disable-gpu-sandbox",           # 禁用GPU沙盒
                "--disable-software-rasterizer",   # 禁用软件光栅化
                "--disable-background-timer-throttling",  # 禁用后台定时器限制
                "--disable-backgrounding-occluded-windows",  # 禁用后台窗口遮挡
                "--disable-renderer-backgrounding",  # 禁用渲染器后台化
                "--disable-features=TranslateUI",   # 禁用翻译UI
                "--disable-ipc-flooding-protection",  # 禁用IPC洪水保护
                "--force-device-scale-factor=1",    # 强制设备缩放因子为1
                "--high-dpi-support=0",             # 禁用高DPI支持
                "--disable-extensions",             # 禁用扩展
                "--no-sandbox",                     # 禁用沙盒（提高兼容性）
            ]

            # 将命令行参数添加到设置中（CEF Python的正确方式）
            if hasattr(cef, 'g_commandLineSwitches'):
                for arg in command_line_args:
                    if '=' in arg:
                        key, value = arg.split('=', 1)
                        cef.g_commandLineSwitches[key.lstrip('-')] = value
                    else:
                        cef.g_commandLineSwitches[arg.lstrip('-')] = ""

            # 在打包环境中，需要切换到CEF目录进行初始化
            original_cwd = None
            if hasattr(sys, '_MEIPASS'):
                cef_path = os.path.join(sys._MEIPASS, 'cefpython3')
                if os.path.exists(cef_path):
                    original_cwd = os.getcwd()
                    os.chdir(cef_path)
                    default_logger.info(f"CEF初始化时切换工作目录到: {cef_path}")

            # 初始化CEF
            cef.Initialize(settings)
            default_logger.info("CEF初始化成功")

            # 恢复原始工作目录
            if original_cwd:
                os.chdir(original_cwd)
                default_logger.info(f"CEF初始化完成，恢复工作目录到: {original_cwd}")
        except Exception as e:
            default_logger.error(f"CEF初始化失败: {e}")

    window = MainWindow()
    window.show()

    # 创建事件循环
    loop = QEventLoop(app)
    asyncio.set_event_loop(loop)

    try:
        # 运行事件循环
        with loop:
            if CEF_AVAILABLE:
                # 创建CEF消息循环定时器
                from PyQt5.QtCore import QTimer
                cef_timer = QTimer()
                cef_timer.timeout.connect(lambda: cef.MessageLoopWork())
                cef_timer.start(10)  # 每10ms处理一次CEF消息

            loop.run_forever()
    finally:
        # 清理CEF资源
        if CEF_AVAILABLE:
            try:
                cef.Shutdown()
                default_logger.info("CEF清理成功")
            except Exception as e:
                default_logger.error(f"CEF清理失败: {e}")

if __name__ == '__main__':
    main()