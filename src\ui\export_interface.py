import asyncio
from PyQt5.QtCore import Qt, QDate, QTimer
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidgetItem,
                           QHeaderView, QSplitter, QFrame, QProgressBar,QButtonGroup,QAbstractItemView,QDialog)
from time import time  # 添加time导入

from qfluentwidgets import (SegmentedWidget, CardWidget, ComboBox, PushButton,
                          TableWidget, DatePicker, SubtitleLabel, PrimaryPushButton,
                          FluentIcon, InfoBar, InfoBarPosition, ScrollArea, LineEdit,
                          CheckBox, TransparentPushButton, SearchLineEdit, SpinBox,
                          ToolButton, StateToolTip, Dialog, MessageBox)
from datetime import datetime, timedelta
from PyQt5.QtGui import QPixmap, QPainter
import os
import json
import hashlib
import requests
from src.models.store_data import (Product, get_session_context, ProductSet, 
                                 Discount, ProductSku, FlowerMaterialCost,ProductComposition)  # 添加FlowerMaterialCost导入
from sqlalchemy import and_, text

from src.api.meituan_api import MeituanAPI
from src.api.eleme import ElemeAPI
from src.ui.components.image_label import ImageLabel
from src.ui.components.ProductMaintenanceTab import ProductMaintenanceTab
from src.ui.components.SmartUpdateTab import SmartUpdateTab
from src.ui.components.ImageLoadQueue import ImageLoadQueue
from src.ui.components.LoadingControl import LoadingControl
from src.models.store_data import Product as StoreProduct
from src.api.eleme import ElemeAPI as ElemeAPISrc
from src.api.meituan_api import MeituanAPI as MeituanAPISrc
from src.utils.mtgsig import MtgsigGenerator
from collections import defaultdict





class SmartUpdateInterface(ScrollArea):
    def __init__(self, parent=None):
        super().__init__(parent=parent)
        self.setup_ui()
        
    def setup_ui(self):
        """主界面布局"""
        self.container = QWidget()
        self.layout = QVBoxLayout(self.container)
        self.layout.setContentsMargins(16, 16, 16, 16)  # 减小整体边距
        self.layout.setSpacing(8)  # 减小间距
        
        # 创建Tab导航
        self.pivot = SegmentedWidget(self)
        self.pivot.setFixedHeight(32)  # 设置固定高度，使标签更紧凑
        self.tab_names = ['智能更新', '成本维护', '商品维护']
        for name in self.tab_names:
            self.pivot.addItem(
                routeKey=name,
                text=name,
            )
        # 尝试连接信号 - 不同版本的qfluentwidgets可能有不同的信号名称
        try:
            # 尝试 currentItemChanged 信号
            if hasattr(self.pivot, 'currentItemChanged'):
                self.pivot.currentItemChanged.connect(self.on_tab_changed)
                print("✓ 连接 currentItemChanged 信号成功")
            else:
                print("✗ 没有 currentItemChanged 信号")
                # 尝试其他可能的信号
                possible_signals = ['itemClicked', 'currentChanged', 'selectionChanged']
                connected = False
                for signal_name in possible_signals:
                    if hasattr(self.pivot, signal_name):
                        signal_obj = getattr(self.pivot, signal_name)
                        if hasattr(signal_obj, 'connect'):
                            signal_obj.connect(self.on_tab_changed)
                            print(f"✓ 连接 {signal_name} 信号成功")
                            connected = True
                            break

                if not connected:
                    print("✗ 无法找到合适的信号进行连接")
                    # 手动为每个项目添加点击事件
                    for i, name in enumerate(self.tab_names):
                        item = self.pivot.widget(name)
                        if item and hasattr(item, 'clicked'):
                            item.clicked.connect(lambda checked, key=name: self.on_tab_changed(key))
                            print(f"✓ 为 {name} 添加点击事件")
        except Exception as e:
            print(f"✗ 连接信号时出错: {e}")
        
        # 添加Tab内容
        self.tabs = {
            '智能更新': SmartUpdateTab(),
            '成本维护': CostMaintenanceTab(),
            '商品维护': ProductMaintenanceTab()
        }
        
        # 构建布局
        self.layout.addWidget(self.pivot)
        for tab in self.tabs.values():
            self.layout.addWidget(tab)
            tab.setVisible(False)
            
        self.tabs[self.tab_names[0]].setVisible(True)
        self.setWidget(self.container)
        self.setWidgetResizable(True)
        
        # # 添加表格单元格点击事件处理
        # self.product_table.cellClicked.connect(self.on_cell_clicked)
        # self.product_table.itemChanged.connect(self.on_item_changed)
        
    def on_tab_changed(self, item):
        """切换Tab事件处理"""
        for name, tab in self.tabs.items():
            tab.setVisible(name == item)

# 以下是三个独立模块化的Tab组件


class CostMaintenanceTab(QWidget):
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.setup_connections()
        self.load_data()  # 加载数据
        
    def setup_ui(self):
        """成本维护Tab内容"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(16)
        
        # 成本编辑表格
        cost_card = CardWidget()
        cost_layout = QVBoxLayout(cost_card)
        
        # 标题和搜索框布局
        header_layout = QHBoxLayout()
        header_layout.addWidget(SubtitleLabel('花材成本维护'))
        
        # 搜索框（默认隐藏）
        self.search_box = SearchLineEdit()
        self.search_box.setPlaceholderText('搜索花材...')
        self.search_box.textChanged.connect(self.on_search)
        self.search_box.hide()
        header_layout.addWidget(self.search_box)
        header_layout.addStretch(1)
        
        cost_layout.addLayout(header_layout)
        
        self.cost_table = TableWidget()
        self.cost_table.setColumnCount(3)
        self.cost_table.setHorizontalHeaderLabels(['花材名称', '成本（元）', '备注'])
        # 设置列宽比例
        self.cost_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.cost_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeToContents)
        self.cost_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.Stretch)
        self.cost_table.setEditTriggers(TableWidget.DoubleClicked)
        cost_layout.addWidget(self.cost_table)
        
        # 操作按钮
        btn_layout = QHBoxLayout()
        self.add_btn = PrimaryPushButton('增加花材', self)
        self.add_btn.setIcon(FluentIcon.ADD)
        self.del_btn = PushButton('删除花材', self)
        self.del_btn.setIcon(FluentIcon.REMOVE)
        
        btn_layout.addWidget(self.add_btn)
        btn_layout.addWidget(self.del_btn)
        btn_layout.addStretch(1)
        cost_layout.addLayout(btn_layout)
        
        layout.addWidget(cost_card)
        
        # 安装事件过滤器以捕获快捷键
        self.installEventFilter(self)
        
    def setup_connections(self):
        """连接信号槽"""
        self.add_btn.clicked.connect(self.add_flower)
        self.del_btn.clicked.connect(self.delete_flower)
        self.cost_table.itemChanged.connect(self.on_item_changed)
        
    def eventFilter(self, obj, event):
        """事件过滤器，用于捕获快捷键"""
        if event.type() == event.KeyPress:
            if event.modifiers() == Qt.ControlModifier and event.key() == Qt.Key_F:
                self.toggle_search()
                return True
        return super().eventFilter(obj, event)
        
    def toggle_search(self):
        """切换搜索框显示状态"""
        if self.search_box.isHidden():
            self.search_box.show()
            self.search_box.setFocus()
        else:
            self.search_box.hide()
            self.search_box.clear()
            # 清除搜索时显示所有行
            self.show_all_rows()
            
    def show_all_rows(self):
        """显示所有行"""
        for row in range(self.cost_table.rowCount()):
            self.cost_table.setRowHidden(row, False)
            
    def on_search(self, text):
        """处理搜索"""
        search_text = text.lower().strip()
        for row in range(self.cost_table.rowCount()):
            item = self.cost_table.item(row, 0)  # 获取花材名称列
            if item:
                if search_text:
                    # 如果有搜索文本，则根据匹配结果显示/隐藏
                    self.cost_table.setRowHidden(
                        row, 
                        search_text not in item.text().lower()
                    )
                else:
                    # 如果搜索框为空，显示所有行
                    self.cost_table.setRowHidden(row, False)
        
    def add_flower(self):
        """添加新花材行"""
        # 创建自定义对话框
        dialog = QDialog(self)
        dialog.setWindowTitle('新增花材')
        dialog.setMinimumWidth(300)
        
        # 对话框布局
        layout = QVBoxLayout(dialog)
        
        # 添加输入框
        name_edit = LineEdit(dialog)
        name_edit.setPlaceholderText('请输入花材名称')
        layout.addWidget(name_edit)
        
        # 按钮布局
        btn_layout = QHBoxLayout()
        confirm_btn = PrimaryPushButton('确认', dialog)
        cancel_btn = PushButton('取消', dialog)
        btn_layout.addWidget(confirm_btn)
        btn_layout.addWidget(cancel_btn)
        layout.addLayout(btn_layout)
        
        # 连接按钮事件
        confirm_btn.clicked.connect(dialog.accept)
        cancel_btn.clicked.connect(dialog.reject)
        
        # 显示对话框
        if dialog.exec_():
            flower_name = name_edit.text().strip()
            if not flower_name:
                InfoBar.warning(
                    title='提示',
                    content='花材名称不能为空',
                    parent=self
                )
                return
                
            try:
                with get_session_context() as session:
                    # 检查是否存在
                    existing = session.query(FlowerMaterialCost).filter_by(花材=flower_name).first()
                    if existing:
                        InfoBar.warning(
                            title='提示',
                            content=f'花材 [{flower_name}] 已存在',
                            parent=self
                        )
                        return
                
                # 添加新行到表格
                row = self.cost_table.rowCount()
                self.cost_table.insertRow(row)
                
                # 花材名称
                name_item = QTableWidgetItem(flower_name)
                self.cost_table.setItem(row, 0, name_item)
                
                # 成本
                cost_item = QTableWidgetItem("0.00")
                self.cost_table.setItem(row, 1, cost_item)
                
                # 备注
                note_item = QTableWidgetItem("")
                self.cost_table.setItem(row, 2, note_item)
                
                InfoBar.success(
                    title='添加成功',
                    content=f'已添加花材 [{flower_name}]',
                    parent=self
                )
                
            except Exception as e:
                InfoBar.error(
                    title='添加失败',
                    content=f'添加花材失败: {str(e)}',
                    parent=self
                )
        
    def delete_flower(self):
        """删除选中花材"""
        selected = self.cost_table.selectedItems()
        if not selected:
            InfoBar.warning(
                title='操作提示',
                content='请先选择要删除的花材行',
                parent=self
            )
            return
            
        # 获取所有需要删除的行和花材名称
        rows = {item.row() for item in selected}
        flower_names = []
        for row in rows:
            name_item = self.cost_table.item(row, 0)
            if name_item and name_item.text().strip():
                flower_names.append(name_item.text().strip())
                
        if not flower_names:
            return
            
        # 显示确认对话框
        box = MessageBox(
            '删除确认',
            f'确定要删除以下花材吗？\n{", ".join(flower_names)}',
            self
        )
        box.yesButton.setText('确定')
        box.cancelButton.setText('取消')
        
        if box.exec():
            try:
                with get_session_context() as session:
                    for row in sorted(rows, reverse=True):
                        # 获取花材名称
                        name_item = self.cost_table.item(row, 0)
                        if name_item and name_item.text().strip():
                            # 从数据库删除
                            session.query(FlowerMaterialCost).filter_by(花材=name_item.text().strip()).delete()
                        
                        # 从表格中删除
                        self.cost_table.removeRow(row)
                    
                    session.commit()
                    InfoBar.success(
                        title='删除成功',
                        content=f'已删除选中的花材: {", ".join(flower_names)}',
                        parent=self
                    )
                    
            except Exception as e:
                InfoBar.error(
                    title='删除失败',
                    content=f'删除花材记录失败: {str(e)}',
                    parent=self
                )
    def on_item_changed(self, item):
        """处理单元格内容变化，实时保存到数据库"""
        if not hasattr(self, '_is_loading'):
            self._is_loading = False
            
        if self._is_loading:
                return
                
        row = item.row()
        col = item.column()
        
        # 获取当前行的数据
        name_item = self.cost_table.item(row, 0)
        cost_item = self.cost_table.item(row, 1)
        note_item = self.cost_table.item(row, 2)
        
        if not all([name_item, cost_item, note_item]):
            return
            
        # 获取数据
        name = name_item.text().strip()
        cost = cost_item.text().strip()
        note = note_item.text().strip()
        
        # 如果是成本列，验证输入是否为有效数字
        if col == 1:  # 成本列
            try:
                cost_value = float(cost or "0")
                cost = f"{cost_value:.2f}"
                cost_item.setText(cost)
            except ValueError:
                cost = "0.00"
                cost_item.setText(cost)
                InfoBar.warning(
                    title='格式错误',
                    content='成本必须是有效的数字',
                    parent=self
                )
        
        try:
            with get_session_context() as session:
                # 查找现有记录
                material = session.query(FlowerMaterialCost).filter_by(花材=name).first()
                
                if material is None:
                    # 新记录
                    material = FlowerMaterialCost(
                        花材=name,
                        成本=cost,
                        备注=note
                    )
                    session.add(material)
                else:
                    # 更新现有记录
                    material.成本 = cost
                    material.备注 = note
                    
                session.commit()
                
        except Exception as e:
                InfoBar.error(
                    title='保存失败',
                content=f'保存数据失败: {str(e)}',
                    parent=self
                )

    def load_data(self):
        """从数据库加载花材成本数据"""
        try:
            self._is_loading = True  # 设置加载标志，防止触发itemChanged信号
            with get_session_context() as session:
                # 查询所有花材成本记录并按名称排序
                materials = session.query(FlowerMaterialCost).order_by(FlowerMaterialCost.花材).all()
                
                # 设置表格行数
                self.cost_table.setRowCount(len(materials))
                
                # 填充数据
                for row, material in enumerate(materials):
                    # 花材名称
                    name_item = QTableWidgetItem(material.花材)
                    self.cost_table.setItem(row, 0, name_item)
                    
                    # 成本
                    cost_item = QTableWidgetItem(material.成本 if material.成本 else "0.00")
                    self.cost_table.setItem(row, 1, cost_item)
                    
                    # 备注
                    note_item = QTableWidgetItem(material.备注 if material.备注 else "")
                    self.cost_table.setItem(row, 2, note_item)
                    
        except Exception as e:
            InfoBar.error(
                title='加载失败',
                content=f'加载花材成本数据失败: {str(e)}',
            parent=self
        )
        finally:
            self._is_loading = False  # 重置加载标志

   