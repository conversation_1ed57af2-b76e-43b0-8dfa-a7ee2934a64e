#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from qfluentwidgets import SegmentedWidget
    print("✓ 成功导入 SegmentedWidget")

    # 创建一个实例来测试
    widget = SegmentedWidget()

    # 检查是否有 currentItemChanged 信号
    if hasattr(widget, 'currentItemChanged'):
        print("✓ SegmentedWidget 有 currentItemChanged 信号")

        # 尝试连接信号
        def test_handler(key):
            print(f"信号触发: {key}")

        try:
            widget.currentItemChanged.connect(test_handler)
            print("✓ 信号连接成功")
        except Exception as e:
            print(f"✗ 信号连接失败: {e}")
    else:
        print("✗ SegmentedWidget 没有 currentItemChanged 信号")

        # 列出所有可用的信号
        print("\n=== 可用的信号 ===")
        for attr in dir(widget):
            if not attr.startswith('_'):
                attr_obj = getattr(widget, attr)
                if hasattr(attr_obj, 'connect'):
                    print(f"信号: {attr}")

except ImportError as e:
    print(f"✗ 导入失败: {e}")
except Exception as e:
    print(f"✗ 其他错误: {e}")

print("测试完成")
