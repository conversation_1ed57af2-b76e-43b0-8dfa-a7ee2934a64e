import json
import aiohttp
from typing import Dict, Any, Optional, Union
from urllib.parse import urlencode
from src.utils.logger import default_logger

class AuthenticationExpiredError(Exception):
    """认证过期异常"""
    def __init__(self, platform: str, store_name: str, message: str = None):
        self.platform = platform
        self.store_name = store_name
        self.message = message or f"{platform}-{store_name}的认证已过期"
        super().__init__(self.message)

class BaseAPI:
    """基础API客户端类，处理通用的请求逻辑"""
    
    def __init__(self, cookies: Dict[str, str], sign_generator: callable):
        """
        初始化API客户端
        
        Args:
            cookies: 包含认证信息的cookie字典
            sign_generator: 签名生成函数
        """
        self.cookies = cookies
        self.sign_generator = sign_generator
        self.logger = default_logger
        
        # 设置通用请求头
        self.headers = {
            'Accept': 'application/json, text/plain, */*',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Origin': 'https://shangoue.meituan.com',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'M-APPKEY': 'fe_waimai_sc_fe_product',
            'Referer': 'https://shangoue.meituan.com/',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'X-Requested-With': 'XMLHttpRequest'
        }
        
        # 设置必要的cookies
        self.base_cookies = {
            'WEBDFPID': cookies.get('WEBDFPID', ''),
            '_lxsdk_cuid': cookies.get('_lxsdk_cuid', ''),
            'terminal': 'bizCenter',
            'uuid_update': 'true',
            'timeout': '2000',
            'device_uuid': cookies.get('device_uuid', ''),
            'bizad_cityId': cookies.get('bizad_cityId', ''),
            'bizad_second_city_id': cookies.get('bizad_second_city_id', ''),
            'bizad_third_city_id': cookies.get('bizad_third_city_id', ''),
            'bizad_first_tag_id': cookies.get('bizad_first_tag_id', ''),
            'iuuid': cookies.get('iuuid', ''),
            'mt_c_token': cookies.get('mt_c_token', ''),
            'oops': cookies.get('oops', ''),
            'userId': cookies.get('userId', ''),
            '_lxsdk': cookies.get('_lxsdk', ''),
            'wmPoiName': cookies.get('wmPoiName', ''),
            'acctId': cookies.get('acctId', ''),
            'wm_order_channel': 'default',
            'swim_line': 'default',
            'grayState': '0',
            'token': cookies.get('token', ''),
            'igateApp': 'igate',
            'wmPoiId': cookies.get('wmPoiId', ''),
            'bsid': cookies.get('bsid', ''),
            '_source': 'PC',
            'wpush_server_url': 'wss://wpush.meituan.com',
            'fromPoiChange': 'false',
            '_lxsdk_s': cookies.get('_lxsdk_s', '')
        }
        
        self.logger.info("API客户端初始化完成")
    
    def _get_common_params(self) -> Dict[str, Union[str, int]]:
        """获取通用查询参数"""
        return {}
    
    def _build_url(self, base_url: str, params: Optional[Dict[str, Any]] = None) -> str:
        """
        构建完整的请求URL
        
        Args:
            base_url: 基础URL
            params: 查询参数字典
            
        Returns:
            str: 完整的请求URL
        """
        # 合并通用参数和自定义参数
        all_params = self._get_common_params()
        if params:
            all_params.update(params)
            
        # 构建URL
        url = f"{base_url}?{urlencode(all_params)}"
        self.logger.debug(f"构建URL: {url}")
        return url
    
    async def _handle_response(self, response: aiohttp.ClientResponse) -> Dict[str, Any]:
        """
        处理API响应
        
        Args:
            response: aiohttp响应对象
            
        Returns:
            Dict[str, Any]: 响应数据
            
        Raises:
            Exception: 当响应状态码不是200或响应数据中的code不是0时抛出异常
        """
        # 检查HTTP状态码
        response.raise_for_status()
        
        # 打印原始响应内容和响应头
        self.logger.debug(f"响应状态码: {response.status}")
        self.logger.debug(f"响应头: {dict(response.headers)}")
        
        # 获取响应文本
        text = await response.text()
        self.logger.debug(f"响应内容: {text}")
        
        # 解析响应数据
        try:
            data = await response.json()
        except Exception as e:
            self.logger.error(f"JSON解析失败: {str(e)}")
            self.logger.error(f"响应内容: {text}")
            raise
            
        # 检查是否需要解密数据
        if isinstance(data, dict) and data.get('encrypted') is True and 'data' in data:
            self.logger.info("检测到加密数据，正在解密...")
            try:
                # 导入解密函数
                from Crypto.Cipher import AES
                from Crypto.Util.Padding import unpad
                import base64
                import json
                
                # 解密数据
                encrypted_data = data['data']
                if not encrypted_data:
                    self.logger.warning("加密数据为空")
                    return data
                    
                # 移除空白字符
                encrypted_data = encrypted_data.replace(" ", "")
                
                # 密钥: sgproduct + decrypt
                key = "sgproductdecrypt"
                key_bytes = key.encode('utf-8')
                
                # 创建AES-ECB解密器
                cipher = AES.new(key_bytes, AES.MODE_ECB)
                
                try:
                    # 尝试base64解码
                    encrypted_bytes = base64.b64decode(encrypted_data)
                except:
                    # 如果不是base64格式，直接使用字节编码
                    encrypted_bytes = encrypted_data.encode('utf-8')
                    
                # 解密并去除填充
                decrypted_bytes = unpad(cipher.decrypt(encrypted_bytes), AES.block_size)
                
                # 转为字符串并解析JSON
                decrypted_text = decrypted_bytes.decode('utf-8')
                decrypted_data = json.loads(decrypted_text)
                
                # 用解密后的数据替换原数据
                self.logger.info("数据解密成功")
                data['data'] = decrypted_data
                data['encrypted'] = False
                
            except Exception as e:
                self.logger.error(f"数据解密失败: {str(e)}")
                self.logger.error(f"原始加密数据: {encrypted_data[:100]}...")
        
        # 检查美团API的认证失效错误
        if isinstance(data, dict):
            code = data.get('code')
            msg = data.get('msg', '')

            # 美团API错误码1001表示认证失效
            if code == 1001 and ('连接出现异常' in msg or '请重新登录' in msg):
                # 获取门店名称（如果有的话）
                store_name = getattr(self, 'store_name', '未知门店')
                self.logger.warning(f"美团-{store_name}的cookies已失效: {msg}")
                raise AuthenticationExpiredError('美团', store_name, f"美团-{store_name}的cookies已失效，请更新登录信息")

        return data
    
    async def get(self, url: str, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """发送异步GET请求"""
        full_url = self._build_url(url, params)
        headers = {**self.headers, 'mtgsig': self.sign_generator(full_url)}
        
        self.logger.info(f"发送GET请求: {full_url}")
        # self.logger.debug(f"请求参数: {params}")
        
        async with aiohttp.ClientSession(cookies=self.base_cookies) as session:
            async with session.get(full_url, headers=headers) as response:
                return await self._handle_response(response)

    async def post(self, url: str, data: Dict[str, Any], params: Optional[Dict[str, Any]] = None, contenttype=None) -> Dict[str, Any]:
        """
        发送异步POST请求

        Args:
            url: str, 请求URL
            data: Dict[str, Any], 请求数据
            params: Optional[Dict[str, Any]], URL参数
            contenttype: Optional[str], 内容类型('json' or None)
        """
        full_url = self._build_url(url, params)

        if contenttype == 'json':
            headers = {
                **self.headers,
                'Content-Type': 'application/json',
                'Origin': 'https://waimaieapp.meituan.com',
                'Referer': 'https://waimaieapp.meituan.com/',
                # 'mtgsig': self.sign_generator(full_url, data)
            }

            async with aiohttp.ClientSession(cookies=self.base_cookies) as session:
                async with session.post(full_url, json=data, headers=headers) as response:
                    return await self._handle_response(response)
        else:
            headers = {
                **self.headers,
                'mtgsig': self.sign_generator(full_url, data)
            }

            async with aiohttp.ClientSession(cookies=self.base_cookies) as session:
                async with session.post(full_url, data=data, headers=headers) as response:
                    return await self._handle_response(response)
    
    async def put(self, url: str, data: Dict[str, Any], params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """发送异步PUT请求"""
        full_url = self._build_url(url, params)
        headers = {**self.headers, 'mtgsig': self.sign_generator(full_url, data)}
        
        self.logger.info(f"发送PUT请求: {full_url}")
        # self.logger.debug(f"请求参数: {params}")
        self.logger.debug(f"请求数据: {data}")
        
        async with aiohttp.ClientSession(cookies=self.base_cookies) as session:
            async with session.put(full_url, data=data, headers=headers) as response:
                return await self._handle_response(response)
    
    async def delete(self, url: str, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """发送异步DELETE请求"""
        full_url = self._build_url(url, params)
        headers = {**self.headers, 'mtgsig': self.sign_generator(full_url)}
        
        self.logger.info(f"发送DELETE请求: {full_url}")
        # self.logger.debug(f"请求参数: {params}")
        
        async with aiohttp.ClientSession(cookies=self.base_cookies) as session:
            async with session.delete(full_url, headers=headers) as response:
                return await self._handle_response(response) 