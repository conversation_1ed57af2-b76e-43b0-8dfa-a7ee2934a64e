#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from PyQt5.QtWidgets import QApplication
    from qfluentwidgets import SegmentedWidget
    
    # 创建应用程序实例（必需的）
    app = QApplication(sys.argv)
    
    print("✓ 成功导入 SegmentedWidget")
    
    # 创建一个实例来测试
    widget = SegmentedWidget()
    
    print("\n=== SegmentedWidget 的所有属性和方法 ===")
    for attr in sorted(dir(widget)):
        if not attr.startswith('_'):
            try:
                attr_obj = getattr(widget, attr)
                if hasattr(attr_obj, 'connect'):
                    print(f"信号: {attr}")
                elif callable(attr_obj):
                    print(f"方法: {attr}()")
                else:
                    print(f"属性: {attr}")
            except Exception as e:
                print(f"错误获取 {attr}: {e}")
    
    # 检查父类
    print(f"\n=== 继承关系 ===")
    print(f"SegmentedWidget 的 MRO: {[cls.__name__ for cls in widget.__class__.__mro__]}")
    
    # 尝试添加项目并测试信号
    print(f"\n=== 测试添加项目 ===")
    try:
        widget.addItem("test1", "测试1")
        widget.addItem("test2", "测试2")
        print("✓ 成功添加项目")
        
        # 尝试连接可能的信号
        possible_signals = ['currentItemChanged', 'itemClicked', 'currentChanged', 'selectionChanged']
        
        for signal_name in possible_signals:
            if hasattr(widget, signal_name):
                signal_obj = getattr(widget, signal_name)
                if hasattr(signal_obj, 'connect'):
                    try:
                        def test_handler(*args):
                            print(f"{signal_name} 信号触发，参数: {args}")
                        signal_obj.connect(test_handler)
                        print(f"✓ 成功连接信号: {signal_name}")
                    except Exception as e:
                        print(f"✗ 连接信号 {signal_name} 失败: {e}")
                else:
                    print(f"- {signal_name} 不是信号")
            else:
                print(f"- 没有 {signal_name} 属性")
                
    except Exception as e:
        print(f"✗ 添加项目失败: {e}")
    
except ImportError as e:
    print(f"✗ 导入失败: {e}")
except Exception as e:
    print(f"✗ 其他错误: {e}")

print("\n测试完成")
