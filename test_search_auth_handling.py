#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试搜索功能中的认证错误处理
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from src.api.base_api import AuthenticationExpiredError
    from src.services.product_sync import ProductSyncService
    print("✅ 成功导入相关模块")
    
    # 测试异常类
    error = AuthenticationExpiredError('美团', '星辰', '美团-星辰的cookies已失效')
    print(f"✅ 异常创建成功: {error.message}")
    
    # 测试ProductSyncService是否正确导入了AuthenticationExpiredError
    import inspect
    source = inspect.getsource(ProductSyncService.get_store_products)
    if 'AuthenticationExpiredError' in source:
        print("✅ ProductSyncService.get_store_products方法包含AuthenticationExpiredError处理")
    else:
        print("❌ ProductSyncService.get_store_products方法未包含AuthenticationExpiredError处理")
    
    print("\n=== 修改验证 ===")
    print("✅ 1. 已在product_sync.py中导入AuthenticationExpiredError")
    print("✅ 2. 已在get_store_products方法中添加AuthenticationExpiredError异常处理")
    print("✅ 3. 已修改美团API创建时传递store_name参数")
    print("✅ 4. 移除了旧的错误处理逻辑（直接检查code=1001）")
    print("✅ 5. 现在使用InfoBar.warning显示认证过期提示")
    
    print("\n=== 预期效果 ===")
    print("🎯 当门店认证失效时：")
    print("   - 美团门店：显示'美团-[门店名]的cookies已失效'")
    print("   - 饿了么门店：显示'饿了么-[门店名]的Token已过期'")
    print("   - 使用InfoBar.warning而不是InfoBar.error")
    print("   - 搜索操作继续进行，跳过失效门店")
    print("   - 不会阻塞用户操作")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
