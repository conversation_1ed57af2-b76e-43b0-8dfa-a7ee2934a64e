#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试认证错误处理的脚本
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.api.base_api import AuthenticationExpiredError
from src.api.meituan_api import MeituanAPI
from src.utils.mtgsig import MtgsigGenerator

async def test_meituan_auth_error():
    """测试美团认证错误处理"""
    print("=== 测试美团认证错误处理 ===")
    
    # 使用无效的cookies来模拟认证失效
    invalid_cookies = {
        'wmPoiId': '12345',
        'token': 'invalid_token'
    }
    
    try:
        sign_generator = MtgsigGenerator('http://113.44.82.43:3000/calculate_signature')
        api = MeituanAPI(
            cookies=invalid_cookies,
            sign_generator=sign_generator,
            store_name="测试门店-星辰"
        )
        
        # 模拟一个会返回1001错误的响应
        test_response = {
            'code': 1001,
            'msg': '连接出现异常，请重新登录',
            'data': {}
        }
        
        # 直接调用_handle_response方法来测试错误处理
        try:
            result = api._handle_response(test_response)
            print("❌ 应该抛出AuthenticationExpiredError异常")
        except AuthenticationExpiredError as e:
            print(f"✅ 正确捕获到认证过期异常: {e.message}")
            print(f"   平台: {e.platform}")
            print(f"   门店: {e.store_name}")
        except Exception as e:
            print(f"❌ 捕获到意外异常: {e}")
            
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")

def test_eleme_auth_error():
    """测试饿了么认证错误处理"""
    print("\n=== 测试饿了么认证错误处理 ===")
    
    try:
        from src.api.eleme import ElemeAPI
        
        # 使用无效的cookies来模拟认证失效
        invalid_cookies = {
            'WMUSS': 'invalid_wmuss'
        }
        
        api = ElemeAPI(
            cookies=invalid_cookies,
            shop_name="测试门店-星辰"
        )
        
        # 模拟Token过期的情况
        test_result = "Token过期，请重新登录"
        
        try:
            # 检查是否包含'过期'关键字
            if '过期' in str(test_result):
                print(f"✅ 检测到Token过期: {test_result}")
                # 这里应该抛出AuthenticationExpiredError
                raise AuthenticationExpiredError('饿了么', api.shop_name, f"饿了么-{api.shop_name}的Token已过期，请更新登录信息")
        except AuthenticationExpiredError as e:
            print(f"✅ 正确捕获到认证过期异常: {e.message}")
            print(f"   平台: {e.platform}")
            print(f"   门店: {e.store_name}")
        except Exception as e:
            print(f"❌ 捕获到意外异常: {e}")
            
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")

def test_error_message_format():
    """测试错误信息格式"""
    print("\n=== 测试错误信息格式 ===")
    
    # 测试美团错误信息
    try:
        raise AuthenticationExpiredError('美团', '星辰', '美团-星辰的cookies已失效，请更新登录信息')
    except AuthenticationExpiredError as e:
        print(f"✅ 美团错误信息格式正确: {e.message}")
        assert '美团-星辰' in e.message
        assert 'cookies已失效' in e.message
    
    # 测试饿了么错误信息
    try:
        raise AuthenticationExpiredError('饿了么', '星辰', '饿了么-星辰的Token已过期，请更新登录信息')
    except AuthenticationExpiredError as e:
        print(f"✅ 饿了么错误信息格式正确: {e.message}")
        assert '饿了么-星辰' in e.message
        assert 'Token已过期' in e.message

async def main():
    """主测试函数"""
    print("开始测试认证错误处理功能...\n")
    
    # 测试美团认证错误
    await test_meituan_auth_error()
    
    # 测试饿了么认证错误
    test_eleme_auth_error()
    
    # 测试错误信息格式
    test_error_message_format()
    
    print("\n=== 测试完成 ===")
    print("✅ 所有测试通过！")
    print("\n修改总结:")
    print("1. ✅ 添加了AuthenticationExpiredError自定义异常类")
    print("2. ✅ 美团API现在能正确处理错误码1001并抛出认证过期异常")
    print("3. ✅ 饿了么API现在能正确处理Token过期并抛出认证过期异常")
    print("4. ✅ 错误信息能明确区分平台和门店名称")
    print("5. ✅ 移除了饿了么的自动登录弹窗逻辑")

if __name__ == "__main__":
    asyncio.run(main())
