#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
CEF Python 窗口大小适配问题修复 MVP
解决网页内容不完全填充Qt widget和动态resize问题
"""

import sys
import os
import ctypes
from ctypes import wintypes
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtCore import QTimer, Qt

# 添加Lib/site-packages到Python路径以支持CEF Python
lib_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'Lib', 'site-packages')
if lib_path not in sys.path:
    sys.path.insert(0, lib_path)

# CEF Python imports
from cefpython3 import cefpython as cef

# Windows API 常量和函数
user32 = ctypes.windll.user32
SWP_NOZORDER = 0x0004
SWP_NOACTIVATE = 0x0010

class ClientHandler:
    """CEF客户端处理器"""

    def __init__(self, parent_widget):
        self.parent_widget = parent_widget

    def OnLoadEnd(self, browser, **_):
        """页面加载完成时的回调"""
        try:
            print("页面加载完成")
            # 确保浏览器正确渲染和调整大小
            QTimer.singleShot(100, lambda: self._ensure_proper_sizing(browser))

        except Exception as e:
            print(f"页面加载完成回调失败: {e}")

    def _ensure_proper_sizing(self, browser):
        """确保浏览器正确调整大小"""
        try:
            if browser and self.parent_widget:
                # 强制调整浏览器大小
                self.parent_widget._force_browser_resize()
                print("页面加载后强制调整浏览器大小")

                # 延迟再次调整，确保完全填充
                QTimer.singleShot(500, lambda: self.parent_widget._force_browser_resize())
                QTimer.singleShot(1000, lambda: self.parent_widget._force_browser_resize())
        except Exception as e:
            print(f"强制调整浏览器大小失败: {e}")


class CEFWidget(QWidget):
    """修复窗口大小适配问题的CEF浏览器组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.browser = None
        self.resize_timer = None
        self.browser_hwnd = None
        self.setFocusPolicy(Qt.StrongFocus)
        
        # 设置窗口属性
        self.setAttribute(Qt.WA_NativeWindow, True)
        self.setAttribute(Qt.WA_DontCreateNativeAncestors, True)

    def embed_browser(self, url):
        """嵌入CEF浏览器 - 修复窗口大小适配问题"""
        try:
            # 如果已经有浏览器实例，先关闭它
            if self.browser:
                self.browser.CloseBrowser(True)
                self.browser = None
                self.browser_hwnd = None

            # 确保窗口已经显示并获得正确的尺寸
            self.show()
            self.repaint()
            QApplication.processEvents()

            # 等待窗口完全初始化
            QTimer.singleShot(500, lambda: self._create_browser_delayed(url))

        except Exception as e:
            print(f"创建CEF浏览器失败: {e}")

    def _create_browser_delayed(self, url):
        """延迟创建浏览器，确保窗口完全初始化"""
        try:
            # 获取当前widget的精确尺寸
            widget_rect = self.rect()
            print(f"Widget尺寸: {widget_rect.width()}x{widget_rect.height()}")

            # 获取Windows客户区尺寸（考虑DPI缩放）
            hwnd = int(self.winId())
            client_rect = ctypes.wintypes.RECT()
            user32.GetClientRect(hwnd, ctypes.byref(client_rect))

            cef_width = client_rect.right - client_rect.left
            cef_height = client_rect.bottom - client_rect.top

            print(f"Windows客户区尺寸: {cef_width}x{cef_height}")

            # 设置CEF窗口信息 - 使用Windows API获取的精确坐标
            window_info = cef.WindowInfo()
            rect = [0, 0, cef_width, cef_height]
            window_info.SetAsChild(hwnd, rect)
            print(f"CEF窗口设置: {rect}")

            # 浏览器设置
            browser_settings = {
                "web_security_disabled": True,
                "plugins_disabled": True,
                "javascript_disabled": False,
                "background_color": 0xFFFFFFFF,
            }

            # 创建浏览器
            self.browser = cef.CreateBrowserSync(
                window_info=window_info,
                url=url,
                settings=browser_settings
            )

            # 设置客户端处理器
            client_handler = ClientHandler(self)
            self.browser.SetClientHandler(client_handler)

            # 获取浏览器窗口句柄
            self._get_browser_hwnd()

            # 立即同步调用一次resize
            try:
                self.browser.WasResized()
                print("浏览器创建后立即同步resize")
            except:
                pass

            # 然后多次异步触发强制resize以确保初始渲染正确
            QTimer.singleShot(50, lambda: self._force_browser_resize())
            QTimer.singleShot(100, lambda: self._force_browser_resize())
            QTimer.singleShot(200, lambda: self._force_browser_resize())
            QTimer.singleShot(300, lambda: self._force_browser_resize())
            QTimer.singleShot(500, lambda: self._force_browser_resize())

            print(f"CEF浏览器创建成功: {url}")

        except Exception as e:
            print(f"延迟创建CEF浏览器失败: {e}")

    def _get_browser_hwnd(self):
        """获取CEF浏览器的窗口句柄"""
        try:
            if self.browser:
                # 尝试获取浏览器窗口句柄
                browser_hwnd = self.browser.GetWindowHandle()
                if browser_hwnd:
                    self.browser_hwnd = browser_hwnd
                    print(f"获取到浏览器窗口句柄: {browser_hwnd}")
                else:
                    # 如果直接获取失败，尝试查找子窗口
                    QTimer.singleShot(200, self._find_browser_hwnd)
        except Exception as e:
            print(f"获取浏览器窗口句柄失败: {e}")

    def _find_browser_hwnd(self):
        """查找CEF浏览器的子窗口句柄"""
        try:
            parent_hwnd = int(self.winId())
            
            def enum_child_proc(hwnd, lparam):
                # 检查是否是Chrome子窗口
                class_name = ctypes.create_unicode_buffer(256)
                user32.GetClassNameW(hwnd, class_name, 256)
                if "Chrome" in class_name.value:
                    self.browser_hwnd = hwnd
                    print(f"找到Chrome子窗口: {hwnd}, 类名: {class_name.value}")
                    return False  # 停止枚举
                return True  # 继续枚举

            # 枚举子窗口
            enum_child_proc_type = ctypes.WINFUNCTYPE(ctypes.c_bool, wintypes.HWND, wintypes.LPARAM)
            user32.EnumChildWindows(parent_hwnd, enum_child_proc_type(enum_child_proc), 0)
            
        except Exception as e:
            print(f"查找浏览器子窗口失败: {e}")

    def resizeEvent(self, event):
        """窗口大小改变时调整浏览器大小"""
        super().resizeEvent(event)
        
        # 使用定时器延迟调整大小，避免频繁调用
        if self.resize_timer:
            self.resize_timer.stop()
        
        self.resize_timer = QTimer()
        self.resize_timer.setSingleShot(True)
        self.resize_timer.timeout.connect(self._do_resize)
        self.resize_timer.start(50)  # 减少延迟到50ms

    def _do_resize(self):
        """实际执行浏览器大小调整"""
        if self.browser:
            try:
                self._force_browser_resize()
            except Exception as e:
                print(f"调整浏览器大小失败: {e}")

    def _force_browser_resize(self):
        """强制调整浏览器大小 - 多种方法组合"""
        if not self.browser:
            return

        try:
            # 获取Windows客户区的精确尺寸（考虑DPI缩放）
            hwnd = int(self.winId())
            client_rect = ctypes.wintypes.RECT()
            user32.GetClientRect(hwnd, ctypes.byref(client_rect))

            new_width = client_rect.right - client_rect.left
            new_height = client_rect.bottom - client_rect.top

            print(f"强制调整浏览器大小到: {new_width}x{new_height} (Windows API)")

            # 方法1: 调用CEF的标准方法
            self.browser.WasResized()
            
            # 方法2: 如果有浏览器窗口句柄，直接调整Windows窗口大小
            if self.browser_hwnd:
                try:
                    # 使用SetWindowPos调整子窗口大小
                    result = user32.SetWindowPos(
                        self.browser_hwnd,  # 窗口句柄
                        0,                  # 插入位置
                        0,                  # X坐标
                        0,                  # Y坐标
                        new_width,          # 宽度
                        new_height,         # 高度
                        SWP_NOZORDER | SWP_NOACTIVATE  # 标志
                    )
                    if result:
                        print(f"Windows API调整窗口大小成功: {new_width}x{new_height}")
                    else:
                        print("Windows API调整窗口大小失败")

                    # 额外强制刷新窗口
                    user32.InvalidateRect(self.browser_hwnd, None, True)
                    user32.UpdateWindow(self.browser_hwnd)

                except Exception as e:
                    print(f"Windows API调整窗口大小异常: {e}")
            else:
                # 如果还没有窗口句柄，尝试重新获取
                self._get_browser_hwnd()

            # 方法3: 强制重绘
            self.update()
            self.repaint()
            QApplication.processEvents()

            # 方法4: 延迟再次调用WasResized
            QTimer.singleShot(100, lambda: self._delayed_resize())

        except Exception as e:
            print(f"强制调整浏览器大小异常: {e}")

    def _delayed_resize(self):
        """延迟调用WasResized"""
        try:
            if self.browser:
                self.browser.WasResized()
                print("延迟调用WasResized完成")
        except Exception as e:
            print(f"延迟调用WasResized失败: {e}")

    def closeEvent(self, event):
        """关闭时清理浏览器"""
        try:
            if self.resize_timer:
                self.resize_timer.stop()
                self.resize_timer = None
            
            if self.browser:
                self.browser.CloseBrowser(True)
                self.browser = None
                
            self.browser_hwnd = None
            print("CEF浏览器已清理")
        except Exception as e:
            print(f"关闭浏览器失败: {e}")
        super().closeEvent(event)


class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("CEF窗口大小适配修复测试")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)  # 移除边距
        layout.setSpacing(0)  # 移除间距
        
        # 创建CEF widget
        self.cef_widget = CEFWidget()
        layout.addWidget(self.cef_widget)
        
        # 延迟加载页面，确保窗口完全初始化
        QTimer.singleShot(1000, self.load_page)

    def load_page(self):
        """延迟加载页面"""
        print("开始加载页面...")
        self.cef_widget.embed_browser("https://www.baidu.com")

    def resizeEvent(self, event):
        """主窗口大小改变时的处理"""
        super().resizeEvent(event)
        print(f"主窗口大小改变: {self.size().width()}x{self.size().height()}")


def main():
    print("开始初始化CEF（窗口大小适配修复版本）...")
    
    # 初始化CEF
    sys.excepthook = cef.ExceptHook
    
    # CEF设置
    settings = {
        "debug": False,
        "log_severity": cef.LOGSEVERITY_ERROR,
        "log_file": "",
        "multi_threaded_message_loop": False,
        "command_line_args_disabled": False,
    }
    
    # 命令行参数（保持之前的GPU禁用设置）
    command_line_args = [
        "--disable-gpu",
        "--disable-gpu-compositing", 
        "--disable-gpu-sandbox",
        "--force-device-scale-factor=1",
        "--high-dpi-support=0",
        "--no-sandbox",
    ]
    
    # 应用命令行参数
    if hasattr(cef, 'g_commandLineSwitches'):
        for arg in command_line_args:
            if '=' in arg:
                key, value = arg.split('=', 1)
                cef.g_commandLineSwitches[key.lstrip('-')] = value
            else:
                cef.g_commandLineSwitches[arg.lstrip('-')] = ""
    
    # 初始化CEF
    cef.Initialize(settings)
    print("CEF初始化完成")
    
    # 创建Qt应用
    app = QApplication(sys.argv)
    print("Qt应用创建完成")
    
    # 创建测试窗口
    window = TestWindow()
    window.show()
    print("测试窗口显示完成")
    
    # CEF消息循环定时器
    cef_timer = QTimer()
    cef_timer.timeout.connect(lambda: cef.MessageLoopWork())
    cef_timer.start(10)
    print("CEF消息循环启动")
    
    # 运行应用
    print("开始运行应用...")
    app.exec_()
    
    # 清理CEF
    print("清理CEF...")
    cef.Shutdown()


if __name__ == "__main__":
    main()
